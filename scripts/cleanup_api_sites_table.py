#!/usr/bin/env python3
"""
Cleanup Script: Remove api_sites table
Safely removes the api_sites table after successful migration to marketplaces
"""

import sqlite3
from datetime import datetime
import shutil

def backup_database():
    """Create a backup of the current database"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_path = f'migrations/backups/pazaryeri_backup_cleanup_{timestamp}.db'
    shutil.copy('pazaryeri.db', backup_path)
    print(f"✅ Database backed up to: {backup_path}")
    return backup_path

def verify_marketplaces_data(conn):
    """Verify that marketplaces table has all the data"""
    cursor = conn.cursor()
    
    print("🔍 Verifying marketplaces data...")
    
    # Check counts
    cursor.execute("SELECT COUNT(*) FROM api_sites")
    api_sites_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM marketplaces WHERE is_deleted = 0")
    marketplaces_count = cursor.fetchone()[0]
    
    print(f"  📊 api_sites records: {api_sites_count}")
    print(f"  📊 marketplaces records: {marketplaces_count}")
    
    if marketplaces_count >= api_sites_count:
        print("  ✅ Marketplaces data verification successful!")
        return True
    else:
        print("  ❌ Marketplaces data verification failed!")
        return False

def remove_api_sites_table(conn):
    """Remove the api_sites table"""
    cursor = conn.cursor()
    
    print("🗑️  Removing api_sites table...")
    
    # Check if table exists
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='api_sites'")
    if not cursor.fetchone():
        print("  ℹ️  api_sites table does not exist")
        return True
    
    try:
        cursor.execute("DROP TABLE api_sites")
        print("  ✅ api_sites table removed successfully")
        return True
    except sqlite3.Error as e:
        print(f"  ❌ Error removing api_sites table: {e}")
        return False

def update_schema_script(conn):
    """Remove api_sites references from schema enhancement script"""
    try:
        # Read the current schema enhancement script
        with open('scripts/enhance_sqlite_schema.py', 'r') as f:
            content = f.read()
        
        # Remove api_sites from the tables list
        old_line = "'users', 'warehouses', 'suppliers', 'product_images', \n        'api_sites', 'marketplace_products', 'categories'"
        new_line = "'users', 'warehouses', 'suppliers', 'product_images', \n        'marketplace_products', 'categories'"
        
        if old_line in content:
            content = content.replace(old_line, new_line)
            
            with open('scripts/enhance_sqlite_schema.py', 'w') as f:
                f.write(content)
            
            print("  ✅ Updated schema enhancement script")
        else:
            print("  ℹ️  Schema enhancement script already updated")
        
        return True
    except Exception as e:
        print(f"  ⚠️  Could not update schema enhancement script: {e}")
        return False

def main():
    """Main cleanup function"""
    print("🧹 Starting api_sites Cleanup")
    print("=" * 40)
    
    # Create backup
    backup_path = backup_database()
    
    # Connect to database
    conn = sqlite3.connect('pazaryeri.db')
    
    try:
        # Step 1: Verify marketplaces data
        if not verify_marketplaces_data(conn):
            print("\n❌ Cleanup aborted - marketplaces data verification failed")
            return
        
        # Step 2: Remove api_sites table
        if not remove_api_sites_table(conn):
            print("\n❌ Cleanup failed - could not remove api_sites table")
            conn.rollback()
            return
        
        # Step 3: Update schema script
        update_schema_script(conn)
        
        # Commit changes
        conn.commit()
        
        print("\n" + "=" * 40)
        print("✅ api_sites Cleanup Completed Successfully!")
        print(f"📁 Backup created at: {backup_path}")
        
        # Show final summary
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM marketplaces WHERE is_deleted = 0")
        final_count = cursor.fetchone()[0]
        print(f"📊 Active marketplaces: {final_count}")
        
        # List remaining tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"📋 Remaining tables: {len(tables)}")
        
        if 'api_sites' not in tables:
            print("  ✅ api_sites table successfully removed")
        else:
            print("  ⚠️  api_sites table still exists")
            
    except Exception as e:
        print(f"\n❌ Cleanup failed: {e}")
        conn.rollback()
        print(f"🔄 Database can be restored from: {backup_path}")
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    main()