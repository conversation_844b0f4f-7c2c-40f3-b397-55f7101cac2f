#!/usr/bin/env python3
"""
Migration script to convert old suppliers table to new structure
Old: suppliers(product_id, supplier_link)
New: suppliers(name, website, description) + product_suppliers(product_id, supplier_id, supplier_product_url, supplier_price)
"""

import sqlite3
import re
from urllib.parse import urlparse
from datetime import datetime

def extract_supplier_name(url):
    """Extract supplier name from URL"""
    domain = urlparse(url).netloc
    
    # Known supplier mappings
    mappings = {
        'hepsiburada.com': 'Hepsiburada',
        'trendyol.com': 'Trendyol',
        'n11.com': 'N11',
        'amazon.com.tr': 'Amazon TR',
        'teknosa.com': 'Teknosa',
        'mediamarkt.com.tr': 'MediaMarkt',
        'vatanbilgisayar.com': 'Vatan Bilgisayar',
        'gittigidiyor.com': 'GittiGidiyor',
        'ciceksepeti.com': 'Çiçeksepeti',
        'morhipo.com': '<PERSON>rhip<PERSON>'
    }
    
    for domain_key, name in mappings.items():
        if domain_key in domain:
            return name
    
    # If not found, use domain name
    return domain.replace('www.', '').split('.')[0].title()

def migrate_database():
    """Perform the migration"""
    conn = sqlite3.connect('pazaryeri.db')
    cursor = conn.cursor()
    
    print("Starting supplier table migration...")
    
    # Check if old suppliers table exists
    cursor.execute("""
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='suppliers'
    """)
    
    if not cursor.fetchone():
        print("No suppliers table found. Nothing to migrate.")
        return
    
    # Check if product_suppliers table already exists
    cursor.execute("""
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='product_suppliers'
    """)
    
    if cursor.fetchone():
        print("product_suppliers table already exists. Migration may have been completed.")
        return
    
    # Get all existing supplier links
    cursor.execute("SELECT product_id, supplier_link FROM suppliers")
    old_data = cursor.fetchall()
    
    print(f"Found {len(old_data)} supplier links to migrate")
    
    # Rename old suppliers table
    cursor.execute("ALTER TABLE suppliers RENAME TO suppliers_old")
    
    # Create new suppliers table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS suppliers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        website TEXT,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # Create product_suppliers table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS product_suppliers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        supplier_id INTEGER NOT NULL,
        supplier_product_url TEXT,
        supplier_price REAL,
        last_checked TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE,
        UNIQUE(product_id, supplier_id)
    )
    ''')
    
    # Extract unique suppliers and insert them
    suppliers_map = {}
    for product_id, supplier_link in old_data:
        supplier_name = extract_supplier_name(supplier_link)
        
        if supplier_name not in suppliers_map:
            # Get base URL for website
            parsed_url = urlparse(supplier_link)
            website = f"{parsed_url.scheme}://{parsed_url.netloc}"
            
            # Insert supplier
            cursor.execute('''
                INSERT OR IGNORE INTO suppliers (name, website, description)
                VALUES (?, ?, ?)
            ''', (supplier_name, website, f"Migrated from {website}"))
            
            # Get the ID
            cursor.execute("SELECT id FROM suppliers WHERE name = ?", (supplier_name,))
            suppliers_map[supplier_name] = cursor.fetchone()[0]
            print(f"Added supplier: {supplier_name}")
    
    # Insert product-supplier relationships
    for product_id, supplier_link in old_data:
        supplier_name = extract_supplier_name(supplier_link)
        supplier_id = suppliers_map[supplier_name]
        
        cursor.execute('''
            INSERT OR IGNORE INTO product_suppliers 
            (product_id, supplier_id, supplier_product_url, supplier_price)
            VALUES (?, ?, ?, NULL)
        ''', (product_id, supplier_id, supplier_link))
    
    print(f"Migrated {len(old_data)} supplier links")
    print(f"Created {len(suppliers_map)} supplier companies")
    
    # Drop the old table
    cursor.execute("DROP TABLE suppliers_old")
    
    conn.commit()
    conn.close()
    
    print("Migration completed successfully!")

if __name__ == "__main__":
    migrate_database()