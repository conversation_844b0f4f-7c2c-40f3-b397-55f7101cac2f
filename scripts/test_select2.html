<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <title>Select2 Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
</head>
<body>
    <div class="container mt-5">
        <h1>Select2 Test</h1>
        <select class="form-select" id="testSelect" style="width: 100%">
            <option value="">Ürün seçin...</option>
            <option value="1" data-sku="SKU001" data-brand="Apple" data-barcode="123456789">iPhone 15 Pro Max</option>
            <option value="2" data-sku="SKU002" data-brand="Samsung" data-barcode="987654321">Galaxy S24 Ultra</option>
            <option value="3" data-sku="SKU003" data-brand="Xiaomi" data-barcode="456789123">Redmi Note 13 Pro</option>
        </select>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
    $(document).ready(function() {
        $('#testSelect').select2({
            theme: 'bootstrap-5',
            width: '100%',
            allowClear: true,
            placeholder: 'Ürün adı, SKU veya barkod ile arayın...',
            language: {
                searching: function() { return "Aranıyor..."; },
                noResults: function() { return "Sonuç bulunamadı"; }
            }
        });
    });
    </script>
</body>
</html>