#!/usr/bin/env python3
"""
SQLite Schema Enhancement Script
Applies PostgreSQL schema improvements to existing SQLite database
Adds soft delete fields, new tables, and enhanced columns
"""

import sqlite3
from datetime import datetime
import json

def backup_database():
    """Create a backup of the current database"""
    import shutil
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_path = f'migrations/backups/pazaryeri_backup_{timestamp}.db'
    shutil.copy('pazaryeri.db', backup_path)
    print(f"✅ Database backed up to: {backup_path}")
    return backup_path

def enhance_products_table(conn):
    """Add missing fields to products table"""
    cursor = conn.cursor()
    
    print("🔄 Enhancing products table...")
    
    # Get current columns
    cursor.execute("PRAGMA table_info(products)")
    existing_columns = {col[1] for col in cursor.fetchall()}
    
    # Add new columns if they don't exist
    new_columns = [
        ("description", "TEXT"),
        ("created_at", "TIMESTAMP"),
        ("updated_at", "TIMESTAMP"),
        ("is_deleted", "BOOLEAN DEFAULT 0"),
        ("deleted_at", "TIMESTAMP NULL")
    ]
    
    for col_name, col_type in new_columns:
        if col_name not in existing_columns:
            try:
                cursor.execute(f"ALTER TABLE products ADD COLUMN {col_name} {col_type}")
                print(f"  ✅ Added column: {col_name}")
            except sqlite3.Error as e:
                print(f"  ⚠️  Column {col_name} might already exist: {e}")
    
    # Update stock_multiplier_main_product to BOOLEAN type behavior
    cursor.execute("UPDATE products SET stock_multiplier_main_product = 0 WHERE stock_multiplier_main_product IS NULL OR stock_multiplier_main_product = ''")
    
    # Set created_at and updated_at for existing records (only if columns exist)
    cursor.execute("PRAGMA table_info(products)")
    current_columns = {col[1] for col in cursor.fetchall()}
    
    if 'created_at' in current_columns:
        cursor.execute("UPDATE products SET created_at = CURRENT_TIMESTAMP WHERE created_at IS NULL")
    if 'updated_at' in current_columns:
        cursor.execute("UPDATE products SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL")
    if 'is_deleted' in current_columns:
        cursor.execute("UPDATE products SET is_deleted = 0 WHERE is_deleted IS NULL")
    
    print("  ✅ Products table enhanced")

def add_soft_delete_to_existing_tables(conn):
    """Add soft delete fields to all existing tables that don't have them"""
    cursor = conn.cursor()
    
    print("🔄 Adding soft delete fields to existing tables...")
    
    # Tables that should have soft delete
    tables_to_update = [
        'users', 'warehouses', 'suppliers', 'product_images', 
        'marketplace_products', 'categories'
    ]
    
    for table_name in tables_to_update:
        # Check if table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
        if not cursor.fetchone():
            continue
            
        # Get current columns
        cursor.execute(f"PRAGMA table_info({table_name})")
        existing_columns = {col[1] for col in cursor.fetchall()}
        
        # Add soft delete columns
        soft_delete_columns = [
            ("is_deleted", "BOOLEAN DEFAULT 0"),
            ("deleted_at", "TIMESTAMP NULL")
        ]
        
        for col_name, col_type in soft_delete_columns:
            if col_name not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {col_name} {col_type}")
                    print(f"  ✅ Added {col_name} to {table_name}")
                except sqlite3.Error as e:
                    print(f"  ⚠️  Error adding {col_name} to {table_name}: {e}")
        
        # Set default values for existing records
        cursor.execute(f"UPDATE {table_name} SET is_deleted = 0 WHERE is_deleted IS NULL")
    
    print("  ✅ Soft delete fields added to existing tables")

def create_marketplaces_table(conn):
    """Create marketplaces table"""
    cursor = conn.cursor()
    
    print("🔄 Creating marketplaces table...")
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS marketplaces (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(50) NOT NULL,
        api_key VARCHAR(255),
        api_secret VARCHAR(255),
        region VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_deleted BOOLEAN DEFAULT 0,
        deleted_at TIMESTAMP NULL
    )
    ''')
    
    # Insert default marketplaces
    default_marketplaces = [
        ('Amazon', None, None, 'TR'),
        ('Ozon', None, None, 'RU'),
        ('Hepsiburada', None, None, 'TR'),
        ('Trendyol', None, None, 'TR'),
        ('N11', None, None, 'TR')
    ]
    
    for marketplace in default_marketplaces:
        cursor.execute('''
            INSERT OR IGNORE INTO marketplaces (name, api_key, api_secret, region)
            VALUES (?, ?, ?, ?)
        ''', marketplace)
    
    print("  ✅ Marketplaces table created with default data")

def create_marketplace_categories_table(conn):
    """Create marketplace_categories table"""
    cursor = conn.cursor()
    
    print("🔄 Creating marketplace_categories table...")
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS marketplace_categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        marketplace_id INTEGER NOT NULL,
        marketplace_category_id VARCHAR(100) NOT NULL,
        name VARCHAR(255) NOT NULL,
        parent_marketplace_category_id VARCHAR(100),
        category_path VARCHAR(500),
        level INTEGER NOT NULL DEFAULT 0,
        is_leaf BOOLEAN NOT NULL DEFAULT 0,
        attributes_schema TEXT, -- JSON string for SQLite
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_deleted BOOLEAN DEFAULT 0,
        deleted_at TIMESTAMP NULL,
        FOREIGN KEY (marketplace_id) REFERENCES marketplaces(id)
    )
    ''')
    
    print("  ✅ Marketplace categories table created")

def create_category_mappings_table(conn):
    """Create category_mappings table"""
    cursor = conn.cursor()
    
    print("🔄 Creating category_mappings table...")
    
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS category_mappings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        category_id INTEGER,
        marketplace_id INTEGER,
        marketplace_category_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_deleted BOOLEAN DEFAULT 0,
        deleted_at TIMESTAMP NULL,
        FOREIGN KEY (category_id) REFERENCES categories(id),
        FOREIGN KEY (marketplace_id) REFERENCES marketplaces(id),
        FOREIGN KEY (marketplace_category_id) REFERENCES marketplace_categories(id)
    )
    ''')
    
    print("  ✅ Category mappings table created")

def enhance_marketplace_products_table(conn):
    """Enhance existing marketplace_products table"""
    cursor = conn.cursor()
    
    print("🔄 Enhancing marketplace_products table...")
    
    # Get current columns
    cursor.execute("PRAGMA table_info(marketplace_products)")
    existing_columns = {col[1] for col in cursor.fetchall()}
    
    # Add missing columns
    new_columns = [
        ("product_id", "INTEGER REFERENCES products(id)"),
        ("is_deleted", "BOOLEAN DEFAULT 0"),
        ("deleted_at", "TIMESTAMP NULL")
    ]
    
    for col_name, col_type in new_columns:
        if col_name not in existing_columns:
            try:
                cursor.execute(f"ALTER TABLE marketplace_products ADD COLUMN {col_name} {col_type}")
                print(f"  ✅ Added column: {col_name}")
            except sqlite3.Error as e:
                print(f"  ⚠️  Column {col_name} might already exist: {e}")
    
    # Set default values
    cursor.execute("UPDATE marketplace_products SET is_deleted = 0 WHERE is_deleted IS NULL")
    
    print("  ✅ Marketplace products table enhanced")

def enhance_categories_table(conn):
    """Enhance existing categories table to match new schema"""
    cursor = conn.cursor()
    
    print("🔄 Enhancing categories table...")
    
    # Get current structure
    cursor.execute("PRAGMA table_info(categories)")
    existing_columns = {col[1] for col in cursor.fetchall()}
    
    # Check if this is the old categories table (marketplace-specific)
    if 'marketplace_id' in existing_columns:
        # This is the old table, rename it
        cursor.execute("ALTER TABLE categories RENAME TO old_marketplace_categories")
        print("  ✅ Renamed old categories table to old_marketplace_categories")
        
        # Create new general categories table
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            parent_id INTEGER REFERENCES categories(id),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_deleted BOOLEAN DEFAULT 0,
            deleted_at TIMESTAMP NULL
        )
        ''')
        
        # Insert some default categories
        default_categories = [
            ('Elektronik', None),
            ('Telefon & Aksesuar', 1),
            ('Bilgisayar & Tablet', 1),
            ('Ev & Yaşam', None),
            ('Moda', None),
            ('Kitap & Dergi', None)
        ]
        
        for cat_name, parent_id in default_categories:
            cursor.execute('''
                INSERT OR IGNORE INTO categories (name, parent_id)
                VALUES (?, ?)
            ''', (cat_name, parent_id))
        
        print("  ✅ Created new general categories table with default data")
    else:
        # Just add missing columns to existing categories table
        new_columns = [
            ("updated_at", "TIMESTAMP"),
            ("is_deleted", "BOOLEAN DEFAULT 0"),
            ("deleted_at", "TIMESTAMP NULL")
        ]
        
        for col_name, col_type in new_columns:
            if col_name not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE categories ADD COLUMN {col_name} {col_type}")
                    print(f"  ✅ Added column: {col_name}")
                except sqlite3.Error as e:
                    print(f"  ⚠️  Column {col_name} might already exist: {e}")
        
        # Set default values
        cursor.execute("UPDATE categories SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL")
        cursor.execute("UPDATE categories SET is_deleted = 0 WHERE is_deleted IS NULL")

def create_indexes(conn):
    """Create performance indexes"""
    cursor = conn.cursor()
    
    print("🔄 Creating performance indexes...")
    
    indexes = [
        ("idx_products_sku", "CREATE UNIQUE INDEX IF NOT EXISTS idx_products_sku ON products(sku)"),
        ("idx_products_is_deleted", "CREATE INDEX IF NOT EXISTS idx_products_is_deleted ON products(is_deleted)"),
        ("idx_marketplace_products_is_deleted", "CREATE INDEX IF NOT EXISTS idx_marketplace_products_is_deleted ON marketplace_products(is_deleted)"),
        ("idx_suppliers_is_deleted", "CREATE INDEX IF NOT EXISTS idx_suppliers_is_deleted ON suppliers(is_deleted)"),
        ("idx_categories_is_deleted", "CREATE INDEX IF NOT EXISTS idx_categories_is_deleted ON categories(is_deleted)"),
        ("idx_marketplace_categories_marketplace", "CREATE INDEX IF NOT EXISTS idx_marketplace_categories_marketplace ON marketplace_categories(marketplace_id)"),
        ("idx_category_mappings_category", "CREATE INDEX IF NOT EXISTS idx_category_mappings_category ON category_mappings(category_id, marketplace_id)")
    ]
    
    for idx_name, idx_sql in indexes:
        try:
            cursor.execute(idx_sql)
            print(f"  ✅ Created index: {idx_name}")
        except sqlite3.Error as e:
            print(f"  ⚠️  Index {idx_name} might already exist: {e}")

def main():
    """Main migration function"""
    print("🚀 Starting SQLite Schema Enhancement Migration")
    print("=" * 50)
    
    # Create backup
    backup_path = backup_database()
    
    # Connect to database
    conn = sqlite3.connect('pazaryeri.db')
    conn.execute("PRAGMA foreign_keys = ON")  # Enable foreign key constraints
    
    try:
        # Step 1: Enhance products table
        enhance_products_table(conn)
        
        # Step 2: Add soft delete to existing tables
        add_soft_delete_to_existing_tables(conn)
        
        # Step 3: Create new tables
        create_marketplaces_table(conn)
        create_marketplace_categories_table(conn)
        create_category_mappings_table(conn)
        
        # Step 4: Enhance existing tables
        enhance_marketplace_products_table(conn)
        enhance_categories_table(conn)
        
        # Step 5: Create indexes
        create_indexes(conn)
        
        # Commit all changes
        conn.commit()
        
        print("\n" + "=" * 50)
        print("✅ SQLite Schema Enhancement Completed Successfully!")
        print(f"📁 Backup created at: {backup_path}")
        print("\n📊 Schema Summary:")
        
        # Show summary
        summary_cursor = conn.cursor()
        summary_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = summary_cursor.fetchall()
        print(f"  📋 Total tables: {len(tables)}")
        for table in tables:
            summary_cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
            count = summary_cursor.fetchone()[0]
            print(f"    - {table[0]}: {count} records")
        
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        conn.rollback()
        print(f"🔄 Database can be restored from: {backup_path}")
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    main()