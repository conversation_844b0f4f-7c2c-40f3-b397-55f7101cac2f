#!/usr/bin/env python3
"""
Add financial columns to orders and order_items tables
"""

import sqlite3
import json
from datetime import datetime

def add_financial_columns():
    """Add financial tracking columns to orders and order_items tables"""
    conn = sqlite3.connect('pazaryeri.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    print("Adding financial columns to orders and order_items tables...")
    
    try:
        # Check current schema
        cursor.execute("PRAGMA table_info(orders)")
        orders_columns = {col[1] for col in cursor.fetchall()}
        
        cursor.execute("PRAGMA table_info(order_items)")
        order_items_columns = {col[1] for col in cursor.fetchall()}
        
        # Add columns to orders table
        orders_new_columns = [
            ("original_amount", "DECIMAL(10,2)", "Original/list price total"),
            ("discount_amount", "DECIMAL(10,2)", "Total discount amount"),
            ("commission_amount", "DECIMAL(10,2)", "Total commission amount"),
            ("net_payout", "DECIMAL(10,2)", "Net payout after all deductions")
        ]
        
        for col_name, col_type, description in orders_new_columns:
            if col_name not in orders_columns:
                print(f"Adding {col_name} to orders table... ({description})")
                cursor.execute(f"ALTER TABLE orders ADD COLUMN {col_name} {col_type}")
                print(f"✅ Added {col_name}")
            else:
                print(f"⚠️  Column {col_name} already exists in orders table")
        
        # Add columns to order_items table
        order_items_new_columns = [
            ("original_price", "DECIMAL(10,2)", "Original/list price per unit"),
            ("discount_amount", "DECIMAL(10,2)", "Discount amount for this item"),
            ("discount_percent", "DECIMAL(5,2)", "Discount percentage"),
            ("commission_amount", "DECIMAL(10,2)", "Commission for this item"),
            ("commission_percent", "DECIMAL(5,2)", "Commission percentage"),
            ("net_payout", "DECIMAL(10,2)", "Net payout for this item")
        ]
        
        for col_name, col_type, description in order_items_new_columns:
            if col_name not in order_items_columns:
                print(f"Adding {col_name} to order_items table... ({description})")
                cursor.execute(f"ALTER TABLE order_items ADD COLUMN {col_name} {col_type}")
                print(f"✅ Added {col_name}")
            else:
                print(f"⚠️  Column {col_name} already exists in order_items table")
        
        conn.commit()
        print("\n✅ Financial columns added successfully!")
        
        # Now populate the financial data from existing marketplace_data
        print("\n🔄 Migrating existing financial data from marketplace_data JSON...")
        migrate_existing_financial_data(conn)
        
    except Exception as e:
        print(f"\n❌ Error adding columns: {e}")
        conn.rollback()
    finally:
        conn.close()

def migrate_existing_financial_data(conn):
    """Migrate financial data from marketplace_data JSON to new columns"""
    cursor = conn.cursor()
    
    try:
        # Get all orders with marketplace_data
        cursor.execute("""
            SELECT id, marketplace_data 
            FROM orders 
            WHERE marketplace_data IS NOT NULL AND marketplace_data != ''
        """)
        
        orders = cursor.fetchall()
        updated_orders = 0
        updated_items = 0
        
        for order in orders:
            order_id = order['id']
            try:
                marketplace_data = json.loads(order['marketplace_data'])
                financial_data = marketplace_data.get('financial_data', {})
                
                if financial_data and 'products' in financial_data:
                    # Calculate order totals
                    total_original = 0
                    total_discount = 0
                    total_commission = 0
                    total_payout = 0
                    
                    # Process each product in financial data
                    financial_products = financial_data.get('products', [])
                    
                    for fp in financial_products:
                        # Calculate totals
                        quantity = fp.get('quantity', 1)
                        old_price = float(fp.get('old_price', 0)) * quantity
                        discount = float(fp.get('total_discount_value', 0))
                        commission = float(fp.get('commission_amount', 0))
                        payout = float(fp.get('payout', 0))
                        
                        total_original += old_price
                        total_discount += discount
                        total_commission += commission
                        total_payout += payout
                        
                        # Update matching order items
                        product_id = str(fp.get('product_id', ''))
                        sku = str(fp.get('sku', ''))
                        
                        if product_id or sku:
                            # Find matching order item
                            if product_id:
                                cursor.execute("""
                                    UPDATE order_items 
                                    SET original_price = ?,
                                        discount_amount = ?,
                                        discount_percent = ?,
                                        commission_amount = ?,
                                        commission_percent = ?,
                                        net_payout = ?
                                    WHERE order_id = ? AND (sku = ? OR sku = ?)
                                """, (
                                    fp.get('old_price', 0),
                                    fp.get('total_discount_value', 0),
                                    fp.get('total_discount_percent', 0),
                                    fp.get('commission_amount', 0),
                                    fp.get('commission_percent', 0),
                                    fp.get('payout', 0),
                                    order_id,
                                    product_id,
                                    sku
                                ))
                                
                                if cursor.rowcount > 0:
                                    updated_items += cursor.rowcount
                    
                    # Update order totals
                    cursor.execute("""
                        UPDATE orders 
                        SET original_amount = ?,
                            discount_amount = ?,
                            commission_amount = ?,
                            net_payout = ?
                        WHERE id = ?
                    """, (total_original, total_discount, total_commission, total_payout, order_id))
                    
                    if cursor.rowcount > 0:
                        updated_orders += 1
                        
            except json.JSONDecodeError:
                print(f"⚠️  Could not parse marketplace_data for order {order_id}")
            except Exception as e:
                print(f"⚠️  Error processing order {order_id}: {e}")
        
        conn.commit()
        print(f"\n✅ Migration completed!")
        print(f"   - Updated {updated_orders} orders with financial totals")
        print(f"   - Updated {updated_items} order items with financial details")
        
    except Exception as e:
        print(f"\n❌ Error during migration: {e}")
        conn.rollback()

def verify_migration():
    """Verify the migration was successful"""
    conn = sqlite3.connect('pazaryeri.db')
    cursor = conn.cursor()
    
    print("\n🔍 Verifying migration...")
    
    # Check orders with financial data
    cursor.execute("""
        SELECT COUNT(*) as total,
               COUNT(CASE WHEN net_payout IS NOT NULL AND net_payout > 0 THEN 1 END) as with_payout
        FROM orders
    """)
    result = cursor.fetchone()
    print(f"   - Total orders: {result[0]}")
    print(f"   - Orders with financial data: {result[1]}")
    
    # Check order items with financial data
    cursor.execute("""
        SELECT COUNT(*) as total,
               COUNT(CASE WHEN commission_amount IS NOT NULL THEN 1 END) as with_commission
        FROM order_items
    """)
    result = cursor.fetchone()
    print(f"   - Total order items: {result[0]}")
    print(f"   - Items with commission data: {result[1]}")
    
    # Show sample data
    cursor.execute("""
        SELECT marketplace_order_id, total_amount, discount_amount, 
               commission_amount, net_payout
        FROM orders
        WHERE net_payout IS NOT NULL AND net_payout > 0
        LIMIT 3
    """)
    
    samples = cursor.fetchall()
    if samples:
        print("\n📊 Sample migrated data:")
        for sample in samples:
            print(f"   Order {sample[0]}:")
            print(f"     - Total: {sample[1]}")
            print(f"     - Discount: {sample[2]}")
            print(f"     - Commission: {sample[3]}")
            print(f"     - Net Payout: {sample[4]}")
    
    conn.close()

if __name__ == '__main__':
    print("🚀 Starting financial columns migration...")
    print("=" * 50)
    
    add_financial_columns()
    verify_migration()
    
    print("\n✅ Migration script completed!")