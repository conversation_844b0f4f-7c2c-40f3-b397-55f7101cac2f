#!/usr/bin/env python3
"""
Add price tracking columns to products table
"""

import sqlite3
from datetime import datetime

def add_price_tracking_columns():
    """Add min_price, max_price, avg_price and price_updated_at columns to products table"""
    conn = sqlite3.connect('pazaryeri.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    print("Adding price tracking columns to products table...")
    
    try:
        # Check current schema
        cursor.execute("PRAGMA table_info(products)")
        existing_columns = {col[1] for col in cursor.fetchall()}
        
        # Define new columns to add
        new_columns = [
            ("min_price", "DECIMAL(10,2)", "Minimum price across all sources"),
            ("max_price", "DECIMAL(10,2)", "Maximum price across all sources"),
            ("avg_price", "DECIMAL(10,2)", "Average price across all sources"),
            ("price_updated_at", "TIMESTAMP", "When price fields were last updated")
        ]
        
        # Add each column if it doesn't exist
        for col_name, col_type, description in new_columns:
            if col_name not in existing_columns:
                print(f"Adding {col_name} to products table... ({description})")
                cursor.execute(f"ALTER TABLE products ADD COLUMN {col_name} {col_type}")
                print(f"✅ Added {col_name}")
            else:
                print(f"⚠️  Column {col_name} already exists in products table")
        
        # Commit changes
        conn.commit()
        print("\n✅ Price tracking columns added successfully!")
        
        # Show sample of how to calculate prices (for reference)
        print("\n📊 Sample query to calculate prices from suppliers:")
        print("""
        SELECT 
            p.id,
            p.sku,
            p.product_name,
            MIN(ps.supplier_price) as min_supplier_price,
            MAX(ps.supplier_price) as max_supplier_price,
            AVG(ps.supplier_price) as avg_supplier_price,
            COUNT(ps.supplier_price) as price_count
        FROM products p
        LEFT JOIN product_suppliers ps ON p.id = ps.product_id
        WHERE ps.supplier_price IS NOT NULL AND ps.supplier_price > 0
        GROUP BY p.id
        LIMIT 5
        """)
        
        # Execute sample query
        cursor.execute("""
        SELECT 
            p.id,
            p.sku,
            p.product_name,
            MIN(ps.supplier_price) as min_supplier_price,
            MAX(ps.supplier_price) as max_supplier_price,
            AVG(ps.supplier_price) as avg_supplier_price,
            COUNT(ps.supplier_price) as price_count
        FROM products p
        LEFT JOIN product_suppliers ps ON p.id = ps.product_id
        WHERE ps.supplier_price IS NOT NULL AND ps.supplier_price > 0
        GROUP BY p.id
        LIMIT 5
        """)
        
        results = cursor.fetchall()
        if results:
            print("\nSample results:")
            for row in results:
                print(f"Product: {row['sku']} - {row['product_name']}")
                print(f"  Price Count: {row['price_count']}")
                avg_price = row['avg_supplier_price'] if row['avg_supplier_price'] else 0
                print(f"  Min: {row['min_supplier_price']}, Max: {row['max_supplier_price']}, Avg: {avg_price:.2f if avg_price else 0}")
                print()
        else:
            print("\nNo products with supplier prices found yet.")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        conn.rollback()
        return False
    finally:
        conn.close()
    
    return True

if __name__ == '__main__':
    success = add_price_tracking_columns()
    if success:
        print("\n🎉 Migration completed successfully!")
        print("\nNext steps:")
        print("1. Implement a service to periodically update these price fields")
        print("2. The service should:")
        print("   - Calculate prices from product_suppliers table")
        print("   - Calculate prices from marketplace_products (via product_mappings)")
        print("   - Update min_price, max_price, avg_price, and price_updated_at")
    else:
        print("\n❌ Migration failed!")