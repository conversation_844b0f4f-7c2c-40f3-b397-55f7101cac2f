#!/usr/bin/env python3
"""
Extract brand names from Ozon product titles and update the database
"""

import sqlite3
import re

def extract_brand_from_title(title):
    """Extract brand name from product title"""
    # Common patterns for brand extraction
    # Usually the brand is the first word in the title
    
    # Known brands that appear in the imported products
    known_brands = [
        'Samsung', 'WMF', 'Silit', 'Zwilling', 'Apple', 'Sony', 'LG', 'Philips',
        'Bosch', 'Tefal', 'Braun', 'Panasonic', 'HP', 'Dell', 'Lenovo', 'Asus'
    ]
    
    # Check if any known brand is in the title
    title_lower = title.lower()
    for brand in known_brands:
        if brand.lower() in title_lower:
            return brand
    
    # If no known brand found, try to extract first word
    # (often the brand is the first word)
    words = title.split()
    if words:
        first_word = words[0]
        # Check if it looks like a brand (starts with capital, not a common word)
        if first_word[0].isupper() and len(first_word) > 2:
            return first_word
    
    return None

def update_ozon_brands():
    """Update brand information for Ozon products"""
    conn = sqlite3.connect('pazaryeri.db')
    cursor = conn.cursor()
    
    # Get Ozon marketplace ID
    cursor.execute("SELECT id FROM marketplaces WHERE name = 'Ozon'")
    ozon_id = cursor.fetchone()[0]
    
    # Get all Ozon products without brand
    cursor.execute("""
        SELECT id, title 
        FROM marketplace_products 
        WHERE marketplace_id = ? AND (brand IS NULL OR brand = '')
    """, (ozon_id,))
    
    products = cursor.fetchall()
    print(f"Found {len(products)} Ozon products without brand information")
    
    updated_count = 0
    for product_id, title in products:
        brand = extract_brand_from_title(title)
        if brand:
            cursor.execute("""
                UPDATE marketplace_products 
                SET brand = ? 
                WHERE id = ?
            """, (brand, product_id))
            updated_count += 1
            print(f"Updated product {product_id}: {title[:50]}... -> Brand: {brand}")
    
    conn.commit()
    print(f"\nSuccessfully updated {updated_count} products with brand information")
    
    # Show sample of updated products
    cursor.execute("""
        SELECT id, title, brand, price 
        FROM marketplace_products 
        WHERE marketplace_id = ? 
        LIMIT 10
    """, (ozon_id,))
    
    print("\nSample of Ozon products:")
    print("-" * 80)
    for row in cursor.fetchall():
        print(f"ID: {row[0]}, Brand: {row[2] or 'N/A':15} Price: {row[3]:8.2f} Title: {row[1][:40]}...")
    
    conn.close()

if __name__ == "__main__":
    update_ozon_brands()