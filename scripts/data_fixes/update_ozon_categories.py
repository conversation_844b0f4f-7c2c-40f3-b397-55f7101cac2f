#!/usr/bin/env python3
"""
Assign categories to Ozon products based on product names and existing marketplace categories
"""

import sqlite3

def find_best_category_match(title, brand, categories):
    """Find the best matching category for a product"""
    title_lower = title.lower()
    
    # Category mapping based on product types
    category_keywords = {
        'умные часы': ['часы', 'smart watch', 'galaxy watch'],
        'кухонные принадлежности': ['кофемолка', 'мельниц', 'овощечистка', 'шпатель', 'битер', 'мерный стакан', 'мерная ложка', 'воронка', 'термометр'],
        'сковороды и сотейники': ['сковорода', 'fusiontec'],
        'наборы для специй': ['специй', 'spice'],
        'кухонные инструменты': ['пресс для картофеля', 'картофеля'],
        'столовые приборы': ['atal b ak seti', 'столовые']
    }
    
    # First try to find exact category matches
    for category_name, keywords in category_keywords.items():
        for keyword in keywords:
            if keyword in title_lower:
                # Find the category in the database
                for cat in categories:
                    if category_name.lower() in cat['name'].lower():
                        return cat['marketplace_category_id'], cat['name']
    
    # If no match found, try to find by brand
    if brand:
        if brand.lower() == 'samsung':
            for cat in categories:
                if 'смарт-часы' in cat['name'].lower() or 'электроника' in cat['name'].lower():
                    return cat['marketplace_category_id'], cat['name']
        elif brand.lower() in ['wmf', 'silit', 'zwilling']:
            for cat in categories:
                if 'кухня' in cat['name'].lower() or 'посуда' in cat['name'].lower():
                    return cat['marketplace_category_id'], cat['name']
    
    # Default fallback - find a general category
    for cat in categories:
        if 'товары' in cat['name'].lower() and cat['level'] <= 1:
            return cat['marketplace_category_id'], cat['name']
    
    return None, None

def update_ozon_categories():
    """Update category information for Ozon products"""
    conn = sqlite3.connect('pazaryeri.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    # Get Ozon marketplace ID
    cursor.execute("SELECT id FROM marketplaces WHERE name = 'Ozon'")
    ozon_id = cursor.fetchone()[0]
    
    # Get all Ozon categories
    cursor.execute("""
        SELECT marketplace_category_id, name, level, category_path 
        FROM marketplace_categories 
        WHERE marketplace_id = ?
        ORDER BY level, name
    """, (ozon_id,))
    
    categories = [dict(row) for row in cursor.fetchall()]
    print(f"Found {len(categories)} Ozon categories")
    
    # Get some relevant categories for our products
    print("\nSample categories that might match our products:")
    for cat in categories:
        cat_name_lower = cat['name'].lower()
        if any(keyword in cat_name_lower for keyword in ['часы', 'кухня', 'посуда', 'электроника', 'специи', 'сковород']):
            print(f"  - {cat['name']} (ID: {cat['marketplace_category_id']}, Level: {cat['level']})")
    
    # Get all Ozon products without category
    cursor.execute("""
        SELECT id, title, brand
        FROM marketplace_products 
        WHERE marketplace_id = ? AND (category IS NULL OR category = '')
    """, (ozon_id,))
    
    products = cursor.fetchall()
    print(f"\nFound {len(products)} Ozon products without category information")
    
    # Update products with categories
    updated_count = 0
    category_assignments = {}
    
    for product in products:
        product_id = product['id']
        title = product['title']
        brand = product['brand']
        
        category_id, category_name = find_best_category_match(title, brand, categories)
        
        if category_name:
            cursor.execute("""
                UPDATE marketplace_products 
                SET category = ? 
                WHERE id = ?
            """, (category_name, product_id))
            updated_count += 1
            
            # Track assignments for summary
            if category_name not in category_assignments:
                category_assignments[category_name] = []
            category_assignments[category_name].append(title[:50] + '...')
            
            print(f"Updated product {product_id}: {title[:40]}... -> Category: {category_name}")
    
    conn.commit()
    print(f"\nSuccessfully updated {updated_count} products with category information")
    
    # Show summary of category assignments
    print("\nCategory Assignment Summary:")
    print("-" * 80)
    for category, products_list in category_assignments.items():
        print(f"\n{category}:")
        for prod in products_list[:3]:  # Show first 3 products per category
            print(f"  - {prod}")
        if len(products_list) > 3:
            print(f"  ... and {len(products_list) - 3} more")
    
    # Show final state
    cursor.execute("""
        SELECT category, COUNT(*) as count 
        FROM marketplace_products 
        WHERE marketplace_id = ? 
        GROUP BY category
        ORDER BY count DESC
    """, (ozon_id,))
    
    print("\nFinal category distribution:")
    print("-" * 50)
    for row in cursor.fetchall():
        print(f"{row['category'] or 'No category':30} : {row['count']} products")
    
    conn.close()

if __name__ == "__main__":
    update_ozon_categories()