# Data Fixes Directory

This directory contains one-time data migration and fix scripts.

## Files in this directory:
- `convert_ozon_to_usd.py` - Converts Ozon product prices from RUB to USD (2024-07-14)
- `update_ozon_brands.py` - Extracts and updates brand names from Ozon product titles (2024-07-14)
- `update_ozon_categories.py` - Assigns categories to Ozon products based on product types (2024-07-14)
- `update_ozon_categories_v2.py` - Improved category assignment for Ozon products (2024-07-14)
- `update_ozon_prices.py` - Adds sample prices to Ozon products for demonstration (2024-07-14)

## Usage:
These scripts are meant to be run once to fix or migrate data. After running, they should not be needed again unless similar data issues arise.

```bash
python scripts/data_fixes/script_name.py
```

## Important:
- Always backup the database before running data fix scripts
- Document what each script does and when it was run
- Consider deleting scripts after they've served their purpose (keep in git history)