#!/usr/bin/env python3
"""
Convert all Ozon products from RUB to USD
"""

import sqlite3

def convert_ozon_to_usd():
    """Convert Ozon product prices from RUB to USD"""
    conn = sqlite3.connect('pazaryeri.db')
    cursor = conn.cursor()
    
    try:
        # Get Ozon marketplace ID
        cursor.execute("SELECT id FROM marketplaces WHERE name = 'Ozon'")
        ozon_id = cursor.fetchone()[0]
        
        # Get all Ozon products with RUB currency
        cursor.execute("""
            SELECT id, title, price, currency 
            FROM marketplace_products 
            WHERE marketplace_id = ? AND currency = 'RUB'
        """, (ozon_id,))
        
        products = cursor.fetchall()
        print(f"Found {len(products)} Ozon products with RUB currency")
        
        # Approximate conversion rate (1 USD = ~100 RUB)
        conversion_rate = 100.0
        
        converted_count = 0
        for product_id, title, price, currency in products:
            # Convert price from RUB to USD
            usd_price = round(price / conversion_rate, 2)
            
            # Ensure minimum price of $0.99
            if usd_price < 0.99:
                usd_price = 0.99
            
            # Update product
            cursor.execute("""
                UPDATE marketplace_products 
                SET price = ?, currency = 'USD' 
                WHERE id = ?
            """, (usd_price, product_id))
            
            converted_count += 1
            print(f"Converted: {title[:50]}... {price} RUB → ${usd_price} USD")
        
        conn.commit()
        print(f"\nSuccessfully converted {converted_count} products to USD")
        
        # Show summary
        cursor.execute("""
            SELECT 
                MIN(price) as min_price,
                MAX(price) as max_price,
                AVG(price) as avg_price,
                COUNT(*) as total
            FROM marketplace_products 
            WHERE marketplace_id = ? AND currency = 'USD'
        """, (ozon_id,))
        
        stats = cursor.fetchone()
        print(f"\nOzon products price statistics (USD):")
        print(f"Total products: {stats[3]}")
        print(f"Min price: ${stats[0]:.2f}")
        print(f"Max price: ${stats[1]:.2f}")
        print(f"Average price: ${stats[2]:.2f}")
        
        # Show sample products
        cursor.execute("""
            SELECT title, brand, price 
            FROM marketplace_products 
            WHERE marketplace_id = ? 
            ORDER BY price DESC 
            LIMIT 10
        """, (ozon_id,))
        
        print("\nTop 10 most expensive products:")
        print("-" * 80)
        for title, brand, price in cursor.fetchall():
            print(f"${price:7.2f} | {brand:15} | {title[:40]}...")
            
    except Exception as e:
        print(f"Error converting products: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    convert_ozon_to_usd()