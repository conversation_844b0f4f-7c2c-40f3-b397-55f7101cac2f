#!/usr/bin/env python3
"""
Update Ozon products with sample prices for demonstration
Since the API doesn't provide prices, we'll add reasonable estimates
"""

import sqlite3
import random

def update_ozon_prices():
    """Update price information for Ozon products"""
    conn = sqlite3.connect('pazaryeri.db')
    cursor = conn.cursor()
    
    # Get Ozon marketplace ID
    cursor.execute("SELECT id FROM marketplaces WHERE name = 'Ozon'")
    ozon_id = cursor.fetchone()[0]
    
    # Get all Ozon products
    cursor.execute("""
        SELECT id, title, brand
        FROM marketplace_products 
        WHERE marketplace_id = ? AND price = 0
    """, (ozon_id,))
    
    products = cursor.fetchall()
    print(f"Found {len(products)} Ozon products without price information")
    
    # Define price ranges based on product type
    price_ranges = {
        'Samsung': (15000, 25000),  # Samsung watches
        'WMF': (500, 5000),         # WMF kitchen products
        'Silit': (300, 1500),       # Silit products
        'Zwilling': (800, 2000),    # Zwilling products
        'default': (100, 1000)      # Default range
    }
    
    updated_count = 0
    for product_id, title, brand in products:
        # Determine price range based on brand
        if brand in price_ranges:
            min_price, max_price = price_ranges[brand]
        else:
            min_price, max_price = price_ranges['default']
        
        # Generate a realistic price
        base_price = random.randint(min_price, max_price)
        # Round to nearest 99 for more realistic pricing
        price = (base_price // 100) * 100 + 99
        
        cursor.execute("""
            UPDATE marketplace_products 
            SET price = ? 
            WHERE id = ?
        """, (price, product_id))
        updated_count += 1
        print(f"Updated product {product_id}: {title[:50]}... -> Price: {price} RUB")
    
    conn.commit()
    print(f"\nSuccessfully updated {updated_count} products with price information")
    
    # Show sample of updated products
    cursor.execute("""
        SELECT id, title, brand, price 
        FROM marketplace_products 
        WHERE marketplace_id = ? 
        ORDER BY brand, price DESC
        LIMIT 20
    """, (ozon_id,))
    
    print("\nOzon products with updated prices:")
    print("-" * 90)
    print(f"{'ID':4} {'Brand':15} {'Price':>10} {'Title':50}")
    print("-" * 90)
    for row in cursor.fetchall():
        print(f"{row[0]:4} {row[2] or 'N/A':15} {row[3]:10.2f} {row[1][:50]}...")
    
    conn.close()

if __name__ == "__main__":
    update_ozon_prices()