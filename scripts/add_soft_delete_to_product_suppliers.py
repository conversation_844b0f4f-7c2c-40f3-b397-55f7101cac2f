#!/usr/bin/env python3
"""
Migration script to add soft delete functionality to product_suppliers table
Adds: is_deleted, deleted_at, deleted_by columns
"""

import sqlite3
from datetime import datetime

def add_soft_delete_columns():
    """Add soft delete columns to product_suppliers table"""
    conn = sqlite3.connect('pazaryeri.db')
    cursor = conn.cursor()
    
    print("Starting soft delete migration for product_suppliers table...")
    
    try:
        # Check if columns already exist
        cursor.execute("PRAGMA table_info(product_suppliers)")
        columns = [col[1] for col in cursor.fetchall()]
        
        if 'is_deleted' in columns:
            print("Soft delete columns already exist. Migration skipped.")
            return
        
        # Add soft delete columns
        print("Adding is_deleted column...")
        cursor.execute('''
            ALTER TABLE product_suppliers 
            ADD COLUMN is_deleted BOOLEAN DEFAULT 0
        ''')
        
        print("Adding deleted_at column...")
        cursor.execute('''
            ALTER TABLE product_suppliers 
            ADD COLUMN deleted_at TIMESTAMP NULL
        ''')
        
        print("Adding deleted_by column...")
        cursor.execute('''
            ALTER TABLE product_suppliers 
            ADD COLUMN deleted_by INTEGER NULL
        ''')
        
        # Create index for performance
        print("Creating index on is_deleted...")
        cursor.execute('''
            CREATE INDEX IF NOT EXISTS idx_product_suppliers_is_deleted 
            ON product_suppliers(is_deleted)
        ''')
        
        # Set default values for existing records
        print("Setting default values for existing records...")
        cursor.execute('''
            UPDATE product_suppliers 
            SET is_deleted = 0 
            WHERE is_deleted IS NULL
        ''')
        
        conn.commit()
        
        # Verify the changes
        cursor.execute("PRAGMA table_info(product_suppliers)")
        new_columns = cursor.fetchall()
        print("\nUpdated table structure:")
        for col in new_columns:
            print(f"  {col[1]} {col[2]} {'NOT NULL' if col[3] else 'NULL'} {'DEFAULT ' + str(col[4]) if col[4] else ''}")
        
        # Check current data
        cursor.execute("SELECT COUNT(*) FROM product_suppliers WHERE is_deleted = 0")
        active_count = cursor.fetchone()[0]
        cursor.execute("SELECT COUNT(*) FROM product_suppliers WHERE is_deleted = 1")
        deleted_count = cursor.fetchone()[0]
        
        print(f"\nData status:")
        print(f"  Active relationships: {active_count}")
        print(f"  Deleted relationships: {deleted_count}")
        
        print("\n✅ Soft delete migration completed successfully!")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    add_soft_delete_columns()