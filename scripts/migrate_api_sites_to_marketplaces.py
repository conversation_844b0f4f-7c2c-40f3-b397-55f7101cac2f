#!/usr/bin/env python3
"""
Migration Script: api_sites to marketplaces
Migrates data from api_sites table to enhanced marketplaces table
"""

import sqlite3
from datetime import datetime
import shutil

def backup_database():
    """Create a backup of the current database"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_path = f'migrations/backups/pazaryeri_backup_api_sites_migration_{timestamp}.db'
    shutil.copy('pazaryeri.db', backup_path)
    print(f"✅ Database backed up to: {backup_path}")
    return backup_path

def enhance_marketplaces_table(conn):
    """Enhance marketplaces table with all api_sites fields"""
    cursor = conn.cursor()
    
    print("🔄 Enhancing marketplaces table structure...")
    
    # Get current columns
    cursor.execute("PRAGMA table_info(marketplaces)")
    existing_columns = {col[1] for col in cursor.fetchall()}
    
    # Add all missing columns from api_sites
    new_columns = [
        ("client_id", "TEXT"),
        ("base_url", "TEXT"),
        ("access_token", "TEXT"),
        ("refresh_token", "TEXT"),
        ("token_expires_at", "TIMESTAMP"),
        ("rate_limit", "INTEGER DEFAULT 100"),
        ("rate_limit_window", "INTEGER DEFAULT 3600"),
        ("is_active", "BOOLEAN DEFAULT 1"),
        ("marketplace_type", "TEXT DEFAULT 'marketplace'"),
        ("country_code", "TEXT"),
        ("updated_at", "TIMESTAMP")
    ]
    
    for col_name, col_type in new_columns:
        if col_name not in existing_columns:
            try:
                cursor.execute(f"ALTER TABLE marketplaces ADD COLUMN {col_name} {col_type}")
                print(f"  ✅ Added column: {col_name}")
            except sqlite3.Error as e:
                print(f"  ⚠️  Column {col_name} might already exist: {e}")
    
    # Set default values for existing records (check if columns exist first)
    cursor.execute("PRAGMA table_info(marketplaces)")
    current_columns = {col[1] for col in cursor.fetchall()}
    
    if 'updated_at' in current_columns:
        cursor.execute("UPDATE marketplaces SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL")
    if 'is_active' in current_columns:
        cursor.execute("UPDATE marketplaces SET is_active = 1 WHERE is_active IS NULL")
    if 'rate_limit' in current_columns:
        cursor.execute("UPDATE marketplaces SET rate_limit = 100 WHERE rate_limit IS NULL")
    if 'rate_limit_window' in current_columns:
        cursor.execute("UPDATE marketplaces SET rate_limit_window = 3600 WHERE rate_limit_window IS NULL")
    if 'marketplace_type' in current_columns:
        cursor.execute("UPDATE marketplaces SET marketplace_type = 'marketplace' WHERE marketplace_type IS NULL")
    
    print("  ✅ Marketplaces table enhanced with api_sites fields")

def migrate_data_from_api_sites(conn):
    """Migrate data from api_sites to marketplaces table"""
    cursor = conn.cursor()
    
    print("🔄 Migrating data from api_sites to marketplaces...")
    
    # First, clear duplicate entries in marketplaces
    cursor.execute("DELETE FROM marketplaces")
    print("  ✅ Cleared existing duplicate entries in marketplaces")
    
    # Get all data from api_sites
    cursor.execute("""
        SELECT name, base_url, client_id, api_key, api_secret, access_token, 
               refresh_token, token_expires_at, rate_limit, rate_limit_window,
               is_active, marketplace_type, country_code, created_at, updated_at
        FROM api_sites
        ORDER BY id
    """)
    
    api_sites_data = cursor.fetchall()
    
    # Insert data into marketplaces with mapping
    for row in api_sites_data:
        name, base_url, client_id, api_key, api_secret, access_token, refresh_token, \
        token_expires_at, rate_limit, rate_limit_window, is_active, marketplace_type, \
        country_code, created_at, updated_at = row
        
        # Map country_code to region (keep both for compatibility)
        region = country_code
        
        cursor.execute("""
            INSERT INTO marketplaces (
                name, api_key, api_secret, region, created_at, is_deleted, deleted_at,
                client_id, base_url, access_token, refresh_token, token_expires_at,
                rate_limit, rate_limit_window, is_active, marketplace_type, 
                country_code, updated_at
            ) VALUES (?, ?, ?, ?, ?, 0, NULL, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            name, api_key, api_secret, region, created_at, client_id, base_url,
            access_token, refresh_token, token_expires_at, rate_limit, 
            rate_limit_window, is_active, marketplace_type, country_code, updated_at
        ))
        
        print(f"  ✅ Migrated: {name} ({country_code})")
    
    print(f"  ✅ Successfully migrated {len(api_sites_data)} records")

def create_indexes(conn):
    """Create performance indexes for marketplaces table"""
    cursor = conn.cursor()
    
    print("🔄 Creating performance indexes...")
    
    indexes = [
        ("idx_marketplaces_name", "CREATE UNIQUE INDEX IF NOT EXISTS idx_marketplaces_name ON marketplaces(name) WHERE is_deleted = 0"),
        ("idx_marketplaces_is_active", "CREATE INDEX IF NOT EXISTS idx_marketplaces_is_active ON marketplaces(is_active)"),
        ("idx_marketplaces_is_deleted", "CREATE INDEX IF NOT EXISTS idx_marketplaces_is_deleted ON marketplaces(is_deleted)"),
        ("idx_marketplaces_country", "CREATE INDEX IF NOT EXISTS idx_marketplaces_country ON marketplaces(country_code)"),
        ("idx_marketplaces_type", "CREATE INDEX IF NOT EXISTS idx_marketplaces_type ON marketplaces(marketplace_type)")
    ]
    
    for idx_name, idx_sql in indexes:
        try:
            cursor.execute(idx_sql)
            print(f"  ✅ Created index: {idx_name}")
        except sqlite3.Error as e:
            print(f"  ⚠️  Index {idx_name} might already exist: {e}")

def verify_migration(conn):
    """Verify the migration was successful"""
    cursor = conn.cursor()
    
    print("🔍 Verifying migration...")
    
    # Check marketplaces count
    cursor.execute("SELECT COUNT(*) FROM marketplaces WHERE is_deleted = 0")
    marketplaces_count = cursor.fetchone()[0]
    
    # Check api_sites count
    cursor.execute("SELECT COUNT(*) FROM api_sites")
    api_sites_count = cursor.fetchone()[0]
    
    print(f"  📊 api_sites records: {api_sites_count}")
    print(f"  📊 marketplaces records: {marketplaces_count}")
    
    if marketplaces_count == api_sites_count:
        print("  ✅ Migration verification successful!")
        return True
    else:
        print("  ❌ Migration verification failed - record counts don't match!")
        return False

def main():
    """Main migration function"""
    print("🚀 Starting api_sites to marketplaces Migration")
    print("=" * 50)
    
    # Create backup
    backup_path = backup_database()
    
    # Connect to database
    conn = sqlite3.connect('pazaryeri.db')
    conn.execute("PRAGMA foreign_keys = ON")
    
    try:
        # Step 1: Enhance marketplaces table
        enhance_marketplaces_table(conn)
        
        # Step 2: Migrate data
        migrate_data_from_api_sites(conn)
        
        # Step 3: Create indexes
        create_indexes(conn)
        
        # Step 4: Verify migration
        if verify_migration(conn):
            # Commit all changes
            conn.commit()
            
            print("\n" + "=" * 50)
            print("✅ api_sites to marketplaces Migration Completed Successfully!")
            print(f"📁 Backup created at: {backup_path}")
            
            # Show summary
            cursor = conn.cursor()
            cursor.execute("SELECT name, country_code, is_active FROM marketplaces WHERE is_deleted = 0 ORDER BY name")
            marketplaces = cursor.fetchall()
            
            print("\n📊 Migrated Marketplaces:")
            for mp in marketplaces:
                status = "Active" if mp[2] else "Inactive"
                print(f"  - {mp[0]} ({mp[1]}) - {status}")
                
        else:
            print("\n❌ Migration verification failed - rolling back changes")
            conn.rollback()
            
    except Exception as e:
        print(f"\n❌ Migration failed: {e}")
        conn.rollback()
        print(f"🔄 Database can be restored from: {backup_path}")
        raise
    finally:
        conn.close()

if __name__ == "__main__":
    main()