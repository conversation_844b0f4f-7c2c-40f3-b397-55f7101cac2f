#!/usr/bin/env python3
"""
Database Schema Synchronization Script
Compares two SQLite databases and adds missing tables/columns to make them identical
Only adds missing elements, never removes anything
"""

import sqlite3
import os
from datetime import datetime
import shutil


def backup_databases():
    """Create backups of both databases"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backups = []

    for db_file in ['pazaryeri.db', 'pazaryeri_backup.db']:
        if os.path.exists(db_file):
            backup_path = f'migrations/backups/{db_file.replace(".db", "")}_sync_backup_{timestamp}.db'
            os.makedirs('migrations/backups', exist_ok=True)
            shutil.copy(db_file, backup_path)
            backups.append(backup_path)
            print(f"✅ {db_file} backed up to: {backup_path}")

    return backups


def get_database_schema(db_path):
    """Get complete schema information from database"""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    schema = {
        'tables': {},
        'indexes': []
    }

    # Get all tables
    cursor.execute(
        "SELECT name, sql FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
    tables = cursor.fetchall()

    for table_name, table_sql in tables:
        # Get column information
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()

        schema['tables'][table_name] = {
            'sql': table_sql,
            'columns': {col[1]: {
                'type': col[2],
                'notnull': col[3],
                'default': col[4],
                'pk': col[5]
            } for col in columns}
        }

    # Get indexes
    cursor.execute(
        "SELECT name, sql FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'")
    schema['indexes'] = cursor.fetchall()

    conn.close()
    return schema


def add_missing_table(conn, table_name, table_sql):
    """Add missing table to database"""
    cursor = conn.cursor()
    try:
        cursor.execute(table_sql)
        print(f"  ✅ Created table: {table_name}")
        return True
    except sqlite3.Error as e:
        print(f"  ❌ Error creating table {table_name}: {e}")
        return False


def add_missing_column(conn, table_name, column_name, column_info):
    """Add missing column to existing table"""
    cursor = conn.cursor()

    # Build column definition
    col_def = f"{column_name} {column_info['type']}"

    if column_info['notnull']:
        col_def += " NOT NULL"

    if column_info['default'] is not None:
        col_def += f" DEFAULT {column_info['default']}"

    try:
        cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {col_def}")
        print(f"  ✅ Added column {column_name} to {table_name}")
        return True
    except sqlite3.Error as e:
        print(f"  ❌ Error adding column {column_name} to {table_name}: {e}")
        return False


def sync_schemas(db1_path, db2_path):
    """Synchronize schemas between two databases"""
    print(f"🔄 Synchronizing schemas between {db1_path} and {db2_path}")

    # Get schemas
    schema1 = get_database_schema(db1_path)
    schema2 = get_database_schema(db2_path)

    # Connect to both databases
    conn1 = sqlite3.connect(db1_path)
    conn2 = sqlite3.connect(db2_path)

    # Disable FK during schema changes
    conn1.execute("PRAGMA foreign_keys = OFF")
    conn2.execute("PRAGMA foreign_keys = OFF")

    changes_made = False

    try:
        # Find tables in schema2 but not in schema1
        missing_tables_in_db1 = set(
            schema2['tables'].keys()) - set(schema1['tables'].keys())
        if missing_tables_in_db1:
            print(f"\n📋 Adding missing tables to {db1_path}:")
            for table_name in missing_tables_in_db1:
                if add_missing_table(conn1, table_name, schema2['tables'][table_name]['sql']):
                    changes_made = True

        # Find tables in schema1 but not in schema2
        missing_tables_in_db2 = set(
            schema1['tables'].keys()) - set(schema2['tables'].keys())
        if missing_tables_in_db2:
            print(f"\n📋 Adding missing tables to {db2_path}:")
            for table_name in missing_tables_in_db2:
                if add_missing_table(conn2, table_name, schema1['tables'][table_name]['sql']):
                    changes_made = True

        # Check for missing columns in common tables
        common_tables = set(schema1['tables'].keys()) & set(
            schema2['tables'].keys())

        for table_name in common_tables:
            cols1 = set(schema1['tables'][table_name]['columns'].keys())
            cols2 = set(schema2['tables'][table_name]['columns'].keys())

            # Missing columns in db1
            missing_cols_in_db1 = cols2 - cols1
            if missing_cols_in_db1:
                print(
                    f"\n🔧 Adding missing columns to {table_name} in {db1_path}:")
                for col_name in missing_cols_in_db1:
                    col_info = schema2['tables'][table_name]['columns'][col_name]
                    if add_missing_column(conn1, table_name, col_name, col_info):
                        changes_made = True

            # Missing columns in db2
            missing_cols_in_db2 = cols1 - cols2
            if missing_cols_in_db2:
                print(
                    f"\n🔧 Adding missing columns to {table_name} in {db2_path}:")
                for col_name in missing_cols_in_db2:
                    col_info = schema1['tables'][table_name]['columns'][col_name]
                    if add_missing_column(conn2, table_name, col_name, col_info):
                        changes_made = True

        if changes_made:
            conn1.commit()
            conn2.commit()
            print(f"\n✅ Schema synchronization completed successfully!")
        else:
            print(f"\n✅ Schemas are already synchronized!")

    except Exception as e:
        print(f"\n❌ Error during synchronization: {e}")
        conn1.rollback()
        conn2.rollback()
        raise
    finally:
        conn1.execute("PRAGMA foreign_keys = ON")  # Re-enable FK
        conn2.execute("PRAGMA foreign_keys = ON")
        conn1.close()
        conn2.close()


def main():
    """Main function"""
    print("🚀 Database Schema Synchronization")
    print("=" * 50)

    # Check if both databases exist
    db1_path = 'pazaryeri.db'
    db2_path = 'pazaryeri_backup.db'

    if not os.path.exists(db1_path):
        print(f"❌ Database {db1_path} not found!")
        return

    # If backup doesn't exist, create it from main database
    if not os.path.exists(db2_path):
        print(f"📋 Creating {db2_path} from {db1_path}...")
        shutil.copy(db1_path, db2_path)
        print(f"✅ {db2_path} created successfully!")
        print("ℹ️  Both databases are now identical. Run the script again after making schema changes to either database.")
        return

    # Create backups
    backup_paths = backup_databases()

    try:
        # Synchronize schemas
        sync_schemas(db1_path, db2_path)

        print("\n" + "=" * 50)
        print("📊 Final Schema Summary:")

        # Show final table counts
        for db_path in [db1_path, db2_path]:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            cursor.execute(
                "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' ORDER BY name")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"\n📁 {db_path}: {len(tables)} tables")
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"  - {table}: {count} records")
            conn.close()

    except Exception as e:
        print(f"\n❌ Synchronization failed: {e}")
        print(f"🔄 Databases can be restored from backups:")
        for backup in backup_paths:
            print(f"  - {backup}")


if __name__ == "__main__":
    main()
