# Soft Delete Implementation Guide

## Overview
This document describes the soft delete implementation for the PazarYeri application, starting with the `product_suppliers` table.

## What is Soft Delete?
Soft delete is a pattern where instead of permanently removing data from the database, records are marked as "deleted" and hidden from normal queries. This allows for:

- **Data Recovery**: Accidentally deleted records can be restored
- **Audit Trail**: Track what was deleted, when, and by whom
- **Referential Integrity**: No foreign key constraint violations
- **Reporting**: Historical data remains available for analytics

## Database Schema

### product_suppliers Table Structure
```sql
CREATE TABLE product_suppliers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_id INTEGER NOT NULL,
    supplier_id INTEGER NOT NULL,
    supplier_product_url TEXT,
    supplier_price REAL,
    last_checked TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- Soft Delete Fields
    is_deleted BOOLEAN DEFAULT 0,
    deleted_at TIMESTAMP NULL,
    deleted_by INTEGER NULL,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE,
    UNIQUE(product_id, supplier_id)
);

-- Performance Index
CREATE INDEX idx_product_suppliers_is_deleted ON product_suppliers(is_deleted);
```

## API Endpoints

### 1. Soft Delete
```
DELETE /api/product-supplier/delete/{product_id}/{supplier_id}
```
Marks a product-supplier relationship as deleted.

### 2. Restore
```
POST /api/product-supplier/restore/{product_id}/{supplier_id}
```
Restores a soft-deleted relationship.

### 3. List Deleted
```
GET /api/product-supplier/deleted
```
Returns all soft-deleted relationships with full details.

### 4. Hard Delete (Admin Only)
```
DELETE /api/product-supplier/hard-delete/{product_id}/{supplier_id}
```
Permanently removes a relationship from the database.

## Utility Functions

### Location: `app/utils/soft_delete.py`

#### Core Functions
```python
# Soft delete a relationship
soft_delete_product_supplier(product_id, supplier_id, deleted_by=None)

# Restore a relationship  
restore_product_supplier(product_id, supplier_id)

# Get all deleted relationships
get_deleted_product_suppliers()

# Add soft delete filter to queries
add_soft_delete_filter(query, table_alias="ps")
```

#### SoftDeleteMixin Class
Provides reusable methods for soft delete operations that can be extended to other tables.

## Query Patterns

### Before (Hard Delete)
```sql
SELECT * FROM product_suppliers ps
JOIN suppliers s ON ps.supplier_id = s.id
WHERE ps.product_id = ?
```

### After (Soft Delete)
```sql
SELECT * FROM product_suppliers ps
JOIN suppliers s ON ps.supplier_id = s.id
WHERE ps.product_id = ? AND ps.is_deleted = 0
```

## Admin Interface

### Location: `/admin/deleted-relationships`

Features:
- View all soft-deleted relationships
- Restore individual relationships
- Hard delete individual relationships  
- Bulk restore all deleted relationships
- Bulk hard delete all deleted relationships
- Real-time feedback with toast notifications

## Migration

### Adding Soft Delete to Existing Tables

1. **Create Migration Script**:
```python
# scripts/add_soft_delete_to_{table}.py
ALTER TABLE {table} ADD COLUMN is_deleted BOOLEAN DEFAULT 0;
ALTER TABLE {table} ADD COLUMN deleted_at TIMESTAMP NULL;
ALTER TABLE {table} ADD COLUMN deleted_by INTEGER NULL;
CREATE INDEX idx_{table}_is_deleted ON {table}(is_deleted);
```

2. **Update Queries**: Add `WHERE is_deleted = 0` to all SELECT queries

3. **Update Delete Operations**: Replace DELETE with UPDATE SET is_deleted = 1

## Best Practices

### 1. Query Filtering
Always filter out deleted records in SELECT queries:
```python
# Good
suppliers = conn.execute('''
    SELECT * FROM product_suppliers ps
    WHERE ps.product_id = ? AND ps.is_deleted = 0
''', (product_id,)).fetchall()

# Bad - includes deleted records
suppliers = conn.execute('''
    SELECT * FROM product_suppliers ps
    WHERE ps.product_id = ?
''', (product_id,)).fetchall()
```

### 2. Relationship Management
When adding relationships, check for existing deleted ones:
```python
# Check if relationship exists (including deleted)
existing = conn.execute('''
    SELECT id, is_deleted FROM product_suppliers 
    WHERE product_id = ? AND supplier_id = ?
''', (product_id, supplier_id)).fetchone()

if existing and existing[1] == 1:  # is_deleted = 1
    # Restore instead of creating new
    restore_product_supplier(product_id, supplier_id)
```

### 3. Performance Considerations
- Index the `is_deleted` column for better query performance
- Consider archiving very old deleted records
- Monitor database size growth over time

### 4. User Experience
- Provide clear feedback when items are deleted/restored
- Implement undo functionality where appropriate
- Show deletion timestamps and user info for audit trail

## Security Considerations

### Hard Delete Access
Hard delete should be restricted to administrators only:
```python
# Example decorator for admin-only endpoints
@admin_required
@bp.route('/api/product-supplier/hard-delete/<int:product_id>/<int:supplier_id>', methods=['DELETE'])
def api_hard_delete_product_supplier(product_id, supplier_id):
    # Implementation
```

### Audit Logging
Track who performs delete/restore operations:
```python
def soft_delete_product_supplier(product_id, supplier_id, deleted_by=None):
    # deleted_by should be current user ID
    cursor.execute('''
        UPDATE product_suppliers 
        SET is_deleted = 1, deleted_at = CURRENT_TIMESTAMP, deleted_by = ?
        WHERE product_id = ? AND supplier_id = ?
    ''', (deleted_by, product_id, supplier_id))
```

## Testing

### Test Categories
1. **Functional Tests**: Verify soft delete/restore operations
2. **Query Tests**: Ensure deleted records are properly filtered
3. **API Tests**: Test all endpoints with various scenarios
4. **UI Tests**: Verify admin interface functionality
5. **Performance Tests**: Check query performance with large datasets

### Example Test
```python
def test_soft_delete_functionality():
    # Test soft delete
    success, message = soft_delete_product_supplier(1, 1)
    assert success == True
    
    # Verify record is hidden from normal queries
    suppliers = get_active_suppliers_for_product(1)
    assert len(suppliers) == 0
    
    # Verify record appears in deleted list
    deleted = get_deleted_product_suppliers()
    assert len(deleted) == 1
    
    # Test restore
    success, message = restore_product_supplier(1, 1)
    assert success == True
    
    # Verify record is visible again
    suppliers = get_active_suppliers_for_product(1)
    assert len(suppliers) == 1
```

## Future Extensions

### Tables to Consider for Soft Delete
1. `suppliers` - Supplier companies
2. `products` - Product catalog
3. `product_images` - Product images
4. `marketplace_products` - Marketplace listings

### Additional Features
1. **Scheduled Cleanup**: Auto-hard-delete very old soft-deleted records
2. **Backup Integration**: Export deleted records before hard delete
3. **Bulk Operations**: Mass delete/restore with filters
4. **Notification System**: Alert admins of mass deletions
5. **Recovery Workflows**: Formal approval process for data recovery

## Conclusion

Soft delete provides a robust solution for data management, offering both safety and flexibility. The implementation follows industry best practices and can be easily extended to other tables in the application.

For questions or improvements, refer to the development team or create an issue in the project repository.