# CLAUDE.md - PazarYeri Architecture Guidelines

## 🏗️ CRITICAL: Architecture Must Be Preserved

The user has specifically requested that the architecture and design of `app.py` be preserved. This document outlines the patterns and principles that MUST be maintained in all future modifications.

## 📋 Architecture Overview

### Core Design Pattern: Flask MVC with Service Layer

```
┌─────────────┐     ┌──────────────┐     ┌───────────────┐
│   Routes    │────▶│   Services   │────▶│   Database    │
│  (app.py)   │     │  (Managers)  │     │  (SQLite)     │
└─────────────┘     └──────────────┘     └───────────────┘
       │                                           ▲
       ▼                                           │
┌─────────────┐                                    │
│  Templates  │────────────────────────────────────┘
│   (HTML)    │     (Direct DB queries for display)
└─────────────┘
```

## 🚫 DO NOT MODIFY These Patterns

### 1. Database Connection Pattern
```python
# ALWAYS use this pattern:
def get_db_connection():
    conn = sqlite3.connect('pazaryeri.db')
    conn.row_factory = sqlite3.Row
    return conn

# In routes:
conn = get_db_connection()
# ... do work ...
conn.close()
```

### 2. Route Structure
- Each route has a single, clear responsibility
- GET routes render templates
- POST routes return JSON for AJAX
- Error handling with try-except blocks returning JSON errors

### 3. Service Layer Usage
```python
# ALWAYS use service classes for business logic:
sync_service = ProductSyncService()
manager = APIManager()
finder = SupplierFinder()
```

### 4. Template Rendering Pattern
```python
# ALWAYS pass data explicitly:
return render_template('template.html', 
                      var1=value1,
                      var2=value2)
```

## 📐 Route Organization

### Main Groups (DO NOT REORGANIZE):

1. **Core Pages**
   - `/` - Dashboard with statistics
   - `/products` - Product listing
   - `/product/<id>` - Product detail

2. **CRUD Operations**
   - `/products/add` - Add new product
   - `/product/<id>/find-suppliers` - Find suppliers

3. **API Management**
   - `/api-sites` - List API sites
   - `/api-sites/<id>/update` - Update credentials
   - `/api-sites/<id>/toggle` - Toggle status

4. **Marketplace Features**
   - `/marketplace-products` - List marketplace products
   - `/marketplace-sync` - Sync products
   - `/sync-status` - View sync logs
   - `/product-mappings` - Manage mappings

## 🎨 UI/UX Design Principles

### Template Structure
```
base.html
├── Navbar (always visible)
├── Container
│   ├── Flash messages
│   └── {% block content %}
└── Footer
```

### Visual Design Rules
1. **Color Scheme**:
   - Primary: Bootstrap primary (blue)
   - Success: Green for positive actions
   - Warning: Yellow for sync operations
   - Info: Light blue for information

2. **Card-Based Layout**:
   - All content sections use Bootstrap cards
   - Hover effects on cards
   - Consistent shadows

3. **Icons**:
   - Bootstrap Icons for all UI elements
   - Consistent icon usage:
     - 📦 `bi-box` for products
     - 🚚 `bi-truck` for suppliers
     - 🌐 `bi-globe` for API
     - 🏪 `bi-shop-window` for marketplace
     - 🔄 `bi-arrow-repeat` for sync

4. **Dashboard Statistics**:
   - Color-coded cards
   - Large numbers with icons
   - Quick action links

## 🔧 Service Layer Interfaces

### DO NOT CHANGE these method signatures:

```python
# ProductSyncService
sync_service.sync_marketplace_by_name(name, limit)
sync_service.sync_all_marketplaces(limit_per_marketplace)
sync_service.get_sync_status()
sync_service.get_marketplace_product_counts()
sync_service.search_marketplace_products(search_term, marketplace_name, limit, offset)
sync_service.get_product_mappings(local_product_id)
sync_service.get_product_mapping_suggestions(local_product_id)
sync_service.create_product_mapping(local_id, marketplace_id, type, confidence, notes)

# APIManager
manager.update_api_credentials(site_id, client_id, api_key, api_secret)
manager.toggle_site_status(site_id)

# SupplierFinder
finder.find_suppliers_for_product(product_id)
```

## 🛡️ Error Handling Pattern

```python
# ALWAYS use this pattern for AJAX endpoints:
try:
    # ... operation ...
    return jsonify({'status': 'success', 'message': 'İşlem başarılı'})
except Exception as e:
    return jsonify({'status': 'error', 'message': str(e)}), 500
```

## 📊 Database Query Patterns

### Statistics Queries
```python
# Simple counts with proper closing
count = conn.execute('SELECT COUNT(*) FROM table').fetchone()[0]
```

### Join Queries
```python
# Multi-line SQL with proper formatting
data = conn.execute('''
    SELECT t1.*, t2.field
    FROM table1 t1
    JOIN table2 t2 ON t1.id = t2.foreign_id
    WHERE condition = ?
    ORDER BY t1.field
''', (param,)).fetchall()
```

## ⚠️ Critical Rules

1. **NEVER** modify the Flask app initialization pattern
2. **NEVER** change the port (5002)
3. **NEVER** remove the database initialization in `__main__`
4. **NEVER** change the template inheritance structure
5. **NEVER** modify the route URL patterns
6. **ALWAYS** maintain the clean separation between routes and services
7. **ALWAYS** use the same Bootstrap version (5.3.0)
8. **ALWAYS** keep the same jQuery version for compatibility

## 📝 Adding New Features

When adding new features:

1. Follow the existing route pattern
2. Use the service layer for business logic
3. Maintain the card-based UI design
4. Use Bootstrap Icons consistently
5. Return JSON for AJAX operations
6. Add proper error handling
7. Close database connections properly

## 🔒 Security Considerations

1. Always use parameterized queries (?, never string concatenation)
2. Strip and validate form inputs
3. Return generic error messages to users
4. Log detailed errors server-side only

## 📱 Responsive Design

The current design is mobile-friendly with:
- Responsive navbar
- Card layouts that stack on mobile
- Table-responsive wrappers
- Button groups that convert to vertical on small screens

**DO NOT** break this responsive behavior when making changes.

---

**Remember**: The user specifically loves this design. Any changes should enhance, not replace, the existing patterns.

## 📁 Project Folder Structure

### Root Directory Files (KEEP IN ROOT)
```
PazarYeri/
├── run.py                 # Application entry point
├── database.py            # Database initialization and setup
├── requirements.txt       # Python dependencies
├── README.md             # Project documentation
├── .env.example          # Environment variables template
├── .gitignore            # Git ignore rules
├── .api_encryption_key   # API encryption key (auto-generated)
├── pazaryeri.db         # SQLite database file
└── flask.log            # Application log file
```

### Application Structure (DO NOT MODIFY)
```
app/
├── __init__.py          # Flask app factory
├── config.py            # Configuration settings
├── database.py          # Database connection utilities
├── models/              # Database models (if needed)
├── routes/              # Route blueprints
│   ├── __init__.py
│   ├── main.py          # Dashboard and core routes
│   ├── products.py      # Product management routes
│   ├── marketplace.py   # Marketplace integration routes
│   ├── api.py           # API site management routes
│   ├── categories.py    # Category management routes
│   └── ai_providers.py  # AI provider management routes
├── services/            # Business logic layer
│   ├── __init__.py
│   ├── api_manager.py
│   ├── product_sync_service.py
│   ├── supplier_finder.py
│   ├── product_import_service.py
│   ├── category_service.py
│   ├── ai_provider_service.py
│   ├── ozon_product_enricher.py
│   └── marketplace/     # Marketplace-specific clients
│       ├── amazon_api_client.py
│       └── ozon_api_client.py
├── templates/           # Jinja2 HTML templates
│   ├── base.html        # Base template with navbar
│   ├── index.html       # Dashboard
│   ├── products.html    # Product listing
│   ├── categories/      # Category templates
│   ├── ai_providers/    # AI provider templates
│   └── admin/           # Admin templates
├── static/              # Static assets
│   ├── css/
│   ├── js/
│   └── uploads/         # User uploads
└── utils/               # Utility modules
    └── soft_delete.py   # Soft delete functionality
```

### Support Directories
```
docs/                    # Documentation
├── CLAUDE.md           # This architecture guide
├── API.md              # API documentation
└── api/                # API-specific docs

scripts/                 # Utility scripts
├── migrations/         # Database migration scripts
├── data_fixes/         # One-time data fix scripts
└── temp/               # Temporary/development scripts

tests/                  # Test files
├── test_*.py           # Unit and integration tests
└── fixtures/           # Test data

logs/                   # Log files
└── *.log              # Application logs

migrations/             # Database migrations (if using migration tool)
```

### File Placement Rules

#### ✅ Files that belong in ROOT:
- Application entry points (`run.py`)
- Core configuration (`database.py`, `requirements.txt`)
- Project documentation (`README.md`, `.env.example`)
- Git/IDE configuration (`.gitignore`, `.api_encryption_key`)
- Database file (`pazaryeri.db`)
- Main log file (`flask.log`)

#### ❌ Files that should NOT be in ROOT:
- Temporary scripts (`fetch_*.py`, `update_*.py`, `test_*.py`)
- One-time data migrations
- API test scripts
- Development/debugging scripts
- Generated documentation

### When Adding New Files:

1. **New Feature Module**: Add to `app/services/` or create new service
2. **New Route**: Add to appropriate file in `app/routes/` or create new blueprint
3. **New Template**: Add to `app/templates/` in appropriate subdirectory
4. **Utility Script**: Add to `scripts/` in appropriate subdirectory
5. **Test File**: Add to `tests/` with `test_` prefix
6. **Documentation**: Add to `docs/`
7. **Static Asset**: Add to `app/static/` in appropriate subdirectory

### Temporary Files to Move:
```
# These files should be moved from root:
fetch_ozon_products*.py    → scripts/temp/ or DELETE if no longer needed
update_ozon_*.py          → scripts/data_fixes/
test_category_sync.py     → tests/
convert_ozon_to_usd.py    → scripts/data_fixes/
ozon_fetch_summary.md     → docs/api/
```

### Import Rules:
- Always use absolute imports from `app` package
- Never add root directory to Python path in production code
- Use relative imports only within the same module

### Clean Code Practices:
1. Delete temporary scripts after use
2. Move one-time scripts to `scripts/` with descriptive names
3. Keep root directory minimal and clean
4. Document any script in `scripts/` with purpose and date
5. Use version control to track script history instead of keeping old versions