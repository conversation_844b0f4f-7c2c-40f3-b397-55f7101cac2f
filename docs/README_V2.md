# PazarYeri Pro - Yeni Veritabanı Yapısı

## Güncelleme Özeti

Veritabanı yapısı başarıyla güncellendi ve aşağıdaki yenilikler eklendi:

### Yeni Tablolar ve Özellikler

1. **Users (Kullanıcılar)**
   - Kullanıcı yönetimi
   - Ürün ve işlem sahipliği takibi

2. **Warehouses (Depolar)**
   - Çoklu depo yönetimi
   - Depo bazlı stok takibi

3. **Gelişmiş Products Tablosu**
   - SKU, Model, Marka, Barkod
   - Desi bilgisi
   - Kar marjı ve komisyon oranları
   - GTIP kodu
   - Stok çarpanı

4. **Suppliers (Tedarikçiler)**
   - Artık doğrudan ürünlere bağlı
   - Fiyat ve stok durumu takibi

5. **Inventory (Stok)**
   - Depo bazlı stok yönetimi
   - Rezerve stok takibi
   - Kullanılabilir stok hesaplaması

6. **Product Images**
   - Ürün görselleri yönetimi

7. **Price History**
   - Fiyat değişim geçmişi
   - Marketplace bazlı fiyat takibi

### Yeni Dosyalar

- `app_v2.py` - Güncellenmiş Flask uygulaması
- `database_v2.py` - Yeni veritabanı şeması
- `api_manager_v2.py` - Güncellenmiş API yöneticisi
- `product_sync_service_v2.py` - Güncellenmiş senkronizasyon servisi
- `templates/*_v2.html` - Yeni arayüz şablonları

### Çalıştırma

```bash
# Yeni uygulamayı çalıştır
python3 app_v2.py

# Veritabanını sıfırdan oluştur
python3 database_v2.py

# Ozon API bilgilerini güncelle
python3 update_ozon_api.py
```

### Önemli Değişiklikler

1. **Veritabanı Dosyası**: `pazaryeri.db` → `pazaryeri_v2.db`
2. **Port**: Hala 5002 kullanılıyor
3. **API Şifreleme**: API anahtarları artık şifreleniyor
4. **Stok Yönetimi**: Depo bazlı stok takibi
5. **Kullanıcı Takibi**: İşlemler kullanıcıya bağlanıyor

### Geçiş Süreci

1. Eski veritabanı otomatik olarak yedeklendi: `pazaryeri_backup.db`
2. Tüm mevcut veriler yeni yapıya taşındı
3. API bilgileri korundu ve şifrelendi
4. Marketplace ürünleri ve eşleştirmeleri korundu

### Yeni Özellikler

- Depo yönetimi
- Gelişmiş stok takibi
- Fiyat geçmişi
- Ürün görselleri desteği
- Kullanıcı bazlı yetkilendirme altyapısı
- GTIP ve gümrük bilgileri
- Kar marjı hesaplamaları

### Sonraki Adımlar

1. Eski `app.py` yerine `app_v2.py` kullanın
2. Yeni arayüz özelliklerini test edin
3. Stok ve depo yönetimini kullanmaya başlayın
4. Ürün görsellerini yükleyin
5. Fiyat takibi özelliğini aktif kullanın