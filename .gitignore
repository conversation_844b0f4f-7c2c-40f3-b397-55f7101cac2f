# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.cache
.mypy_cache/
.dmypy.json
dmypy.json

# Flask
instance/
.webassets-cache

# Environment
.env
.env.local
.env.*.local

# Database
*.db
*.sqlite
*.sqlite3
migrations/backups/

# Logs
logs/
*.log

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Project specific
app/static/uploads/products/*
!app/static/uploads/products/.gitkeep
flask.log

# Temporary files
*.tmp
*.bak
*_old.*
*_v2.*