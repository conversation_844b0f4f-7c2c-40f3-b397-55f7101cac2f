#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Veritabanından ürün linklerini çeken script
"""

import logging
import time
import re
import sqlite3
from typing import List, Dict
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup

# Logging konfigürasyonu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def get_db_connection():
    """Veritabanı bağlantısı al"""
    conn = sqlite3.connect('pazaryeri.db')
    conn.row_factory = sqlite3.Row
    return conn

def get_all_product_suppliers():
    """Tüm ürün tedarikçi bilgilerini getir"""
    conn = get_db_connection()
    try:
        result = conn.execute('''
            SELECT ps.id, ps.product_id, ps.supplier_product_url, ps.supplier_price,
                   p.product_name, p.sku
            FROM product_suppliers ps
            JOIN products p ON ps.product_id = p.id
            WHERE ps.supplier_product_url IS NOT NULL
            ORDER BY ps.id
        ''').fetchall()
        return [dict(row) for row in result]
    finally:
        conn.close()

def update_supplier_price(supplier_id, price):
    """Tedarikçi fiyatını güncelle"""
    conn = get_db_connection()
    try:
        # Türkiye saati için UTC+3 ekle
        conn.execute('''
            UPDATE product_suppliers 
            SET supplier_price = ?, last_checked = datetime('now', 'localtime')
            WHERE id = ?
        ''', (price, supplier_id))
        conn.commit()
    finally:
        conn.close()

def update_product_price_statistics(product_id):
    """Ürün fiyat istatistiklerini güncelle"""
    conn = get_db_connection()
    try:
        # En düşük ve en yüksek fiyatları hesapla
        result = conn.execute('''
            SELECT MIN(supplier_price) as min_price, MAX(supplier_price) as max_price,
                   AVG(supplier_price) as avg_price, COUNT(*) as supplier_count
            FROM product_suppliers 
            WHERE product_id = ? AND supplier_price > 0
        ''', (product_id,)).fetchone()
        
        if result and result['supplier_count'] > 0:
            conn.execute('''
                UPDATE products 
                SET min_price = ?, max_price = ?, avg_price = ?, price_updated_at = datetime('now', 'localtime')
                WHERE id = ?
            ''', (result['min_price'], result['max_price'], result['avg_price'], product_id))
            conn.commit()
    finally:
        conn.close()

def get_price_alerts(threshold_percent=5.0):
    """Fiyat uyarılarını getir"""
    conn = get_db_connection()
    try:
        # Son 7 günün fiyat değişimlerini kontrol et
        result = conn.execute('''
            SELECT ps.id, p.product_name, s.name as supplier_name,
                   ps.supplier_price as price, ps.last_checked,
                   LAG(ps.supplier_price) OVER (PARTITION BY ps.id ORDER BY ps.last_checked) as old_price
            FROM product_suppliers ps
            JOIN products p ON ps.product_id = p.id
            JOIN suppliers s ON ps.supplier_id = s.id
            WHERE ps.last_checked >= datetime('now', '-7 days')
            AND ps.supplier_price > 0
            ORDER BY ps.last_checked DESC
        ''').fetchall()
        
        alerts = []
        for row in result:
            if row['old_price'] and row['old_price'] > 0:
                price_change = row['price'] - row['old_price']
                price_change_percent = (price_change / row['old_price']) * 100
                
                if abs(price_change_percent) >= threshold_percent:
                    alert = dict(row)
                    alert['price_change'] = price_change
                    alert['price_change_percent'] = price_change_percent
                    alerts.append(alert)
        
        return alerts
    finally:
        conn.close()

def extract_price(html: str, url: str = "") -> float:
    """
    HTML içeriğinden fiyat bilgisini çıkarır

    Args:
        html (str): Sayfa HTML içeriği
        url (str): Sayfa URL'si (site tespiti için)

    Returns:
        float: Bulunan fiyat, bulunamazsa 0.0
    """
    try:
        soup = BeautifulSoup(html, 'html.parser')

        # Farklı siteler için fiyat selektörleri (analiz sonuçlarına göre güncellenmiş)
        price_selectors = {
            'hepsiburada.com': [
                '.price-current',
                '.price',
                '[data-test-id="price-current-price"]',
                '.product-price'
            ],
            'trendyol.com': [
                '.prc-dsc',
                '.prc-org',
                '.price-current',
                '.product-price'
            ],
            'teknosa.com': [
                # Önce en spesifik fiyat selektörlerini dene
                '.pds-prices',  # Ana ürün fiyatı
                '.swogo-price-with-discount',  # İndirimli fiyat
                '.swogo-price:not(.swogo-price-with-discount)',  # Normal fiyat
                '[class*="price"]:not([class*="alarm"]):not([class*="track"])',  # Fiyat içeren ama alarm/track olmayan
                '.col-12:contains("TL"):not(:contains("Sepete"))',  # TL içeren ama sepete ekleme butonu olmayan
                '.product-price',
                '.price',
                '.current-price'
            ],
            'mediamarkt.com.tr': [
                # Önce daha spesifik fiyat selektörlerini dene
                '[data-test-id*="price"]',
                '.price-box .price',
                '.product-price-box .price',
                '.sc-e0c7d9f7-0.bPkjPs:contains("₺")',  # Sadece ₺ içerenler
                '.sc-d571b66f-0.kMgwbl.sc-9504f841-2.lgovom',
                '.price',
                '.product-price',
                '.current-price'
            ],
            'vatanbilgisayar.com': [
                '.product-list__price',  # Analiz sonucunda bulundu: "949"
                '.basketMobile_price',  # Analiz sonucunda bulundu: "949,00TL"
                '.d-cell',  # Analiz sonucunda bulundu: "949TL"
                '.product-list__content__price__current',
                '.price',
                '.current-price'
            ],
            'amazon.com.tr': [
                '.a-price-whole',
                '.a-price',
                '.price'
            ],
            'n11.com': [
                '.newPrice',
                '.priceContainer .price',
                '.productPrice .price',
                '.ins',
                '[class*="price"]'
            ],
            'gittigidiyor.com': [
                '.price-current',
                '.gg-price',
                '.product-price',
                '.price'
            ],
            'ciceksepeti.com': [
                '.price',
                '.product-price',
                '.current-price',
                '[class*="price"]'
            ],
            'morhipo.com': [
                '.price',
                '.product-price',
                '.current-price',
                '[class*="price"]'
            ]
        }

        # Site tespiti
        site_key = None
        for site in price_selectors.keys():
            if site in url.lower():
                site_key = site
                break

        # Fiyat arama
        price_text = None
        if site_key:
            # Belirli site için selektörleri dene
            for selector in price_selectors[site_key]:
                elements = soup.select(selector)
                if elements:
                    # Birden fazla element varsa, en uygun olanı seç
                    best_element = None
                    best_score = -1

                    for element in elements:
                        text = element.get_text(strip=True)
                        if not text:
                            continue

                        # Element kalite skoru hesapla
                        score = 0

                        # TL veya ₺ içeriyorsa +10
                        if re.search(r'[₺TL]', text):
                            score += 10

                        # Sayı içeriyorsa +5
                        if re.search(r'\d', text):
                            score += 5

                        # Kısa metinler daha iyi +5 (50 karakter altı)
                        if len(text) <= 50:
                            score += 5

                        # Çok uzun metinler kötü -10 (200 karakter üstü)
                        if len(text) > 200:
                            score -= 10

                        # Problemli kelimeler varsa -20
                        problematic_words = ['değerlendirme', 'inceleme', 'yorum', 'sepet', 'kargo']
                        if any(word in text.lower() for word in problematic_words):
                            score -= 20

                        if score > best_score:
                            best_score = score
                            best_element = element

                    if best_element and best_score > 0:
                        price_text = best_element.get_text(strip=True)
                        logger.info(f"Seçilen element (skor: {best_score}): {selector}")
                        break

        # Genel fiyat arama (site bulunamazsa)
        if not price_text:
            # Genel fiyat selektörleri
            general_selectors = [
                '.price', '.product-price', '.current-price', '.price-current',
                '[class*="price"]', '[class*="fiyat"]', '[id*="price"]'
            ]

            for selector in general_selectors:
                element = soup.select_one(selector)
                if element:
                    price_text = element.get_text(strip=True)
                    break

        if price_text:
            # Fiyat metninden sayısal değeri çıkar
            logger.info(f"Bulunan fiyat metni: '{price_text[:100]}...' - URL: {url}")

            # Çok uzun metinleri filtrele (muhtemelen yanlış element)
            if len(price_text) > 200:
                logger.warning(f"Çok uzun fiyat metni, muhtemelen yanlış element: {len(price_text)} karakter")
                return 0.0

            # Özel durumlar için fiyat çıkarma (Trendyol gibi)
            special_patterns = [
                r'son \d+ günün en düşük fiyatı[!]*(\d{1,4})\s*tl',  # "Son 14 Günün En Düşük Fiyatı!935 TL"
                r'en düşük fiyat[!]*(\d{1,4})\s*tl'  # "En Düşük Fiyat!935 TL"
            ]

            for pattern in special_patterns:
                match = re.search(pattern, price_text.lower())
                if match:
                    try:
                        price_value = float(match.group(1))
                        if 10 <= price_value <= 100000:
                            logger.info(f"Özel pattern ile çıkarılan fiyat: {price_value}")
                            return price_value
                    except ValueError:
                        continue

            # Diğer problemli metinleri filtrele
            problematic_patterns = [
                r'değerlendirme',
                r'inceleme',
                r'yorum',
                r'puan',
                r'sepet',
                r'kargo',
                r'teslimat',
                r'garanti',
                r'kampanya'
            ]

            for pattern in problematic_patterns:
                if re.search(pattern, price_text.lower()):
                    logger.warning(f"Problemli metin tespit edildi: {pattern}")
                    return 0.0

            # Farklı fiyat formatlarını destekle
            # Örnek formatlar: "1.197 TL", "₺949,00", "949TL", "1.258,–"

            # Önce sadece fiyat kısmını bul
            # TL, ₺ ile birlikte olan sayıları ara
            price_patterns = [
                r'(\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{1,2})?)\s*[₺TL]',  # 1.234,56 TL veya ₺1.234,56
                r'[₺TL]\s*(\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{1,2})?)',  # TL 1.234,56 veya ₺ 1.234,56
                r'(\d{1,3}(?:[.,]\d{3})*(?:[.,]\d{1,2})?)\s*(?:TL|₺|lira)',  # 1.234,56 TL
                r'(\d{1,3}(?:\.\d{3})*(?:,\d{1,2})?)\s*(?=\s|$|,)',  # Sadece sayı (Türk formatı) - virgül ile biten
                r'(\d{1,3}(?:,\d{3})*(?:\.\d{1,2})?)\s*(?=\s|$)',   # Sadece sayı (US formatı)
                r'(\d{1,4}),?\s*(?=\s|$)',  # Amazon formatı: "899," veya "1138,"
                r'(\d{1,4})\s*(?=\s|$)'     # Basit sayı formatı
            ]

            price_value = None
            for pattern in price_patterns:
                matches = re.findall(pattern, price_text, re.IGNORECASE)
                if matches:
                    # En büyük değeri al (genelde asıl fiyat)
                    for match in matches:
                        try:
                            # Türk formatını normalize et
                            clean_price = match.replace('.', '').replace(',', '.')
                            if clean_price.count('.') > 1:
                                # Birden fazla nokta varsa, son nokta ondalık ayırıcı
                                parts = clean_price.split('.')
                                clean_price = ''.join(parts[:-1]) + '.' + parts[-1]

                            value = float(clean_price)

                            # Makul fiyat aralığı kontrolü (10 TL - 100.000 TL)
                            if 10 <= value <= 100000:
                                # Daha akıllı fiyat seçimi: çok büyük fiyatları öncelikle reddet
                                if price_value is None:
                                    price_value = value
                                elif value < price_value and value > 50:  # Daha küçük ama makul fiyatı tercih et
                                    price_value = value
                                elif price_value > 10000 and value < 5000:  # Çok büyük fiyat varsa küçüğü tercih et
                                    price_value = value
                        except ValueError:
                            continue

                    if price_value:
                        break

            if price_value:
                logger.info(f"Çıkarılan fiyat: {price_value}")
                return price_value
            else:
                logger.warning(f"Geçerli fiyat bulunamadı: '{price_text[:50]}...'")
                return 0.0

        return 0.0

    except Exception as e:
        logger.error(f"Fiyat çıkarılırken hata: {e}")
        return 0.0

def get_product_links() -> List[Dict]:
    """
    Veritabanından tüm ürün linklerini çeker

    Returns:
        List[Dict]: Ürün bilgileri ve linkleri içeren liste
    """
    try:
        product_suppliers = get_all_product_suppliers()

        logger.info(f"Toplam {len(product_suppliers)} ürün linki bulundu")

        # Sonuçları yazdır
        for item in product_suppliers:
            print(f"ID: {item['id']}")
            print(f"Ürün ID: {item['product_id']}")
            print(f"Ürün Adı: {item['product_name']}")
            print(f"SKU: {item['sku']}")
            print(f"Supplier URL: {item['supplier_product_url']}")
            print(f"Mevcut Fiyat: {item['supplier_price']}")
            print("-" * 50)

        return product_suppliers

    except Exception as e:
        logger.error(f"Ürün linkleri çekilirken hata oluştu: {e}")
        return []

def get_product_links_by_supplier(supplier_name: str) -> List[Dict]:
    """
    Belirli bir tedarikçiye ait ürün linklerini çeker

    Args:
        supplier_name (str): Tedarikçi adı

    Returns:
        List[Dict]: Tedarikçiye ait ürün linkleri
    """
    try:
        conn = get_db_connection()
        result = conn.execute('''
            SELECT ps.id, ps.product_id, ps.supplier_product_url
            FROM product_suppliers ps
            JOIN suppliers s ON ps.supplier_id = s.id
            WHERE s.name = ? AND ps.supplier_product_url IS NOT NULL
        ''', (supplier_name,)).fetchall()
        
        links = [dict(row) for row in result]
        conn.close()

        logger.info(f"{supplier_name} için {len(links)} ürün linki bulundu")

        # Sonuçları yazdır
        for link in links:
            print(f"PS ID: {link['id']}")
            print(f"Ürün ID: {link['product_id']}")
            print(f"URL: {link['supplier_product_url']}")
            print("-" * 30)

        return links

    except Exception as e:
        logger.error(f"{supplier_name} için ürün linkleri çekilirken hata: {e}")
        return []

def show_price_history_summary():
    """Fiyat geçmişi özetini göster"""
    try:
        # Son 7 günün fiyat uyarıları
        alerts = get_price_alerts(5.0)  # %5 üzeri değişimler

        if alerts:
            print(f"\n🚨 Son 7 günün önemli fiyat değişimleri:")
            print("-" * 60)

            for alert in alerts[:5]:  # İlk 5 uyarı
                change_icon = "📈" if alert['price_change'] > 0 else "📉"
                print(f"{change_icon} {alert['product_name'][:30]:<30} | {alert['supplier_name']:<12}")
                print(f"   {alert['old_price']:.2f} TL → {alert['price']:.2f} TL ({alert['price_change_percent']:+.1f}%)")

            if len(alerts) > 5:
                print(f"   ... ve {len(alerts)-5} değişim daha")
        else:
            print("\n✅ Son 7 günde önemli fiyat değişimi yok")

    except Exception as e:
        logger.error(f"Fiyat geçmişi özeti gösterilirken hata: {e}")

def main():
    """Ana fonksiyon"""
    print("=== 🛒 PAZARYERI FİYAT TAKİP SİSTEMİ ===\n")

    # Fiyat geçmişi özeti
    show_price_history_summary()

    # Tüm ürün linklerini çek
    print("\n1. 📦 Tüm ürün linkleri çekiliyor...")
    all_links = get_product_links()

    if not all_links:
        print("❌ Hiç ürün linki bulunamadı!")
        return

    print(f"\nToplam {len(all_links)} ürün linki bulundu.\n")

    # Chrome seçenekleri
    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')
    chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36')

    successful_extractions = 0
    failed_extractions = 0
    browser = None

    try:
        print("🌐 Browser başlatılıyor...")
        browser = webdriver.Chrome(options=chrome_options)
        browser.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        for i, link in enumerate(all_links, 1):
            url = link['supplier_product_url']
            print(f"\n📍 {i}/{len(all_links)} - İşleniyor:")
            print(f"🏷️  Ürün: {link['product_name'][:50]}...")
            print(f"🔗 URL: {url}")

            try:
                print("📄 Sayfa yükleniyor...")
                browser.get(url)
                time.sleep(3)  # Sayfanın yüklenmesini bekle

                html = browser.page_source
                price = extract_price(html, url)

                if price > 0:
                    print(f"✅ Fiyat bulundu: {price} TL")
                    successful_extractions += 1

                    # Veritabanını güncelle
                    update_supplier_price(link['id'], price)
                    print(f"💾 Veritabanı güncellendi (ID: {link['id']})")

                    # Ürün fiyat istatistiklerini güncelle
                    product_id = link['product_id']
                    update_product_price_statistics(product_id)
                    print(f"📊 Ürün fiyat istatistikleri güncellendi (Product ID: {product_id})")

                else:
                    print("❌ Fiyat çıkarılamadı")
                    failed_extractions += 1

            except Exception as e:
                logger.error(f"Link işlenirken hata: {e}")
                print(f"❌ Hata: {e}")
                failed_extractions += 1

            print("-" * 80)

            # Siteler arası nezaket bekleme süresi
            if i < len(all_links):
                print("⏳ Sonraki link için bekleniyor...")
                time.sleep(2)

    except Exception as e:
        logger.error(f"Browser başlatılırken hata: {e}")
        print(f"❌ Browser hatası: {e}")

    finally:
        if browser:
            browser.quit()
            print("🔒 Browser kapatıldı.")

    # Özet rapor
    print(f"\n📊 ÖZET RAPOR:")
    print(f"✅ Başarılı: {successful_extractions}")
    print(f"❌ Başarısız: {failed_extractions}")
    if len(all_links) > 0:
        print(f"📈 Başarı oranı: {(successful_extractions/len(all_links)*100):.1f}%")

if __name__ == "__main__":
    main()
