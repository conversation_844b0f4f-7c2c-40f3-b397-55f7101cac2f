import sqlite3
from datetime import datetime

def create_database():
    conn = sqlite3.connect('pazaryeri.db')
    cursor = conn.cursor()
    
    # Enable foreign keys
    cursor.execute('PRAGMA foreign_keys = ON')
    
    # Users Table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL UNIQUE,
        full_name TEXT,
        email TEXT
    )
    ''')
    
    # Warehouses Table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS warehouses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        location TEXT
    )
    ''')
    
    # Main Products Table (revised structure)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        sku TEXT NOT NULL,
        model TEXT,
        brand TEXT,
        barcode TEXT,
        product_name TEXT,
        desi REAL DEFAULT 0,
        stock_multiplier INTEGER DEFAULT 1,
        stock_multiplier_main_product TEXT,
        profit_margin REAL,
        commission_rate REAL,
        user_id INTEGER,
        warehouse_id INTEGER,
        gtip TEXT,
        min_price DECIMAL(10,2),
        max_price DECIMAL(10,2),
        avg_price DECIMAL(10,2),
        price_updated_at TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE SET NULL
    )
    ''')
    
    # Suppliers Table (Tedarikçi Şirketleri)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS suppliers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        website TEXT,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # Product-Suppliers Junction Table (Ürün-Tedarikçi İlişkileri)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS product_suppliers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        supplier_id INTEGER NOT NULL,
        supplier_product_url TEXT,
        supplier_price REAL,
        last_checked TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id) ON DELETE CASCADE,
        UNIQUE(product_id, supplier_id)
    )
    ''')
    
    # Product Images Table (simplified)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS product_images (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        image_path TEXT NOT NULL,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
    )
    ''')
    
    # Keep existing tables for marketplace integration
    # API Sites table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS api_sites (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        base_url TEXT NOT NULL,
        client_id TEXT,
        api_key TEXT,
        api_secret TEXT,
        access_token TEXT,
        refresh_token TEXT,
        token_expires_at TIMESTAMP,
        rate_limit INTEGER DEFAULT 100,
        rate_limit_window INTEGER DEFAULT 3600,
        is_active BOOLEAN DEFAULT 1,
        marketplace_type TEXT,
        country_code TEXT,
        additional_shipping_cost DECIMAL(10,2) DEFAULT 0,
        additional_commission_rate DECIMAL(5,2) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # Marketplace Products table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS marketplace_products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        marketplace_id INTEGER NOT NULL,
        external_product_id TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT,
        brand TEXT,
        category TEXT,
        price REAL,
        currency TEXT DEFAULT 'USD',
        availability TEXT,
        condition_type TEXT DEFAULT 'new',
        images TEXT,
        attributes TEXT,
        seller_sku TEXT,
        asin TEXT,
        upc TEXT,
        ean TEXT,
        isbn TEXT,
        model_number TEXT,
        manufacturer TEXT,
        package_dimensions TEXT,
        item_dimensions TEXT,
        weight REAL,
        last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (marketplace_id) REFERENCES api_sites (id),
        UNIQUE(marketplace_id, external_product_id)
    )
    ''')
    
    # Product Mappings table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS product_mappings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        local_product_id INTEGER NOT NULL,
        marketplace_product_id INTEGER NOT NULL,
        mapping_confidence REAL DEFAULT 0.0,
        mapping_type TEXT DEFAULT 'manual',
        mapping_status TEXT DEFAULT 'active',
        notes TEXT,
        created_by INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (local_product_id) REFERENCES products (id),
        FOREIGN KEY (marketplace_product_id) REFERENCES marketplace_products (id),
        FOREIGN KEY (created_by) REFERENCES users (id),
        UNIQUE(local_product_id, marketplace_product_id)
    )
    ''')
    
    # API Sync Logs table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS api_sync_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        marketplace_id INTEGER NOT NULL,
        sync_type TEXT NOT NULL,
        status TEXT NOT NULL,
        records_processed INTEGER DEFAULT 0,
        records_success INTEGER DEFAULT 0,
        records_failed INTEGER DEFAULT 0,
        error_message TEXT,
        sync_duration REAL,
        started_at TIMESTAMP,
        completed_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (marketplace_id) REFERENCES api_sites (id)
    )
    ''')
    
    # Categories table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        marketplace_id INTEGER,
        category_id TEXT NOT NULL,
        category_name TEXT NOT NULL,
        parent_category_id TEXT,
        category_path TEXT,
        level INTEGER DEFAULT 0,
        is_leaf BOOLEAN DEFAULT 0,
        attributes_schema TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (marketplace_id) REFERENCES api_sites (id),
        UNIQUE(marketplace_id, category_id)
    )
    ''')
    
    # AI Providers table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS ai_providers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL UNIQUE,
        base_url TEXT NOT NULL,
        api_key TEXT,
        model TEXT,
        is_active BOOLEAN DEFAULT 1,
        is_free BOOLEAN DEFAULT 0,
        rate_limit INTEGER DEFAULT 100,
        rate_limit_window INTEGER DEFAULT 3600,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    # Marketplace Upload Logs table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS marketplace_upload_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        marketplace_id INTEGER NOT NULL,
        status TEXT DEFAULT 'pending', -- pending, uploading, success, failed
        marketplace_product_id TEXT,
        request_data TEXT,
        response_data TEXT,
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id),
        FOREIGN KEY (marketplace_id) REFERENCES api_sites(id)
    )
    ''')
    
    # Orders table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        marketplace_id INTEGER NOT NULL,
        marketplace_order_id TEXT NOT NULL,
        order_number TEXT,
        order_status TEXT DEFAULT 'pending', -- pending, confirmed, processing, shipped, delivered, cancelled, refunded
        payment_status TEXT DEFAULT 'pending', -- pending, paid, failed, refunded
        total_amount DECIMAL(10,2),
        currency TEXT DEFAULT 'USD',
        customer_name TEXT,
        customer_email TEXT,
        customer_phone TEXT,
        shipping_address TEXT,
        billing_address TEXT,
        tracking_number TEXT,
        shipping_method TEXT,
        order_date TIMESTAMP,
        shipped_date TIMESTAMP,
        delivered_date TIMESTAMP,
        notes TEXT,
        marketplace_data TEXT, -- JSON with full marketplace response
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (marketplace_id) REFERENCES api_sites(id),
        UNIQUE(marketplace_id, marketplace_order_id)
    )
    ''')
    
    # Order Items table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS order_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        product_id INTEGER,
        marketplace_product_id INTEGER,
        sku TEXT,
        product_name TEXT,
        quantity INTEGER,
        unit_price DECIMAL(10,2),
        total_price DECIMAL(10,2),
        marketplace_item_data TEXT, -- JSON
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id),
        FOREIGN KEY (marketplace_product_id) REFERENCES marketplace_products(id)
    )
    ''')
    
    # Order Status History table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS order_status_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        order_id INTEGER NOT NULL,
        old_status TEXT,
        new_status TEXT,
        changed_by TEXT DEFAULT 'system',
        change_reason TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE
    )
    ''')
    
    # Settings table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT NOT NULL UNIQUE,
        value TEXT,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')
    
    conn.commit()
    conn.close()

def add_sample_data():
    conn = sqlite3.connect('pazaryeri.db')
    cursor = conn.cursor()
    
    # Add sample users
    sample_users = [
        ('admin', 'Admin User', '<EMAIL>'),
        ('demo', 'Demo User', '<EMAIL>')
    ]
    
    for user in sample_users:
        cursor.execute('''
        INSERT OR IGNORE INTO users (username, full_name, email)
        VALUES (?, ?, ?)
        ''', user)
    
    # Add sample warehouses
    sample_warehouses = [
        ('Ana Depo', 'İstanbul, Türkiye'),
        ('Avrupa Depo', 'Berlin, Almanya'),
        ('FBA Amazon', 'Amazon FBA')
    ]
    
    for warehouse in sample_warehouses:
        cursor.execute('''
        INSERT OR IGNORE INTO warehouses (name, location)
        VALUES (?, ?)
        ''', warehouse)
    
    # Add sample products with new structure
    sample_products = [
        ('IPH15PRO128', 'iPhone 15 Pro', 'Apple', '194253945123', 'Apple iPhone 15 Pro 128GB', 
         0.5, 1, None, 15.0, 8.0, 1, 1, '8517120000'),
        ('SMGS24256', 'Galaxy S24', 'Samsung', '8806095467123', 'Samsung Galaxy S24 256GB', 
         0.4, 1, None, 18.0, 8.0, 1, 1, '8517120000'),
        ('SONYWH1000XM5', 'WH-1000XM5', 'Sony', '4548736134171', 'Sony WH-1000XM5 Kulaklık', 
         0.8, 1, None, 25.0, 10.0, 2, 1, '8518300000'),
        ('DYSV15DET', 'V15 Detect', 'Dyson', '5025155044148', 'Dyson V15 Detect Süpürge', 
         2.5, 1, None, 20.0, 12.0, 2, 2, '8508110000'),
        ('NESPVERTUO', 'Vertuo', 'Nespresso', '7630054671234', 'Nespresso Vertuo Kahve Makinesi', 
         1.2, 1, None, 30.0, 15.0, 1, 1, '8516710000')
    ]
    
    for product in sample_products:
        cursor.execute('''
        INSERT OR IGNORE INTO products 
        (sku, model, brand, barcode, product_name, desi, stock_multiplier, 
         stock_multiplier_main_product, profit_margin, commission_rate, user_id, warehouse_id, gtip)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', product)
    
    # Add sample suppliers (Tedarikçi Şirketleri)
    sample_suppliers = [
        ('Hepsiburada', 'https://www.hepsiburada.com', 'Türkiye\'nin lider e-ticaret platformu'),
        ('Trendyol', 'https://www.trendyol.com', 'Online alışveriş platformu'),
        ('N11', 'https://www.n11.com', 'Açık pazar yeri platformu'),
        ('Amazon TR', 'https://www.amazon.com.tr', 'Global e-ticaret devi'),
        ('Teknosa', 'https://www.teknosa.com', 'Teknoloji perakende zinciri'),
        ('MediaMarkt', 'https://www.mediamarkt.com.tr', 'Elektronik perakende zinciri'),
        ('Vatan Bilgisayar', 'https://www.vatanbilgisayar.com', 'Bilgisayar ve teknoloji mağazası'),
        ('GittiGidiyor', 'https://www.gittigidiyor.com', 'eBay Türkiye'),
        ('Çiçeksepeti', 'https://www.ciceksepeti.com', 'Online alışveriş platformu'),
        ('Morhipo', 'https://www.morhipo.com', 'Moda ve yaşam platformu')
    ]
    
    for supplier in sample_suppliers:
        cursor.execute('''
        INSERT OR IGNORE INTO suppliers (name, website, description)
        VALUES (?, ?, ?)
        ''', supplier)
    
    # Add sample product-supplier relationships
    product_suppliers = [
        (1, 1, 'https://www.hepsiburada.com/iphone-15-pro-p-12345', 45999.90),  # iPhone - Hepsiburada
        (1, 2, 'https://www.trendyol.com/apple/iphone-15-pro-p-67890', 44999.00),  # iPhone - Trendyol
        (2, 5, 'https://www.teknosa.com/samsung-galaxy-s24-p-11111', 42999.00),  # Samsung - Teknosa
        (3, 6, 'https://www.mediamarkt.com.tr/sony-wh1000xm5-p-22222', 11999.00),  # Sony - MediaMarkt
        (4, 7, 'https://www.vatanbilgisayar.com/dyson-v15-p-33333', 24999.00),  # Dyson - Vatan
        (5, 4, 'https://www.amazon.com.tr/nespresso-vertuo-p-44444', 8999.00),  # Nespresso - Amazon
    ]
    
    for ps in product_suppliers:
        cursor.execute('''
        INSERT OR IGNORE INTO product_suppliers (product_id, supplier_id, supplier_product_url, supplier_price)
        VALUES (?, ?, ?, ?)
        ''', ps)
    
    conn.commit()
    conn.close()

def migrate_from_old_database():
    """Migrate data from old database structure to new one"""
    try:
        old_conn = sqlite3.connect('pazaryeri_backup.db')
        new_conn = sqlite3.connect('pazaryeri.db')
        
        old_cursor = old_conn.cursor()
        new_cursor = new_conn.cursor()
        
        # Create default user and warehouse for migration
        new_cursor.execute("INSERT OR IGNORE INTO users (username, full_name) VALUES ('default', 'Default User')")
        new_cursor.execute("INSERT OR IGNORE INTO warehouses (name, location) VALUES ('Default Warehouse', 'Unknown')")
        
        default_user_id = new_cursor.execute("SELECT id FROM users WHERE username = 'default'").fetchone()[0]
        default_warehouse_id = new_cursor.execute("SELECT id FROM warehouses WHERE name = 'Default Warehouse'").fetchone()[0]
        
        # Migrate products
        old_products = old_cursor.execute("SELECT * FROM products").fetchall()
        for product in old_products:
            # Map old fields to new structure
            new_cursor.execute('''
            INSERT OR IGNORE INTO products 
            (id, sku, product_name, user_id, warehouse_id, profit_margin, commission_rate)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                product[0],  # id
                product[2] if product[2] else f'SKU{product[0]}',  # sku (use old sku or generate)
                product[1],  # name -> product_name
                default_user_id,
                default_warehouse_id,
                15.0,  # default profit margin
                8.0    # default commission rate
            ))
        
        # Migrate API sites
        try:
            old_api_sites = old_cursor.execute("SELECT * FROM api_sites").fetchall()
            for site in old_api_sites:
                new_cursor.execute('''
                INSERT OR IGNORE INTO api_sites 
                (name, base_url, client_id, api_key, api_secret, access_token, refresh_token,
                 token_expires_at, rate_limit, rate_limit_window, is_active, marketplace_type, country_code)
                SELECT name, base_url, client_id, api_key, api_secret, access_token, refresh_token,
                       token_expires_at, rate_limit, rate_limit_window, is_active, marketplace_type, country_code
                FROM api_sites WHERE name = ?
                ''', (site[1],))
        except:
            pass
        
        new_conn.commit()
        old_conn.close()
        new_conn.close()
        
        print("Migration completed successfully!")
        return True
        
    except Exception as e:
        print(f"Migration error: {e}")
        return False

def add_api_sites():
    conn = sqlite3.connect('pazaryeri.db')
    cursor = conn.cursor()
    
    api_sites = [
        ('Amazon', 'https://api.amazon.com', None, None, None, None, None, None, 30, 3600, 1, 'marketplace', 'US'),
        ('Amazon Turkey', 'https://api.amazon.com.tr', None, None, None, None, None, None, 30, 3600, 1, 'marketplace', 'TR'),
        ('Ozon', 'https://api-seller.ozon.ru', None, None, None, None, None, None, 100, 3600, 1, 'marketplace', 'RU'),
        ('eBay', 'https://api.ebay.com', None, None, None, None, None, None, 5000, 86400, 1, 'marketplace', 'US'),
        ('Alibaba', 'https://gw.api.alibaba.com', None, None, None, None, None, None, 100, 3600, 1, 'b2b', 'CN'),
        ('AliExpress', 'https://api.aliexpress.com', None, None, None, None, None, None, 100, 3600, 1, 'marketplace', 'CN'),
        ('Walmart', 'https://marketplace.walmartapis.com', None, None, None, None, None, None, 100, 3600, 1, 'marketplace', 'US'),
        ('Etsy', 'https://openapi.etsy.com', None, None, None, None, None, None, 10, 1, 1, 'marketplace', 'US'),
        ('Hepsiburada', 'https://api.hepsiburada.com', None, None, None, None, None, None, 100, 3600, 1, 'marketplace', 'TR'),
        ('Trendyol', 'https://api.trendyol.com', None, None, None, None, None, None, 100, 3600, 1, 'marketplace', 'TR'),
        ('N11', 'https://api.n11.com', None, None, None, None, None, None, 100, 3600, 1, 'marketplace', 'TR'),
        ('GittiGidiyor', 'https://api.gittigidiyor.com', None, None, None, None, None, None, 100, 3600, 1, 'marketplace', 'TR')
    ]
    
    for site in api_sites:
        try:
            cursor.execute('''
            INSERT OR IGNORE INTO api_sites 
            (name, base_url, client_id, api_key, api_secret, access_token, refresh_token, 
             token_expires_at, rate_limit, rate_limit_window, is_active, marketplace_type, country_code)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', site)
        except Exception as e:
            print(f"Error adding {site[0]}: {e}")
    
    conn.commit()
    conn.close()

def add_default_ai_providers():
    conn = sqlite3.connect('pazaryeri.db')
    cursor = conn.cursor()
    
    ai_providers = [
        ('OpenRouter', 'https://openrouter.ai/api/v1', None, 'mistralai/mistral-7b-instruct', 1, 1, 100, 3600),
        ('Mistral AI', 'https://api.mistral.ai/v1', None, 'mistral-tiny', 1, 1, 100, 3600),
        ('Groq', 'https://api.groq.com/openai/v1', None, 'mixtral-8x7b-32768', 1, 1, 100, 3600),
        ('Google Gemini', 'https://generativelanguage.googleapis.com/v1beta', None, 'gemini-pro', 1, 1, 100, 3600),
    ]
    
    for provider in ai_providers:
        try:
            cursor.execute('''
            INSERT OR IGNORE INTO ai_providers 
            (name, base_url, api_key, model, is_active, is_free, rate_limit, rate_limit_window)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', provider)
        except Exception as e:
            print(f"Error adding {provider[0]}: {e}")
    
    conn.commit()
    conn.close()

def add_default_settings():
    conn = sqlite3.connect('pazaryeri.db')
    cursor = conn.cursor()
    
    default_settings = [
        ('default_profit_margin', '20', 'Varsayılan kar marjı yüzdesi'),
        ('minimum_profit_margin', '10', 'Minimum kar marjı yüzdesi'),
        ('product_name_prefix', '', 'Ürün tanımı ön eki'),
        ('default_currency', 'TRY', 'Varsayılan para birimi'),
        ('unknown_stock_value_1', '5', 'Stok değeri bilinmeyen ürünler için minimum stok'),
        ('unknown_stock_value_2', '10', 'Stok değeri bilinmeyen ürünler için maksimum stok')
    ]
    
    for key, value, description in default_settings:
        try:
            cursor.execute('''
            INSERT OR IGNORE INTO settings (key, value, description)
            VALUES (?, ?, ?)
            ''', (key, value, description))
        except Exception as e:
            print(f"Error adding setting {key}: {e}")
    
    conn.commit()
    conn.close()

if __name__ == '__main__':
    # First, backup existing database
    import os
    import shutil
    
    if os.path.exists('pazaryeri.db'):
        shutil.copy('pazaryeri.db', 'pazaryeri_backup.db')
        os.remove('pazaryeri.db')
    
    # Create new database structure
    create_database()
    # add_sample_data()  # Commented out - no mock data
    add_api_sites()
    add_default_ai_providers()
    add_default_settings()
    
    # Try to migrate old data
    if os.path.exists('pazaryeri_backup.db'):
        migrate_from_old_database()
    
    print("Database revision completed!")