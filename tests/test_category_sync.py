#!/usr/bin/env python3
"""
Ozon Category Sync Test Script
Bu script Ozon kategori senkronizasyonunu test etmek için kullanılır.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.marketplace.ozon_api_client import OzonAPIClient
import json

def test_category_sync():
    """Ozon kategori senkronizasyonunu test et"""
    
    print("=" * 60)
    print("OZON CATEGORY SYNC TEST")
    print("=" * 60)
    
    try:
        # Ozon API Client oluştur
        ozon_client = OzonAPIClient()
        
        # Test 1: API credentials kontrolü
        print("\n📋 TEST 1: API Credentials Kontrolü")
        print("-" * 40)
        
        credentials = ozon_client.get_credentials()
        if not credentials or not credentials['client_id'] or not credentials['api_key']:
            print("❌ HATA: Ozon API kimlik bilgileri eksik!")
            print("\nLütfen şu adımları izleyin:")
            print("1. Web arayüzünde '/marketplaces' sayfasına gidin")
            print("2. Ozon satırında ayarlar butonuna tıklayın")
            print("3. Client ID ve API Key bilgilerini girin")
            return False
        else:
            print(f"✅ API Credentials bulundu:")
            print(f"   Client ID: {credentials['client_id'][:10]}...")
            print(f"   API Key: {'*' * 10}")
        
        # Test 2: Manuel API kategori çağrısı
        print("\n\n📁 TEST 2: Manuel Kategori API Çağrısı")
        print("-" * 40)
        
        # Description-category endpoint test
        print("\n🔍 Description-category endpoint testi (RU):")
        categories_desc_ru = ozon_client.get_categories(language="RU")
        
        if categories_desc_ru:
            print(f"✅ Description-category/RU response alındı, keys: {list(categories_desc_ru.keys())}")
            if 'result' in categories_desc_ru:
                result = categories_desc_ru['result']
                if isinstance(result, list):
                    print(f"   Kategori sayısı: {len(result)}")
                    if len(result) > 0:
                        print(f"   İlk kategori: {result[0].get('category_name', 'N/A')}")
                        print(f"   İlk kategori ID: {result[0].get('description_category_id', 'N/A')}")
                else:
                    print(f"   Result type: {type(result)}")
            else:
                print("   'result' anahtarı bulunamadı")
        else:
            print("❌ Description-category/RU response alınamadı")
        
        # Description-category endpoint test EN
        print("\n🔍 Description-category endpoint testi (EN):")
        categories_desc_en = ozon_client.get_categories(language="EN")
        
        if categories_desc_en:
            print(f"✅ Description-category/EN response alındı")
            if 'result' in categories_desc_en and isinstance(categories_desc_en['result'], list):
                print(f"   Kategori sayısı: {len(categories_desc_en['result'])}")
        else:
            print("❌ Description-category/EN response alınamadı")
        
        # Legacy v1 endpoint test
        print("\n🔍 Legacy v1/category/tree endpoint testi (RU):")
        categories_legacy_ru = ozon_client.get_categories_legacy(language="RU")
        
        if categories_legacy_ru:
            print(f"✅ Legacy/RU response alındı")
            if 'result' in categories_legacy_ru and isinstance(categories_legacy_ru['result'], list):
                print(f"   Kategori sayısı: {len(categories_legacy_ru['result'])}")
        else:
            print("❌ Legacy/RU response alınamadı")
        
        # Legacy v1 endpoint test EN
        print("\n🔍 Legacy v1/category/tree endpoint testi (EN):")
        categories_legacy_en = ozon_client.get_categories_legacy(language="EN")
        
        if categories_legacy_en:
            print(f"✅ Legacy/EN response alındı")
            if 'result' in categories_legacy_en and isinstance(categories_legacy_en['result'], list):
                print(f"   Kategori sayısı: {len(categories_legacy_en['result'])}")
        else:
            print("❌ Legacy/EN response alınamadı")
        
        # Test 3: Full sync test
        print("\n\n🔄 TEST 3: Full Sync Test")
        print("-" * 40)
        
        success = ozon_client.sync_categories_to_db()
        
        if success:
            print("\n✅ Kategori senkronizasyonu başarılı!")
            
            # Veritabanından kontrol et
            import sqlite3
            conn = sqlite3.connect(ozon_client.db_path)
            
            # Marketplace ID bul
            marketplace = conn.execute(
                "SELECT id FROM marketplaces WHERE name = 'Ozon' AND is_deleted = 0"
            ).fetchone()
            
            if marketplace:
                marketplace_id = marketplace[0]
                
                # Kategori sayısını kontrol et
                category_count = conn.execute(
                    "SELECT COUNT(*) FROM marketplace_categories WHERE marketplace_id = ? AND is_deleted = 0",
                    (marketplace_id,)
                ).fetchone()[0]
                
                print(f"📊 Veritabanında {category_count} Ozon kategorisi bulundu")
                
                # İlk 5 kategoriyi göster
                categories = conn.execute('''
                    SELECT marketplace_category_id, name, level, is_leaf 
                    FROM marketplace_categories 
                    WHERE marketplace_id = ? AND is_deleted = 0 
                    ORDER BY level, name 
                    LIMIT 5
                ''', (marketplace_id,)).fetchall()
                
                print("\n📋 İlk 5 kategori:")
                for cat in categories:
                    print(f"   ID: {cat[0]}, Name: {cat[1]}, Level: {cat[2]}, Leaf: {cat[3]}")
            
            conn.close()
        else:
            print("\n❌ Kategori senkronizasyonu başarısız!")
        
        print("\n" + "=" * 60)
        print("TEST TAMAMLANDI")
        print("=" * 60)
        
        return success
        
    except Exception as e:
        print(f"\n❌ Test hatası: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_category_sync()