#!/usr/bin/env python3
"""
Ozon API Test Script
Bu script Ozon API bağlantısını test etmek için kullanılır.
"""

from ozon_api_client import OzonAPIClient
from api_manager import APIManager
import json

def test_ozon_connection():
    """Ozon API bağlantısını test et"""
    
    print("=" * 50)
    print("OZON API TEST")
    print("=" * 50)
    
    # API Manager ile credentials kontrolü
    api_manager = APIManager()
    credentials = api_manager.get_api_credentials('Ozon')
    
    if not credentials or not credentials['client_id'] or not credentials['api_key']:
        print("\n❌ HATA: Ozon API kimlik bilgileri bulunamadı!")
        print("\nLütfen şu adımları izleyin:")
        print("1. Web arayüzünde 'API Siteleri' sayfasına gidin")
        print("2. Ozon satırında ayarlar butonuna tıklayın")
        print("3. Client ID ve API Key bilgilerini girin")
        print("\nOzon Seller hesabınızdan API bilgilerini alabilirsiniz:")
        print("https://seller.ozon.ru/app/settings/api-keys")
        return
    
    print(f"\n✅ API Kimlik bilgileri bulundu:")
    print(f"   Client ID: {credentials['client_id'][:10]}...")
    print(f"   API Key: {'*' * 10}")
    
    # Ozon API Client oluştur
    ozon_client = OzonAPIClient()
    
    # Test 1: Basit ürün listesi çek
    print("\n\n📋 TEST 1: Ürün Listesi (limit=5)")
    print("-" * 30)
    
    try:
        result = ozon_client.get_product_list(limit=5)
        
        if result:
            print(f"\n✅ API isteği başarılı!")
            print(f"Response keys: {list(result.keys())}")
            
            if 'result' in result and 'items' in result['result']:
                items = result['result']['items']
                print(f"\nBulunan ürün sayısı: {len(items)}")
                
                for i, item in enumerate(items[:3]):  # İlk 3 ürünü göster
                    print(f"\nÜrün {i+1}:")
                    print(f"  ID: {item.get('product_id', 'N/A')}")
                    print(f"  SKU: {item.get('offer_id', 'N/A')}")
                    print(f"  İsim: {item.get('name', 'N/A')}")
                    print(f"  Fiyat: {item.get('price', 'N/A')}")
                    print(f"  Durum: {item.get('state', 'N/A')}")
            else:
                print("\n⚠️ Beklenmeyen response formatı:")
                print(json.dumps(result, indent=2, ensure_ascii=False)[:500])
        else:
            print("\n❌ API isteği başarısız!")
            
    except Exception as e:
        print(f"\n❌ Hata oluştu: {e}")
    
    # Test 2: Kategori listesi
    print("\n\n📁 TEST 2: Kategori Listesi")
    print("-" * 30)
    
    try:
        result = ozon_client.get_categories()
        
        if result:
            print(f"\n✅ Kategori isteği başarılı!")
            print(f"Response keys: {list(result.keys())}")
            
            if 'result' in result:
                categories = result['result']
                if isinstance(categories, list):
                    print(f"\nBulunan kategori sayısı: {len(categories)}")
                    for cat in categories[:3]:  # İlk 3 kategori
                        print(f"  - {cat.get('category_name', 'N/A')} (ID: {cat.get('description_category_id', 'N/A')})")
        else:
            print("\n❌ Kategori isteği başarısız!")
            
    except Exception as e:
        print(f"\n❌ Hata oluştu: {e}")
    
    # Test 3: Sync testi
    print("\n\n🔄 TEST 3: Mini Sync (limit=2)")
    print("-" * 30)
    
    try:
        success = ozon_client.sync_products_to_db(limit=2)
        
        if success:
            print("\n✅ Sync başarılı!")
        else:
            print("\n❌ Sync başarısız!")
            
    except Exception as e:
        print(f"\n❌ Sync hatası: {e}")
    
    print("\n" + "=" * 50)
    print("TEST TAMAMLANDI")
    print("=" * 50)

if __name__ == '__main__':
    test_ozon_connection()