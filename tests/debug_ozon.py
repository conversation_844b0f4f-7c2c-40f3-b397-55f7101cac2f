#!/usr/bin/env python3
"""
Ozon API Debug Script
"""

import requests
import json
from api_manager import APIManager

def debug_ozon():
    # API bilgilerini al
    api_manager = APIManager()
    creds = api_manager.get_api_credentials('Ozon')
    
    print("=" * 60)
    print("OZON API DEBUG")
    print("=" * 60)
    
    if not creds:
        print("❌ Credentials bulunamadı!")
        return
        
    print(f"Client ID: {creds['client_id']}")
    print(f"API Key uzunluğu: {len(creds['api_key']) if creds['api_key'] else 0} karakter")
    print(f"API Key ilk 10 karakter: {creds['api_key'][:10] if creds['api_key'] else 'None'}...")
    
    # Farklı header kombinasyonları dene
    base_headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    
    header_tests = [
        {
            "name": "Standard Headers",
            "headers": {
                **base_headers,
                'Client-Id': creds['client_id'],
                'Api-Key': creds['api_key']
            }
        },
        {
            "name": "With User-Agent",
            "headers": {
                **base_headers,
                'Client-Id': creds['client_id'],
                'Api-Key': creds['api_key'],
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        },
        {
            "name": "Lowercase headers",
            "headers": {
                **base_headers,
                'client-id': creds['client_id'],
                'api-key': creds['api_key']
            }
        }
    ]
    
    # Test edilecek endpoint'ler - Ozon'un güncel dokümantasyonundan
    endpoints = [
        {
            "name": "Product List (v2)",
            "url": "https://api-seller.ozon.ru/v2/product/list",
            "data": {
                "filter": {
                    "visibility": "ALL"
                },
                "limit": 1
            }
        },
        {
            "name": "Product List (v3)",
            "url": "https://api-seller.ozon.ru/v3/product/list",
            "data": {
                "filter": {
                    "visibility": "ALL"
                },
                "limit": 1
            }
        },
        {
            "name": "Product Info List",
            "url": "https://api-seller.ozon.ru/v2/product/info/list",
            "data": {
                "product_id": [],
                "sku": []
            }
        },
        {
            "name": "Category Tree", 
            "url": "https://api-seller.ozon.ru/v2/category/tree",
            "data": {}
        },
        {
            "name": "Posting FBO List",
            "url": "https://api-seller.ozon.ru/v2/posting/fbo/list",
            "data": {
                "dir": "asc",
                "filter": {
                    "since": "2024-01-01T00:00:00Z",
                    "to": "2024-12-31T23:59:59Z"
                },
                "limit": 1,
                "offset": 0
            }
        }
    ]
    
    print("\n" + "=" * 60)
    print("ENDPOINT TESTLERİ")
    print("=" * 60)
    
    for header_test in header_tests[:1]:  # İlk önce standard headers dene
        print(f"\n📋 Header Test: {header_test['name']}")
        headers = header_test['headers']
        
        for endpoint in endpoints:
            print(f"\n🔍 Endpoint: {endpoint['name']}")
            print(f"   URL: {endpoint['url']}")
            
            try:
                response = requests.post(
                    endpoint['url'],
                    headers=headers,
                    json=endpoint['data'],
                    timeout=15
                )
                
                print(f"   Status: {response.status_code}")
                print(f"   Headers gönderilen: {json.dumps(headers, indent=2)}")
                
                if response.status_code == 200:
                    print("   ✅ BAŞARILI!")
                    data = response.json()
                    print(f"   Response keys: {list(data.keys())}")
                    if 'result' in data:
                        print(f"   Result type: {type(data['result'])}")
                        
                elif response.status_code == 400:
                    print("   ❌ Bad Request")
                    print(f"   Response: {response.text}")
                    
                elif response.status_code == 401:
                    print("   ❌ Authentication Error")
                    print(f"   Response: {response.text}")
                    
                elif response.status_code == 403:
                    print("   ❌ Forbidden - API key yetkisi yok veya yanlış")
                    print(f"   Response: {response.text}")
                    
                elif response.status_code == 404:
                    print("   ❌ Not Found")
                    if response.text:
                        print(f"   Response: {response.text[:200]}")
                        
                else:
                    print(f"   ⚠️ Diğer: {response.status_code}")
                    print(f"   Response: {response.text[:200]}")
                    
            except Exception as e:
                print(f"   ❌ Exception: {e}")
    
    # CURL komutu oluştur (manuel test için)
    print("\n" + "=" * 60)
    print("MANUEL TEST İÇİN CURL KOMUTU")
    print("=" * 60)
    print("\nAşağıdaki komutu terminal'de deneyin:\n")
    
    curl_cmd = f'''curl -X POST https://api-seller.ozon.ru/v2/product/list \\
  -H "Client-Id: {creds['client_id']}" \\
  -H "Api-Key: {creds['api_key']}" \\
  -H "Content-Type: application/json" \\
  -d '{{"filter": {{"visibility": "ALL"}}, "limit": 1}}'
'''
    print(curl_cmd)
    
    print("\n" + "=" * 60)
    print("OLASI SORUNLAR")
    print("=" * 60)
    print("\n1. API Key Türü:")
    print("   - Ozon'da farklı API key türleri var (Statistics API, Seller API, vb.)")
    print("   - Seller API key'i kullandığınızdan emin olun")
    print("\n2. API Key Yetkileri:")
    print("   - API key oluştururken tüm yetkileri verdiğinizden emin olun")
    print("   - Özellikle 'Products and prices' yetkisi olmalı")
    print("\n3. Hesap Durumu:")
    print("   - Ozon seller hesabınızın aktif olduğundan emin olun")
    print("   - Hesabınızda ürün olması gerekebilir")
    print("\n4. API Endpoint:")
    print("   - Ozon API'si sürekli güncelleniyor")
    print("   - Güncel dokümantasyon: https://docs.ozon.ru/api/seller")

if __name__ == '__main__':
    debug_ozon()