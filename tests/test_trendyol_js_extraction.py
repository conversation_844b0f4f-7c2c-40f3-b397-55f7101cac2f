#!/usr/bin/env python3
"""
Test script to verify Trendyol JavaScript variable extraction
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
import json
import re
from app.services.selenium_helper import SeleniumHelper

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_js_extraction():
    """Test extraction of product data from Trendyol JavaScript variables"""
    
    test_url = "https://www.trendyol.com/hikvision/ds-2de1c200iw-d3-w-f1-s7-2mp-2-8mm-wi-fi-pan-tilt-mini-dome-ip-kamera-15mt-sesli-p-840359514?boutiqueId=61&merchantId=406384"
    
    print("\n" + "="*80)
    print("Testing Trendyol JavaScript Variable Extraction")
    print("="*80)
    
    selenium_helper = SeleniumHelper()
    
    try:
        # Initialize driver
        selenium_helper._init_driver()
        
        # Load page
        print(f"Loading page: {test_url}")
        selenium_helper.driver.get(test_url)
        
        # Wait for page to load
        import time
        time.sleep(5)
        
        # Try to extract JavaScript variables
        js_code = """
        return {
            hasProductDetailState: typeof window.__PRODUCT_DETAIL_APP_INITIAL_STATE__ !== 'undefined',
            hasProductDetail: typeof window.__PRODUCT_DETAIL__ !== 'undefined',
            hasProductDetailApp: typeof window.__PRODUCT_DETAIL_APP__ !== 'undefined',
            availableVars: Object.keys(window).filter(key => key.includes('PRODUCT'))
        };
        """
        
        js_vars = selenium_helper.driver.execute_script(js_code)
        print("\nJavaScript Variables Found:")
        print(json.dumps(js_vars, indent=2))
        
        # Try to extract product state
        if js_vars.get('hasProductDetailState'):
            state_js = """
            try {
                const state = window.__PRODUCT_DETAIL_APP_INITIAL_STATE__;
                return {
                    hasProduct: !!state.product,
                    productKeys: state.product ? Object.keys(state.product) : [],
                    hasDescription: !!(state.product && state.product.description),
                    descriptionLength: state.product && state.product.description ? state.product.description.length : 0
                };
            } catch (e) {
                return { error: e.toString() };
            }
            """
            
            state_info = selenium_helper.driver.execute_script(state_js)
            print("\nProduct State Information:")
            print(json.dumps(state_info, indent=2))
            
            # If description exists, try to get it
            if state_info.get('hasDescription'):
                desc_js = """
                try {
                    return window.__PRODUCT_DETAIL_APP_INITIAL_STATE__.product.description;
                } catch (e) {
                    return null;
                }
                """
                description = selenium_helper.driver.execute_script(desc_js)
                if description:
                    print(f"\n✅ Found description in JavaScript variable!")
                    print(f"Length: {len(description)} characters")
                    print(f"Preview: {description[:500]}...")
        
        # Also check page source for these variables
        page_source = selenium_helper.driver.page_source
        
        # Look for window.__PRODUCT patterns
        product_patterns = [
            r'window\.__PRODUCT_DETAIL_APP_INITIAL_STATE__\s*=\s*({[^;]+});',
            r'window\.__PRODUCT_DETAIL__\s*=\s*({[^;]+});',
            r'window\.__PRODUCT_DETAIL_APP__\s*=\s*({[^;]+});'
        ]
        
        print("\nSearching in page source for product data...")
        for pattern in product_patterns:
            matches = re.findall(pattern, page_source, re.DOTALL)
            if matches:
                print(f"Found {len(matches)} match(es) for pattern: {pattern[:50]}...")
                
        # Test the enhanced description extraction
        print("\n" + "-"*80)
        print("Testing Enhanced Description Extraction")
        print("-"*80)
        
        description = selenium_helper.extract_trendyol_description(test_url)
        
        if description:
            print(f"✅ Successfully extracted description!")
            print(f"Length: {len(description)} characters")
            print(f"Preview:\n{description[:800]}...")
        else:
            print("❌ Failed to extract description")
            
    finally:
        selenium_helper.close()
    
    print("\n" + "="*80)
    print("Test completed")
    print("="*80)

if __name__ == "__main__":
    test_js_extraction()