#!/usr/bin/env python3
"""
Update Ozon API credentials in new database
"""

from api_manager_v2 import APIManager

def update_ozon_credentials():
    manager = APIManager()
    
    # Get Ozon site ID
    conn = manager.get_db_connection()
    ozon = conn.execute("SELECT id FROM api_sites WHERE name = 'Ozon'").fetchone()
    conn.close()
    
    if ozon:
        # Update with the known Ozon credentials
        manager.update_api_credentials(
            site_id=ozon['id'],
            client_id="1",  # From previous conversation
            api_key="0143ad5e-2eea-429b-a21d-f7b5f9fd8d88"  # From previous conversation
        )
        print(f"✅ Ozon API credentials updated successfully!")
    else:
        print("❌ Ozon site not found in database")

if __name__ == '__main__':
    update_ozon_credentials()