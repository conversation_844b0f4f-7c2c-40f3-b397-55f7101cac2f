#!/usr/bin/env python3
"""
Ozon API tam test - tüm endpoint'leri dene
"""

from ozon_api_client import OzonAPIClient
import json

def test_all():
    client = OzonAPIClient()
    
    # Product ID'leri al
    print("1. Ürün listesi...")
    list_result = client.get_product_list(limit=2)
    
    if not list_result or 'result' not in list_result:
        print("Ürün listesi alınamadı!")
        return
        
    items = list_result['result']['items']
    print(f"   {len(items)} ürün bulundu")
    
    if len(items) == 0:
        print("Ürün yok!")
        return
    
    product_id = items[0]['product_id']
    sku = items[0]['offer_id']
    print(f"   İlk ürün - ID: {product_id}, SKU: {sku}")
    
    # Farklı endpoint'leri test et
    tests = [
        {
            "name": "Product Info by ID",
            "endpoint": "/v2/product/info",
            "data": {"product_id": product_id}
        },
        {
            "name": "Product Info by SKU", 
            "endpoint": "/v2/product/info",
            "data": {"offer_id": sku}
        },
        {
            "name": "Product Info Attributes",
            "endpoint": "/v3/products/info/attributes",
            "data": {
                "filter": {
                    "product_id": [product_id],
                    "visibility": "ALL"
                },
                "limit": 1
            }
        },
        {
            "name": "Product Description",
            "endpoint": "/v1/product/info/description",
            "data": {"product_id": product_id}
        }
    ]
    
    for test in tests:
        print(f"\n2. Test: {test['name']}")
        print(f"   Endpoint: {test['endpoint']}")
        
        result = client.make_request(test['endpoint'], method='POST', data=test['data'])
        
        if result:
            print("   ✅ Başarılı!")
            print(f"   Response: {json.dumps(result, indent=2, ensure_ascii=False)[:500]}...")

if __name__ == '__main__':
    test_all()