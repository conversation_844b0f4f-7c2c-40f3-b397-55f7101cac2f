#!/usr/bin/env python3
"""
Test script to verify Trendyol description extraction improvements
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging
from app.services.product_import_service import ProductImportService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_trendyol_description():
    """Test Trendyol description extraction with the provided URL"""
    
    # Test URL from the user
    test_url = "https://www.trendyol.com/hikvision/ds-2de1c200iw-d3-w-f1-s7-2mp-2-8mm-wi-fi-pan-tilt-mini-dome-ip-kamera-15mt-sesli-p-840359514?boutiqueId=61&merchantId=406384"
    
    print("\n" + "="*80)
    print("Testing Trendyol Description Extraction")
    print("="*80)
    print(f"URL: {test_url}")
    print("="*80 + "\n")
    
    # Initialize the service
    service = ProductImportService()
    
    # Import product data
    result = service.import_product_data(test_url)
    
    if result['success']:
        print("✅ Successfully extracted product data!\n")
        
        data = result['data']
        
        # Display extracted data
        print("📦 Product Information:")
        print(f"   Name: {data.get('product_name', 'Not found')}")
        print(f"   Brand: {data.get('brand', 'Not found')}")
        print(f"   Model: {data.get('model', 'Not found')}")
        print(f"   Price: {data.get('price', 'Not found')}")
        print(f"   Images: {len(data.get('images', []))} found")
        
        print("\n📝 Description:")
        description = data.get('description', 'Not found')
        if description and description != 'Not found':
            # Show first 500 characters of description
            print(f"   Length: {len(description)} characters")
            print(f"   Preview: {description[:500]}...")
            if len(description) > 500:
                print(f"   ... (truncated, {len(description) - 500} more characters)")
        else:
            print("   ❌ Description not found!")
            
        print("\n🔧 Features:")
        features = data.get('features')
        if features:
            print(f"   Found: {features[:200]}...")
        else:
            print("   Not found")
            
    else:
        print(f"❌ Failed to extract product data: {result.get('error', 'Unknown error')}")
    
    # Cleanup
    service.cleanup()
    
    print("\n" + "="*80)
    print("Test completed")
    print("="*80 + "\n")

if __name__ == "__main__":
    test_trendyol_description()