#!/usr/bin/env python3
"""
Ozon API Endpoint Test Script
Farklı endpoint'leri test eder
"""

from ozon_api_client import OzonAPIClient
import requests
import time

def test_endpoints():
    """Farklı Ozon API endpoint'lerini test et"""
    
    ozon_client = OzonAPIClient()
    credentials = ozon_client.get_credentials()
    
    if not credentials or not credentials['client_id'] or not credentials['api_key']:
        print("❌ API credentials eksik!")
        return
    
    headers = {
        'Client-Id': credentials['client_id'],
        'Api-Key': credentials['api_key'],
        'Content-Type': 'application/json'
    }
    
    base_url = "https://api-seller.ozon.ru"
    
    # Test edilecek endpoint'ler
    endpoints = [
        # Ürün listesi için muhtemel endpoint'ler
        ("/v1/product/list", "POST", {"filter": {"visibility": "ALL"}, "limit": 1}),
        ("/v2/product/list", "POST", {"filter": {"visibility": "ALL"}, "limit": 1}),
        ("/v3/product/list", "POST", {"filter": {"visibility": "ALL"}, "limit": 1}),
        ("/v2/products/list", "POST", {"filter": {"visibility": "ALL"}, "limit": 1}),
        ("/v2/product/info/list", "POST", {"limit": 1}),
        ("/v3/products/info/limit/list", "POST", {"limit": 1}),
        
        # Kategori endpoint'leri
        ("/v1/category/tree", "POST", {}),
        ("/v2/category/tree", "POST", {}),
        ("/v1/categories", "GET", None),
        
        # Info endpoint'leri
        ("/v1/product/info", "POST", {}),
        ("/v2/product/info", "POST", {}),
        
        # Seller info
        ("", "GET", None),
        ("/v1/seller/info", "GET", None),
    ]
    
    print("=" * 60)
    print("OZON API ENDPOINT TESTİ")
    print("=" * 60)
    print(f"Base URL: {base_url}")
    print(f"Client ID: {credentials['client_id'][:10]}...")
    print("=" * 60)
    
    for endpoint, method, data in endpoints:
        url = f"{base_url}{endpoint}"
        print(f"\n🔍 Test: {method} {endpoint}")
        
        try:
            if method == "GET":
                response = requests.get(url, headers=headers, timeout=10)
            else:
                response = requests.post(url, headers=headers, json=data, timeout=10)
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ BAŞARILI!")
                result = response.json()
                print(f"   Response keys: {list(result.keys())}")
                if 'result' in result:
                    print(f"   Result type: {type(result['result'])}")
                    if isinstance(result['result'], dict) and 'items' in result['result']:
                        print(f"   Items count: {len(result['result']['items'])}")
            elif response.status_code == 401:
                print(f"   ❌ Authentication hatası")
            elif response.status_code == 403:
                print(f"   ❌ Yetki hatası")
            elif response.status_code == 404:
                print(f"   ❌ Endpoint bulunamadı")
            else:
                print(f"   ⚠️ Diğer: {response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ Hata: {e}")
        
        time.sleep(0.5)  # Rate limiting
    
    print("\n" + "=" * 60)
    print("TEST TAMAMLANDI")
    print("=" * 60)

if __name__ == '__main__':
    test_endpoints()