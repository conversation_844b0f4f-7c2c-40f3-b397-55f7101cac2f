#!/usr/bin/env python3
"""
Ozon fiyat bilgisi kontrolü
"""

from ozon_api_client import OzonAPIClient
import json

def check_prices():
    client = OzonAPIClient()
    
    # Fiyat endpoint'lerini test et
    product_id = 1419810180
    offer_id = "MYM4"
    
    tests = [
        {
            "name": "Price Info",
            "endpoint": "/v4/product/info/prices",
            "data": {
                "filter": {
                    "product_id": [product_id],
                    "visibility": "ALL"
                },
                "limit": 1
            }
        },
        {
            "name": "Stock Info",
            "endpoint": "/v3/product/info/stocks",
            "data": {
                "filter": {
                    "product_id": [product_id],
                    "visibility": "ALL"
                },
                "limit": 1
            }
        },
        {
            "name": "Product Info Discounted",
            "endpoint": "/v2/product/info/discounted",
            "data": {
                "product_id": [product_id],
                "limit": 1
            }
        }
    ]
    
    for test in tests:
        print(f"\n🔍 Test: {test['name']}")
        print(f"   Endpoint: {test['endpoint']}")
        
        result = client.make_request(test['endpoint'], method='POST', data=test['data'])
        
        if result:
            print("   ✅ Başarılı!")
            print(f"   Response: {json.dumps(result, indent=2, ensure_ascii=False)[:800]}...")

if __name__ == '__main__':
    check_prices()