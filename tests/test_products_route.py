#!/usr/bin/env python3
"""Test products route"""

from app import app
import sqlite3

# Test database connection
try:
    conn = sqlite3.connect('pazaryeri.db')
    conn.row_factory = sqlite3.Row
    
    # Check if products table exists
    cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='products'")
    if cursor.fetchone():
        print("✅ Products table exists")
        
        # Count products
        count = conn.execute('SELECT COUNT(*) FROM products').fetchone()[0]
        print(f"✅ Products count: {count}")
        
        # Get sample products
        products = conn.execute('SELECT * FROM products LIMIT 3').fetchall()
        for p in products:
            print(f"  - {dict(p).get('name', 'N/A')}")
    else:
        print("❌ Products table does not exist")
    
    conn.close()
    
    # Test Flask route
    with app.test_client() as client:
        response = client.get('/products')
        print(f"\n✅ Products route status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Products page loads successfully")
        else:
            print(f"❌ Error: {response.data}")
            
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()