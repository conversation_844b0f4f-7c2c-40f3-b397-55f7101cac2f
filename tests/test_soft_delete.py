#!/usr/bin/env python3
"""
Test script for soft delete functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
import sqlite3

def test_soft_delete():
    """Test soft delete functionality"""
    app = create_app()
    
    with app.app_context():
        from app.utils.soft_delete import soft_delete_product_supplier, restore_product_supplier, get_deleted_product_suppliers
        
        print('=== Testing Soft Delete Functionality ===')
        
        # Check current active relationships
        conn = sqlite3.connect(app.config['DATABASE_PATH'])
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM product_suppliers WHERE is_deleted = 0')
        active_before = cursor.fetchone()[0]
        print(f'Active relationships before: {active_before}')
        
        cursor.execute('SELECT COUNT(*) FROM product_suppliers WHERE is_deleted = 1')
        deleted_before = cursor.fetchone()[0]
        print(f'Deleted relationships before: {deleted_before}')
        
        # Get a sample relationship to test
        cursor.execute('SELECT product_id, supplier_id FROM product_suppliers WHERE is_deleted = 0 LIMIT 1')
        sample = cursor.fetchone()
        conn.close()
        
        if sample:
            product_id, supplier_id = sample
            print(f'\nTesting with product_id={product_id}, supplier_id={supplier_id}')
            
            # Test soft delete
            print('\n1. Testing soft delete...')
            success, message = soft_delete_product_supplier(product_id, supplier_id)
            print(f'   Result: {success}, Message: {message}')
            
            # Check counts after delete
            conn = sqlite3.connect(app.config['DATABASE_PATH'])
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM product_suppliers WHERE is_deleted = 0')
            active_after_delete = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM product_suppliers WHERE is_deleted = 1')
            deleted_after_delete = cursor.fetchone()[0]
            conn.close()
            
            print(f'   Active after delete: {active_after_delete}')
            print(f'   Deleted after delete: {deleted_after_delete}')
            
            # Test get deleted relationships
            print('\n2. Testing get deleted relationships...')
            deleted_rels = get_deleted_product_suppliers()
            print(f'   Found {len(deleted_rels)} deleted relationships')
            if deleted_rels:
                rel = deleted_rels[0]
                print(f'   Sample: Product "{rel["product_name"]}" - Supplier "{rel["supplier_name"]}"')
            
            # Test restore
            print('\n3. Testing restore...')
            success, message = restore_product_supplier(product_id, supplier_id)
            print(f'   Result: {success}, Message: {message}')
            
            # Check counts after restore
            conn = sqlite3.connect(app.config['DATABASE_PATH'])
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM product_suppliers WHERE is_deleted = 0')
            active_after_restore = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM product_suppliers WHERE is_deleted = 1')
            deleted_after_restore = cursor.fetchone()[0]
            conn.close()
            
            print(f'   Active after restore: {active_after_restore}')
            print(f'   Deleted after restore: {deleted_after_restore}')
            
            # Test double delete (should fail)
            print('\n4. Testing double delete (should fail)...')
            success, message = soft_delete_product_supplier(product_id, supplier_id)
            print(f'   Result: {success}, Message: {message}')
            
            # Delete again for final state
            success, message = soft_delete_product_supplier(product_id, supplier_id)
            print(f'   Delete again: {success}')
            
            print('\n✅ Soft delete functionality test completed!')
        else:
            print('No relationships found to test')

if __name__ == "__main__":
    test_soft_delete()