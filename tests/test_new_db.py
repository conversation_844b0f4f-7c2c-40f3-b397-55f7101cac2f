#!/usr/bin/env python3
"""
Test script for new database structure
"""

import sqlite3

def test_database():
    conn = sqlite3.connect('pazaryeri_v2.db')
    conn.row_factory = sqlite3.Row
    
    print("Testing new database structure...")
    
    # Test products
    products = conn.execute('SELECT * FROM products LIMIT 5').fetchall()
    print(f"\n✅ Products table: {len(products)} records")
    if products:
        print(f"   Sample: {products[0]['sku']} - {products[0]['product_name']}")
    
    # Test users
    users = conn.execute('SELECT * FROM users').fetchall()
    print(f"\n✅ Users table: {len(users)} records")
    
    # Test warehouses
    warehouses = conn.execute('SELECT * FROM warehouses').fetchall()
    print(f"\n✅ Warehouses table: {len(warehouses)} records")
    
    # Test suppliers
    suppliers = conn.execute('SELECT * FROM suppliers').fetchall()
    print(f"\n✅ Suppliers table: {len(suppliers)} records")
    
    # Test inventory
    inventory = conn.execute('SELECT * FROM inventory').fetchall()
    print(f"\n✅ Inventory table: {len(inventory)} records")
    
    # Test API sites
    api_sites = conn.execute('SELECT * FROM api_sites').fetchall()
    print(f"\n✅ API sites table: {len(api_sites)} records")
    
    # Test marketplace products
    mp_products = conn.execute('SELECT * FROM marketplace_products').fetchall()
    print(f"\n✅ Marketplace products: {len(mp_products)} records")
    
    # Check Ozon API credentials
    ozon = conn.execute("SELECT * FROM api_sites WHERE name = 'Ozon'").fetchone()
    if ozon:
        print(f"\n✅ Ozon API configured: Client ID exists: {bool(ozon['client_id'])}, API Key exists: {bool(ozon['api_key'])}")
    
    conn.close()

if __name__ == '__main__':
    test_database()