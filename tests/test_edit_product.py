#!/usr/bin/env python3
"""
Test script for product edit functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
import sqlite3

def test_edit_product():
    """Test product edit functionality"""
    app = create_app()
    
    with app.app_context():
        print('=== Testing Product Edit Functionality ===')
        
        # Check product before edit
        conn = sqlite3.connect(app.config['DATABASE_PATH'])
        cursor = conn.cursor()
        
        cursor.execute('SELECT product_name, profit_margin, commission_rate FROM products WHERE id = 1')
        before = cursor.fetchone()
        print(f'Before edit: {before}')
        
        # Test the edit route with proper data
        with app.test_client() as client:
            # First, get the edit page to make sure it loads
            response = client.get('/product/1/edit')
            print(f'GET /product/1/edit status: {response.status_code}')
            
            # Submit edit form
            form_data = {
                'product_name': 'Apple iPhone 15 Pro 128GB - EDITED',
                'sku': 'IPH15PRO128-EDITED',  # Use unique SKU
                'model': 'iPhone 15 Pro',
                'brand': 'Apple',
                'barcode': '194253945123',
                'desi': 0.5,
                'stock_multiplier': 1,
                'profit_margin': 25.0,
                'commission_rate': 12.0,
                'gtip': '8517120000'
            }
            
            response = client.post('/product/1/edit', data=form_data, follow_redirects=False)
            print(f'POST /product/1/edit status: {response.status_code}')
            print(f'Response headers: {dict(response.headers)}')
            
            if response.status_code == 302:
                print(f'Redirected to: {response.location}')
            elif response.status_code == 200:
                # Check if there are flash messages in the response
                response_text = response.get_data(as_text=True)
                if 'alert-danger' in response_text:
                    print('Found error alerts in response')
                    # Extract error messages
                    import re
                    errors = re.findall(r'alert-danger.*?>(.*?)</div>', response_text, re.DOTALL)
                    for error in errors:
                        print(f'Error: {error.strip()}')
        
        # Check product after edit
        cursor.execute('SELECT product_name, profit_margin, commission_rate FROM products WHERE id = 1')
        after = cursor.fetchone()
        print(f'After edit: {after}')
        
        conn.close()
        
        if before != after:
            print('✅ Product edit successful!')
        else:
            print('❌ Product edit failed - no changes detected')

if __name__ == "__main__":
    test_edit_product()