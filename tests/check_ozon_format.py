#!/usr/bin/env python3
"""
Ozon API response formatını kontrol et
"""

from ozon_api_client import OzonAPIClient
import json

def check_format():
    client = OzonAPIClient()
    
    # Tek bir ürün çek
    result = client.get_product_list(limit=1)
    
    if result and 'result' in result:
        print("API Response:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        if 'items' in result['result'] and len(result['result']['items']) > 0:
            print("\n\nİlk ürünün alanları:")
            item = result['result']['items'][0]
            for key, value in item.items():
                print(f"  {key}: {value}")

if __name__ == '__main__':
    check_format()