#!/usr/bin/env python3
"""
Ozon ürün detay bilgilerini test et
"""

from ozon_api_client import OzonAPIClient
import json

def test_detail():
    client = OzonAPIClient()
    
    # Önce ürün listesi al
    print("1. Ürün listesi alınıyor...")
    list_result = client.get_product_list(limit=3)
    
    if list_result and 'result' in list_result:
        items = list_result['result']['items']
        print(f"   {len(items)} ürün bulundu")
        
        # SKU'ları topla
        sku_list = [item['offer_id'] for item in items]
        print(f"   SKU'lar: {sku_list}")
        
        # SKU ile detay bilgi çek
        print("\n2. SKU ile detaylı bilgi çekiliyor...")
        detail_result = client.get_product_info_by_sku(sku_list)
        
        if detail_result:
            print("\nDetay Response:")
            print(json.dumps(detail_result, indent=2, ensure_ascii=False)[:1000])
            
            if 'result' in detail_result and 'items' in detail_result['result']:
                print(f"\n   {len(detail_result['result']['items'])} detaylı ürün bulundu")
                
                if len(detail_result['result']['items']) > 0:
                    print("\n   İlk ürünün detayları:")
                    item = detail_result['result']['items'][0]
                    for key in ['name', 'offer_id', 'price', 'old_price', 'category_id', 'barcode']:
                        if key in item:
                            print(f"     {key}: {item[key]}")

if __name__ == '__main__':
    test_detail()