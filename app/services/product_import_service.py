import requests
import json
import xml.etree.ElementTree as ET
from bs4 import BeautifulSoup
import re
from urllib.parse import urlparse, urljoin
from typing import Dict, List, Optional, Tuple, Any
import time
import logging
import traceback

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class ProductImportService:
    """
    Akıllı ürün içe aktarma servisi
    URL, JSON, XML verilerinden ürün bilgilerini çıkarır
    """
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        # Güvenlik ve performans ayarları
        self.allowed_domains = [
            'trendyol.com', 'hepsiburada.com', 'n11.com', 'amazon.com', 'amazon.com.tr'
        ]
        self.max_content_size = 10 * 1024 * 1024  # 10MB limit
        self.timeout = 15  # 15 saniye timeout
        self.max_redirects = 5
        
        # Selenium helper (lazy loaded)
        self._selenium_helper = None
        self.use_selenium_for_trendyol = True  # Enable Selenium for Trendyol by default
    
    @property
    def selenium_helper(self):
        """Lazy load Selenium helper"""
        if self._selenium_helper is None:
            try:
                from app.services.selenium_helper import SeleniumHelper
                self._selenium_helper = SeleniumHelper()
                logger.info("Selenium helper initialized")
            except Exception as e:
                logger.error(f"Failed to initialize Selenium helper: {e}")
                self.use_selenium_for_trendyol = False
        return self._selenium_helper
    
    def detect_data_type(self, data: str) -> str:
        """Gelen verinin türünü belirle (url, json, xml)"""
        data = data.strip()
        logger.debug(f"Detecting data type for input (first 100 chars): {data[:100]}")
        
        # URL kontrolü
        if data.startswith(('http://', 'https://', 'www.')):
            logger.info(f"Detected URL: {data}")
            return 'url'
        
        # JSON kontrolü
        try:
            json.loads(data)
            logger.info("Detected JSON data")
            return 'json'
        except (json.JSONDecodeError, ValueError) as e:
            logger.debug(f"Not JSON: {str(e)}")
            pass
        
        # XML kontrolü
        try:
            ET.fromstring(data)
            logger.info("Detected XML data")
            return 'xml'
        except ET.ParseError as e:
            logger.debug(f"Not XML: {str(e)}")
            pass
        
        logger.warning("Could not detect data type")
        return 'unknown'
    
    def import_product_data(self, data: str) -> Dict[str, Any]:
        """Ana import fonksiyonu - veri türünü belirleyip uygun parser'ı çağırır"""
        data_type = self.detect_data_type(data)
        
        try:
            logger.info(f"Starting import for data type: {data_type}")
            
            if data_type == 'url':
                return self._import_from_url(data)
            elif data_type == 'json':
                return self._import_from_json(data)
            elif data_type == 'xml':
                return self._import_from_xml(data)
            else:
                logger.error(f"Unsupported data format: {data_type}")
                return {
                    'success': False,
                    'error': 'Desteklenmeyen veri formatı. URL, JSON veya XML verisi girin.',
                    'data': {}
                }
        except Exception as e:
            logger.exception("Unexpected error in import_product_data")
            return {
                'success': False,
                'error': f'Veri işlenirken hata oluştu: {str(e)}',
                'data': {}
            }
    
    def _import_from_url(self, url: str) -> Dict[str, Any]:
        """URL'den ürün verilerini çek"""
        try:
            # URL'yi temizle ve normalize et
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            # URL'yi doğrula
            parsed_url = urlparse(url)
            if not parsed_url.netloc:
                return {
                    'success': False,
                    'error': 'Geçersiz URL formatı',
                    'data': {}
                }
            
            # Güvenlik kontrolü - sadece izin verilen domainlere erişim
            domain = parsed_url.netloc.lower()
            is_allowed = any(allowed_domain in domain for allowed_domain in self.allowed_domains)
            
            if not is_allowed:
                return {
                    'success': False,
                    'error': f'Bu domain desteklenmiyor: {domain}. Desteklenen siteler: {", ".join(self.allowed_domains)}',
                    'data': {}
                }
            
            # Sayfayı çek
            response = self.session.get(
                url, 
                timeout=self.timeout,
                stream=True,
                allow_redirects=True
            )
            response.raise_for_status()
            
            # İçerik boyutu kontrolü
            content_length = response.headers.get('content-length')
            if content_length and int(content_length) > self.max_content_size:
                return {
                    'success': False,
                    'error': 'Sayfa çok büyük (10MB limitini aşıyor)',
                    'data': {}
                }
            
            # İçeriği kademeli olarak oku
            content = b''
            for chunk in response.iter_content(chunk_size=8192):
                content += chunk
                if len(content) > self.max_content_size:
                    return {
                        'success': False,
                        'error': 'Sayfa çok büyük (10MB limitini aşıyor)',
                        'data': {}
                    }
            
            # HTML'i parse et
            soup = BeautifulSoup(content, 'html.parser')
            
            # Site türünü belirle ve özel parser kullan
            domain = parsed_url.netloc.lower()
            logger.info(f"Parsing content from domain: {domain}")
            
            if 'trendyol.com' in domain:
                return self._parse_trendyol(soup, url)
            elif 'hepsiburada.com' in domain:
                return self._parse_hepsiburada(soup, url)
            elif 'n11.com' in domain:
                return self._parse_n11(soup, url)
            elif 'amazon.' in domain:
                return self._parse_amazon(soup, url)
            else:
                logger.info(f"Using generic parser for domain: {domain}")
                return self._parse_generic_ecommerce(soup, url)
                
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': 'Sayfa yüklenirken zaman aşımı oluştu',
                'data': {}
            }
        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': f'Sayfa yüklenemedi: {str(e)}',
                'data': {}
            }
    
    def _import_from_json(self, json_data: str) -> Dict[str, Any]:
        """JSON verilerinden ürün bilgilerini çıkar"""
        try:
            # JSON boyut kontrolü
            if len(json_data) > 1024 * 1024:  # 1MB limit for JSON
                return {
                    'success': False,
                    'error': 'JSON verisi çok büyük (1MB limitini aşıyor)',
                    'data': {}
                }
            
            data = json.loads(json_data)
            
            # JSON yapısını analiz et ve ürün verilerini çıkar
            product_data = self._extract_from_json_structure(data)
            
            # Veri sanitizasyonu
            product_data = self._sanitize_product_data(product_data)
            
            return {
                'success': True,
                'data': product_data,
                'source': 'json'
            }
            
        except json.JSONDecodeError as e:
            return {
                'success': False,
                'error': f'JSON formatı hatalı: {str(e)}',
                'data': {}
            }
    
    def _import_from_xml(self, xml_data: str) -> Dict[str, Any]:
        """XML verilerinden ürün bilgilerini çıkar"""
        try:
            # XML boyut kontrolü
            if len(xml_data) > 1024 * 1024:  # 1MB limit for XML
                return {
                    'success': False,
                    'error': 'XML verisi çok büyük (1MB limitini aşıyor)',
                    'data': {}
                }
            
            # Güvenli XML parsing (XXE saldırılarına karşı)
            parser = ET.XMLParser()
            parser.entity = {}  # Disable entity processing
            
            root = ET.fromstring(xml_data, parser=parser)
            
            # XML yapısını analiz et ve ürün verilerini çıkar
            product_data = self._extract_from_xml_structure(root)
            
            # Veri sanitizasyonu
            product_data = self._sanitize_product_data(product_data)
            
            return {
                'success': True,
                'data': product_data,
                'source': 'xml'
            }
            
        except ET.ParseError as e:
            return {
                'success': False,
                'error': f'XML formatı hatalı: {str(e)}',
                'data': {}
            }
    
    def _parse_trendyol(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """Trendyol sitesinden ürün verilerini çıkar"""
        product_data = {}
        logger.info(f"Starting Trendyol parser for URL: {url}")
        
        try:
            # JSON-LD verilerini önce kontrol et (daha güvenilir)
            scripts = soup.find_all('script', {'type': 'application/ld+json'})
            logger.debug(f"Found {len(scripts)} JSON-LD scripts")
            
            for idx, script in enumerate(scripts):
                try:
                    if not script.string:
                        continue
                    
                    data = json.loads(script.string)
                    logger.debug(f"JSON-LD script {idx}: {type(data).__name__}")
                    
                    # Handle both Product and ProductGroup types
                    if isinstance(data, dict) and data.get('@type') in ['Product', 'ProductGroup']:
                        logger.info(f"Found {data.get('@type')} JSON-LD data")
                        
                        if data.get('name'):
                            product_data['product_name'] = data['name']
                            logger.debug(f"Product name from JSON-LD: {data['name']}")
                        
                        if data.get('brand'):
                            if isinstance(data['brand'], dict):
                                product_data['brand'] = data['brand'].get('name', '')
                            else:
                                product_data['brand'] = str(data['brand'])
                            logger.debug(f"Brand from JSON-LD: {product_data['brand']}")
                        
                        # Extract description from JSON-LD
                        if data.get('description'):
                            product_data['description'] = data['description']
                            logger.debug(f"Description from JSON-LD: {len(data['description'])} chars")
                        
                        # Handle offers - can be dict, list, or embedded in other structures
                        offers = data.get('offers')
                        if offers:
                            if isinstance(offers, dict):
                                price = offers.get('price') or offers.get('lowPrice') or offers.get('highPrice')
                                if price:
                                    try:
                                        product_data['price'] = float(price)
                                        logger.debug(f"Price from JSON-LD: {price}")
                                    except (ValueError, TypeError):
                                        pass
                            elif isinstance(offers, list) and offers:
                                # Take the first offer
                                first_offer = offers[0]
                                if isinstance(first_offer, dict):
                                    price = first_offer.get('price')
                                    if price:
                                        try:
                                            product_data['price'] = float(price)
                                            logger.debug(f"Price from JSON-LD offers[0]: {price}")
                                        except (ValueError, TypeError):
                                            pass
                        
                        # Handle image data - can be string, list, or ImageObject
                        if data.get('image'):
                            image_data = data['image']
                            
                            if isinstance(image_data, str):
                                product_data['images'] = [image_data]
                            elif isinstance(image_data, list):
                                product_data['images'] = image_data
                            elif isinstance(image_data, dict):
                                # Handle ImageObject type
                                if image_data.get('contentUrl'):
                                    content_url = image_data['contentUrl']
                                    if isinstance(content_url, str):
                                        product_data['images'] = [content_url]
                                    elif isinstance(content_url, list):
                                        product_data['images'] = content_url
                                elif image_data.get('url'):
                                    product_data['images'] = [image_data['url']]
                                elif image_data.get('@id'):
                                    product_data['images'] = [image_data['@id']]
                            
                            if product_data.get('images'):
                                logger.debug(f"Found {len(product_data['images'])} images from JSON-LD")
                        
                        # For ProductGroup, check hasVariant for additional data
                        if data.get('@type') == 'ProductGroup' and data.get('hasVariant'):
                            variants = data['hasVariant']
                            if isinstance(variants, list) and variants:
                                # Use first variant for price if not found
                                if not product_data.get('price'):
                                    for variant in variants:
                                        if isinstance(variant, dict) and variant.get('offers'):
                                            variant_offers = variant['offers']
                                            if isinstance(variant_offers, dict):
                                                price = variant_offers.get('price')
                                                if price:
                                                    try:
                                                        product_data['price'] = float(price)
                                                        logger.debug(f"Price from variant: {price}")
                                                        break
                                                    except (ValueError, TypeError):
                                                        pass
                        
                        break
                except Exception as e:
                    logger.debug(f"Error parsing JSON-LD script {idx}: {str(e)}")
                    continue
            
            # Eğer JSON-LD'den veri alınamazsa HTML parsing'e devam et
            if not product_data.get('product_name'):
                logger.debug("Product name not found in JSON-LD, trying HTML selectors")
                
                # Ürün adı - güncel selectors
                title_selectors = [
                    'h1.pr-new-br',
                    'h1[data-test-id="product-name"]',
                    'h1.product-name',
                    'div.pr-cn h1',
                    'div.product-detail-wrapper h1',
                    'span.pr-new-br',
                    'div.pr-in-nm',
                    'h1'  # Fallback
                ]
                
                for selector in title_selectors:
                    title_elem = soup.select_one(selector)
                    if title_elem:
                        text = title_elem.get_text(strip=True)
                        if text and len(text) > 5:  # Minimum uzunluk kontrolü
                            product_data['product_name'] = text
                            logger.debug(f"Found product name with selector '{selector}': {text[:50]}...")
                            break
                    else:
                        logger.debug(f"No element found with selector: {selector}")
                
                # Eğer hala bulunamazsa, title tag'den al
                if not product_data.get('product_name'):
                    logger.debug("Product name not found in HTML, trying title tag")
                    title_tag = soup.find('title')
                    if title_tag:
                        title_text = title_tag.get_text(strip=True)
                        logger.debug(f"Title tag content: {title_text}")
                        # " - Trendyol" gibi site isimlerini kaldır
                        title_text = re.split(r' - | \| ', title_text)[0]
                        if title_text:
                            product_data['product_name'] = title_text
                            logger.debug(f"Extracted product name from title: {title_text}")
            
            if not product_data.get('brand'):
                logger.debug("Brand not found in JSON-LD, trying HTML selectors")
                
                # Marka - güncel selectors
                brand_selectors = [
                    'a[class*="brand"]',
                    'h1 a[href*="/"]',
                    'a[data-test-id="product-brand-name"]',
                    'a.product-brand',
                    'h1.pr-new-br a',
                    'h1.pr-new-br span',
                    'div.pr-bx-nm a',
                    'div.pr-in-br a',
                    'a[href*="-x-b"]'  # Trendyol brand URL pattern
                ]
                
                for selector in brand_selectors:
                    brand_elem = soup.select_one(selector)
                    if brand_elem:
                        brand_text = brand_elem.get_text(strip=True)
                        # Check if it's a valid brand (not too long, not a full product name)
                        if brand_text and len(brand_text) < 30:
                            # Also check href to confirm it's a brand link
                            href = brand_elem.get('href', '')
                            if '-x-b' in href or '/marka/' in href or not href:
                                product_data['brand'] = brand_text
                                logger.debug(f"Found brand with selector '{selector}': {brand_text}")
                                break
                
                # Alternatif: Ürün adının ilk kelimesini marka olarak kullan
                if not product_data.get('brand') and product_data.get('product_name'):
                    first_word = product_data['product_name'].split()[0]
                    # Expanded brand list
                    known_brands = ['PHILIPS', 'SAMSUNG', 'APPLE', 'BRAUN', 'ORAL-B', 'GILLETTE', 'SONY', 
                                  'LG', 'BOSCH', 'TEFAL', 'ROWENTA', 'DYSON', 'KARCHER', 'DELONGHI']
                    if first_word.upper() in known_brands:
                        product_data['brand'] = first_word
                        logger.debug(f"Extracted brand from product name: {first_word}")
            
            if not product_data.get('price'):
                logger.debug("Price not found in JSON-LD, looking for JavaScript variables")
                
                # First try to find price in JavaScript variables
                for script in soup.find_all('script'):
                    if script.string and ('window.__PRODUCT_DETAIL__' in script.string or 'window.__PRODUCT_DETAIL_APP__' in script.string):
                        try:
                            # Extract the JSON part
                            script_text = script.string
                            
                            # Look for price patterns in the script
                            import re
                            price_patterns = [
                                r'"price"\s*:\s*([\d.]+)',
                                r'"salePrice"\s*:\s*([\d.]+)',
                                r'"discountedPrice"\s*:\s*([\d.]+)',
                                r'"sellingPrice"\s*:\s*([\d.]+)'
                            ]
                            
                            for pattern in price_patterns:
                                match = re.search(pattern, script_text)
                                if match:
                                    try:
                                        price = float(match.group(1))
                                        if 0 < price < 10000000:
                                            product_data['price'] = price
                                            logger.debug(f"Found price in JavaScript: {price}")
                                            break
                                    except ValueError:
                                        pass
                            
                            if product_data.get('price'):
                                break
                        except Exception as e:
                            logger.debug(f"Error extracting price from JavaScript: {e}")
                
                # If still no price, try HTML selectors
                if not product_data.get('price'):
                    logger.debug("Price not found in JavaScript, trying HTML selectors")
                    
                    # Fiyat - güncel selectors
                    price_selectors = [
                        'span.prc-dsc',
                        'span.prc-slg',
                        'span[data-test-id="price-current"]',
                        'div.pr-bx-pr-cn span.prc-dsc',
                        'div.pr-bx-pr span',
                        'span.price-new',
                        'span.product-price-new',
                        'div.product-price span',
                        'div[class*="price"] span[class*="prc"]'
                    ]
                    
                    for selector in price_selectors:
                        price_elem = soup.select_one(selector)
                        if price_elem:
                            price_text = price_elem.get_text(strip=True)
                            logger.debug(f"Found price text with selector '{selector}': {price_text}")
                            price = self._extract_price(price_text)
                            if price:
                                product_data['price'] = price
                                logger.debug(f"Extracted price: {price}")
                                break
            
            # Resimler
            if not product_data.get('images'):
                logger.debug("Images not found in JSON-LD, extracting from HTML")
                images = self._extract_trendyol_images(soup, url)
                if images:
                    product_data['images'] = images
                    logger.debug(f"Found {len(images)} images from HTML")
            
            # Model/SKU - Ürün detaylarından
            if not product_data.get('model'):
                # Try different selectors for product details
                detail_selectors = [
                    'li.detail-attr-item',
                    'div.pr-in-at li',
                    'ul.detail-attr-list li',
                    'div[class*="detail"] li'
                ]
                
                for selector in detail_selectors:
                    detail_items = soup.select(selector)
                    for item in detail_items:
                        text = item.get_text()
                        if 'Model' in text or 'model' in text:
                            parts = text.split(':')
                            if len(parts) > 1:
                                product_data['model'] = parts[1].strip()
                                logger.debug(f"Found model: {product_data['model']}")
                                break
                    if product_data.get('model'):
                        break
                
                # If no model found, try to extract from product name
                if not product_data.get('model') and product_data.get('product_name'):
                    # Look for patterns like MG3720/15 or HX9393/20
                    import re
                    # More specific pattern for model numbers
                    model_patterns = [
                        r'([A-Z]{2,3}\d{3,4}/\d{2})',  # Like MG3720/15, HX9393/20
                        r'([A-Z]{2,3}-?\d{3,4})',       # Like MG3720, HX-9393
                        r'([A-Z]+\d+[A-Z0-9/\-]*)',     # General alphanumeric model
                    ]
                    
                    for pattern in model_patterns:
                        model_match = re.search(pattern, product_data['product_name'])
                        if model_match:
                            potential_model = model_match.group(1)
                            # Check if it's likely a model (not ISLAK/KURU etc.)
                            if not any(word in potential_model for word in ['ISLAK', 'KURU', 'YUZ', 'SAC']):
                                product_data['model'] = potential_model
                                logger.debug(f"Extracted model from product name: {product_data['model']}")
                                break
            
            # Extract description
            if not product_data.get('description'):
                # First try to extract from JavaScript window variables in the page
                scripts = soup.find_all('script')
                for script in scripts:
                    if script.string and 'window.__PRODUCT_DETAIL_APP_INITIAL_STATE__' in script.string:
                        try:
                            # Extract the JSON data from the script
                            script_text = script.string
                            
                            # Look for the variable assignment
                            import re
                            match = re.search(r'window\.__PRODUCT_DETAIL_APP_INITIAL_STATE__\s*=\s*({.*?});', script_text, re.DOTALL)
                            if match:
                                json_str = match.group(1)
                                # Parse the JSON
                                state_data = json.loads(json_str)
                                
                                # Look for description in various possible locations
                                if isinstance(state_data, dict):
                                    # Direct product description
                                    if state_data.get('product', {}).get('description'):
                                        product_data['description'] = state_data['product']['description']
                                        logger.info(f"Found description in JavaScript state: {len(product_data['description'])} chars")
                                        break
                                    
                                    # Check other possible paths
                                    for path in ['productDetail.description', 'data.product.description', 'description']:
                                        parts = path.split('.')
                                        value = state_data
                                        for part in parts:
                                            if isinstance(value, dict) and part in value:
                                                value = value[part]
                                            else:
                                                value = None
                                                break
                                        if value and isinstance(value, str):
                                            product_data['description'] = value
                                            logger.info(f"Found description in JavaScript state at {path}: {len(value)} chars")
                                            break
                        except Exception as e:
                            logger.debug(f"Failed to extract description from JavaScript state: {e}")
                
                # If not found in JavaScript, try HTML selectors
                if not product_data.get('description'):
                    description_selectors = [
                        'div.product-detail-desc-content',
                        'div.pr-in-dt-cn',
                        'div.detail-desc-list',
                        'div[class*="description"]',
                        'div.info-wrapper',
                        'section.detail-border:has(h2:contains("Ürün Açıklaması"))',
                        'div.product-description',
                        # New Trendyol-specific selectors
                        'div[class*="detail-description"]',
                        'div[class*="product-detail-container"]',
                        'div.info__wrapper',
                        'div.pr-in-w div.info-wrapper-item',
                        'section[class*="detail"] div[class*="html-content"]'
                    ]
                    description = self._extract_description(soup, description_selectors)
                    if description:
                        product_data['description'] = description
                        logger.debug(f"Found description: {len(description)} characters")
                    else:
                        # Fallback: Try to get meta description
                        meta_desc = soup.find('meta', {'name': 'description'})
                        if not meta_desc:
                            meta_desc = soup.find('meta', {'property': 'og:description'})
                        
                        if meta_desc and meta_desc.get('content'):
                            product_data['description'] = meta_desc.get('content')
                            logger.debug(f"Found description from meta tag: {len(product_data['description'])} chars")
                        
                        # If still no description and dynamic content extraction is enabled, try with Playwright
                        if not product_data.get('description') and self.use_selenium_for_trendyol:
                            logger.info("No description found with BeautifulSoup, trying dynamic content extraction...")
                            try:
                                # Try Playwright first (more reliable)
                                try:
                                    from app.services.playwright_helper import PlaywrightHelper
                                    if not hasattr(self, '_playwright_helper'):
                                        self._playwright_helper = PlaywrightHelper()
                                    
                                    playwright_desc = self._playwright_helper.extract_trendyol_description(url)
                                    if playwright_desc:
                                        product_data['description'] = playwright_desc
                                        logger.info(f"Found description with Playwright: {len(playwright_desc)} chars")
                                    else:
                                        raise Exception("No description found with Playwright")
                                        
                                except Exception as e:
                                    # Fallback to Selenium if Playwright fails
                                    logger.warning(f"Playwright failed ({e}), trying Selenium...")
                                    selenium_desc = self.selenium_helper.extract_trendyol_description(url)
                                    if selenium_desc:
                                        product_data['description'] = selenium_desc
                                        logger.info(f"Found description with Selenium: {len(selenium_desc)} chars")
                                        
                            except Exception as e:
                                logger.error(f"Dynamic content extraction failed: {e}")
            
            # Extract features/specifications
            if not product_data.get('features'):
                feature_selectors = [
                    'ul.detail-attr-list',
                    'div.pr-in-at',
                    'table.detail-features',
                    'div.detail-attributes',
                    'ul.product-feature-list',
                    'div.product-attributes',
                    'div[class*="specification"]'
                ]
                features = self._extract_features(soup, feature_selectors)
                if features:
                    product_data['features'] = json.dumps(features, ensure_ascii=False)
                    logger.debug(f"Found {len(features)} features")
            
            # Veri sanitizasyonu
            product_data = self._sanitize_product_data(product_data)
            
            # Debug log
            logger.info(f"Trendyol parser result: {json.dumps(product_data, ensure_ascii=False)}")
            
            # Log found and missing fields
            found_fields = [k for k in ['product_name', 'brand', 'model', 'price', 'images', 'description', 'features'] if product_data.get(k)]
            missing_fields = [k for k in ['product_name', 'brand', 'model', 'price', 'images', 'description', 'features'] if not product_data.get(k)]
            
            logger.info(f"Found fields: {', '.join(found_fields)}")
            if missing_fields:
                logger.warning(f"Missing fields: {', '.join(missing_fields)}")
            
            # Eğer kritik veriler bulunamazsa generic parser'a fallback yap
            if not product_data.get('product_name') and not product_data.get('price'):
                logger.warning("Trendyol parser failed to extract critical data, trying generic parser...")
                generic_result = self._parse_generic_ecommerce(soup, url)
                if generic_result['success'] and generic_result['data']:
                    logger.info("Generic parser succeeded as fallback")
                    return generic_result
            
            return {
                'success': True,
                'data': product_data,
                'source': 'trendyol'
            }
            
        except Exception as e:
            logger.exception(f"Trendyol parser error: {str(e)}")
            return {
                'success': False,
                'error': f'Trendyol verisi işlenirken hata: {str(e)}',
                'data': {}
            }
    
    def _parse_hepsiburada(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """Hepsiburada sitesinden ürün verilerini çıkar"""
        product_data = {}
        
        try:
            # Ürün adı
            title_elem = soup.find('h1', {'data-test-id': 'product-name'})
            if not title_elem:
                title_elem = soup.find('h1', class_='product-name')
            if title_elem:
                product_data['product_name'] = title_elem.get_text(strip=True)
            
            # Marka
            brand_elem = soup.find('a', {'data-test-id': 'product-brand'})
            if brand_elem:
                product_data['brand'] = brand_elem.get_text(strip=True)
            
            # Fiyat
            price_elem = soup.find('span', {'data-test-id': 'price-current'})
            if price_elem:
                price_text = price_elem.get_text(strip=True)
                price = self._extract_price(price_text)
                if price:
                    product_data['price'] = price
            
            # Resimler
            images = self._extract_hepsiburada_images(soup, url)
            if images:
                product_data['images'] = images
            
            return {
                'success': True,
                'data': product_data,
                'source': 'hepsiburada'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Hepsiburada verisi işlenirken hata: {str(e)}',
                'data': {}
            }
    
    def _parse_n11(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """N11 sitesinden ürün verilerini çıkar"""
        product_data = {}
        
        try:
            # Ürün adı
            title_elem = soup.find('h1', class_='proName')
            if title_elem:
                product_data['product_name'] = title_elem.get_text(strip=True)
            
            # Fiyat
            price_elem = soup.find('ins', class_='newPrice')
            if price_elem:
                price_text = price_elem.get_text(strip=True)
                price = self._extract_price(price_text)
                if price:
                    product_data['price'] = price
            
            # Resimler
            images = self._extract_n11_images(soup, url)
            if images:
                product_data['images'] = images
            
            return {
                'success': True,
                'data': product_data,
                'source': 'n11'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'N11 verisi işlenirken hata: {str(e)}',
                'data': {}
            }
    
    def _parse_amazon(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """Amazon sitesinden ürün verilerini çıkar"""
        product_data = {}
        
        try:
            # Ürün adı
            title_elem = soup.find('span', {'id': 'productTitle'})
            if title_elem:
                product_data['product_name'] = title_elem.get_text(strip=True)
            
            # Marka
            brand_elem = soup.find('a', {'id': 'bylineInfo'})
            if brand_elem:
                brand_text = brand_elem.get_text(strip=True)
                if brand_text.startswith('Brand:'):
                    product_data['brand'] = brand_text.replace('Brand:', '').strip()
                else:
                    product_data['brand'] = brand_text
            
            # Fiyat
            price_elem = soup.find('span', class_='a-price-whole')
            if price_elem:
                price_text = price_elem.get_text(strip=True)
                price = self._extract_price(price_text)
                if price:
                    product_data['price'] = price
            
            return {
                'success': True,
                'data': product_data,
                'source': 'amazon'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Amazon verisi işlenirken hata: {str(e)}',
                'data': {}
            }
    
    def _parse_generic_ecommerce(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """Genel e-ticaret sitelerinden ürün verilerini çıkar"""
        product_data = {}
        
        try:
            # Meta tags ve JSON-LD verilerini kontrol et
            self._extract_meta_data(soup, product_data)
            self._extract_jsonld_data(soup, product_data)
            self._extract_opengraph_data(soup, product_data)
            
            # Genel HTML selectors ile veri çıkarma
            if not product_data.get('product_name'):
                title_selectors = [
                    'h1.product-title', 'h1.product-name', 'h1.title',
                    'h1[itemprop="name"]', '.product-title h1', '.product-name',
                    'h1', 'title'
                ]
                for selector in title_selectors:
                    elem = soup.select_one(selector)
                    if elem:
                        product_data['product_name'] = elem.get_text(strip=True)
                        break
            
            # Fiyat
            if not product_data.get('price'):
                price_selectors = [
                    '.price', '.product-price', '[itemprop="price"]',
                    '.current-price', '.sale-price', '.amount'
                ]
                for selector in price_selectors:
                    elem = soup.select_one(selector)
                    if elem:
                        price_text = elem.get_text(strip=True)
                        price = self._extract_price(price_text)
                        if price:
                            product_data['price'] = price
                            break
            
            # Resimler
            if not product_data.get('images'):
                images = self._extract_generic_images(soup, url)
                if images:
                    product_data['images'] = images
            
            return {
                'success': True,
                'data': product_data,
                'source': 'generic'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f'Genel site verisi işlenirken hata: {str(e)}',
                'data': {}
            }
    
    def _extract_from_json_structure(self, data: Any) -> Dict[str, Any]:
        """JSON yapısından ürün verilerini çıkar"""
        product_data = {}
        
        def search_in_dict(obj, keys_to_find):
            """Dictionary'de belirli anahtarları ara"""
            if isinstance(obj, dict):
                for key, value in obj.items():
                    key_lower = key.lower()
                    if any(search_key in key_lower for search_key in keys_to_find):
                        return value
                    elif isinstance(value, (dict, list)):
                        result = search_in_dict(value, keys_to_find)
                        if result is not None:
                            return result
            elif isinstance(obj, list):
                for item in obj:
                    result = search_in_dict(item, keys_to_find)
                    if result is not None:
                        return result
            return None
        
        # Ürün adı
        name = search_in_dict(data, ['name', 'title', 'product_name', 'productname'])
        if name:
            product_data['product_name'] = str(name)
        
        # Marka
        brand = search_in_dict(data, ['brand', 'manufacturer', 'make'])
        if brand:
            product_data['brand'] = str(brand)
        
        # Model
        model = search_in_dict(data, ['model', 'sku', 'productcode'])
        if model:
            product_data['model'] = str(model)
        
        # Fiyat
        price = search_in_dict(data, ['price', 'cost', 'amount', 'value'])
        if price:
            if isinstance(price, (int, float)):
                product_data['price'] = float(price)
            else:
                extracted_price = self._extract_price(str(price))
                if extracted_price:
                    product_data['price'] = extracted_price
        
        # Barkod
        barcode = search_in_dict(data, ['barcode', 'ean', 'upc', 'gtin'])
        if barcode:
            product_data['barcode'] = str(barcode)
        
        # Resimler
        images = search_in_dict(data, ['images', 'pictures', 'photos', 'image'])
        if images:
            if isinstance(images, list):
                product_data['images'] = images
            else:
                product_data['images'] = [str(images)]
        
        return product_data
    
    def _extract_from_xml_structure(self, root: ET.Element) -> Dict[str, Any]:
        """XML yapısından ürün verilerini çıkar"""
        product_data = {}
        
        def find_element_text(element, tags):
            """XML element'inde belirli tag'leri ara"""
            for tag in tags:
                elem = element.find(f'.//{tag}')
                if elem is not None and elem.text:
                    return elem.text.strip()
            return None
        
        # Ürün adı
        name = find_element_text(root, ['name', 'title', 'product_name', 'productName'])
        if name:
            product_data['product_name'] = name
        
        # Marka
        brand = find_element_text(root, ['brand', 'manufacturer', 'make'])
        if brand:
            product_data['brand'] = brand
        
        # Model
        model = find_element_text(root, ['model', 'sku', 'productCode'])
        if model:
            product_data['model'] = model
        
        # Fiyat
        price = find_element_text(root, ['price', 'cost', 'amount'])
        if price:
            extracted_price = self._extract_price(price)
            if extracted_price:
                product_data['price'] = extracted_price
        
        # Barkod
        barcode = find_element_text(root, ['barcode', 'ean', 'upc', 'gtin'])
        if barcode:
            product_data['barcode'] = barcode
        
        # Resimler
        image_elements = root.findall('.//image') + root.findall('.//img') + root.findall('.//picture')
        if image_elements:
            images = []
            for img_elem in image_elements:
                if img_elem.text:
                    images.append(img_elem.text.strip())
                elif img_elem.get('src'):
                    images.append(img_elem.get('src'))
            if images:
                product_data['images'] = images
        
        return product_data
    
    def _extract_price(self, price_text: str) -> Optional[float]:
        """Metin içinden fiyat çıkar"""
        if not price_text:
            return None
        
        # String'e çevir
        price_text = str(price_text)
        logger.debug(f"Extracting price from: '{price_text}'")
        
        # TL, ₺, $ gibi sembolleri kaldır
        price_text = re.sub(r'[₺$€£¥]', '', price_text)
        
        # "TL", "USD" gibi para birimi kelimelerini kaldır
        price_text = re.sub(r'\b(TL|USD|EUR|GBP)\b', '', price_text, flags=re.IGNORECASE)
        
        # Türkçe binlik ayracı olan nokta ve ondalık virgülü handle et
        # Örnek: 1.234,56 -> 1234.56
        if ',' in price_text and '.' in price_text:
            # Eğer virgül noktadan sonra geliyorsa, Türk formatı
            if price_text.rfind(',') > price_text.rfind('.'):
                price_text = price_text.replace('.', '').replace(',', '.')
            else:
                # İngiliz formatı
                price_text = price_text.replace(',', '')
        elif ',' in price_text:
            # Sadece virgül var, ondalık ayracı olabilir
            parts = price_text.split(',')
            if len(parts) == 2 and len(parts[1]) <= 2:
                # Virgül ondalık ayracı
                price_text = price_text.replace(',', '.')
            else:
                # Virgül binlik ayracı
                price_text = price_text.replace(',', '')
        
        # Sayı dışındaki karakterleri temizle
        cleaned = re.sub(r'[^\d.]', '', price_text)
        
        # Fiyat pattern'lerini dene
        patterns = [
            r'(\d+\.?\d*)',  # 123.45 veya 123
            r'(\d+)'         # 123
        ]
        
        for pattern in patterns:
            match = re.search(pattern, cleaned)
            if match:
                try:
                    price = float(match.group(1))
                    # Mantıklı fiyat aralığı kontrolü
                    if 0 < price < 10000000:
                        logger.debug(f"Successfully extracted price: {price}")
                        return price
                    else:
                        logger.debug(f"Price {price} is out of reasonable range")
                except ValueError as e:
                    logger.debug(f"Failed to convert to float: {e}")
                    continue
        
        logger.debug("Could not extract price")
        return None
    
    def _extract_meta_data(self, soup: BeautifulSoup, product_data: Dict):
        """Meta tag'lerden veri çıkar"""
        # Title
        title_tag = soup.find('title')
        if title_tag and not product_data.get('product_name'):
            product_data['product_name'] = title_tag.get_text(strip=True)
        
        # Meta tags
        meta_mappings = {
            'product:name': 'product_name',
            'product:brand': 'brand',
            'product:price': 'price'
        }
        
        for meta_name, field_name in meta_mappings.items():
            meta_tag = soup.find('meta', {'property': meta_name})
            if meta_tag and meta_tag.get('content'):
                if field_name == 'price':
                    price = self._extract_price(meta_tag.get('content'))
                    if price:
                        product_data[field_name] = price
                else:
                    product_data[field_name] = meta_tag.get('content')
    
    def _extract_jsonld_data(self, soup: BeautifulSoup, product_data: Dict):
        """JSON-LD structured data'dan veri çıkar"""
        scripts = soup.find_all('script', {'type': 'application/ld+json'})
        
        for script in scripts:
            try:
                # Script içeriğini temizle
                script_content = script.string
                if not script_content:
                    continue
                    
                # Bazı siteler birden fazla JSON object içerebilir
                script_content = script_content.strip()
                
                # JSON parse et
                data = json.loads(script_content)
                
                # Array içinde Product olabilir
                if isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict) and item.get('@type') in ['Product', 'ProductGroup']:
                            data = item
                            break
                
                # Handle both Product and ProductGroup types
                if isinstance(data, dict) and data.get('@type') in ['Product', 'ProductGroup']:
                    if not product_data.get('product_name') and data.get('name'):
                        product_data['product_name'] = data['name']
                    
                    if not product_data.get('brand') and data.get('brand'):
                        brand = data['brand']
                        if isinstance(brand, dict):
                            product_data['brand'] = brand.get('name', '')
                        else:
                            product_data['brand'] = str(brand)
                    
                    if not product_data.get('price') and data.get('offers'):
                        offers = data['offers']
                        if isinstance(offers, dict):
                            price = offers.get('price') or offers.get('lowPrice')
                            if price:
                                price = self._extract_price(str(price))
                                if price:
                                    product_data['price'] = price
                        elif isinstance(offers, list) and offers:
                            # İlk teklifi al
                            first_offer = offers[0]
                            price = first_offer.get('price')
                            if price:
                                price = self._extract_price(str(price))
                                if price:
                                    product_data['price'] = price
                    
                    # Model/SKU
                    if not product_data.get('model'):
                        if data.get('sku'):
                            product_data['model'] = str(data['sku'])
                        elif data.get('mpn'):
                            product_data['model'] = str(data['mpn'])
                    
                    # Resimler
                    if not product_data.get('images') and data.get('image'):
                        if isinstance(data['image'], str):
                            product_data['images'] = [data['image']]
                        elif isinstance(data['image'], list):
                            product_data['images'] = data['image']
                        elif isinstance(data['image'], dict):
                            img_url = data['image'].get('url')
                            if img_url:
                                product_data['images'] = [img_url]
                    
            except (json.JSONDecodeError, KeyError, TypeError):
                continue
    
    def _extract_opengraph_data(self, soup: BeautifulSoup, product_data: Dict):
        """OpenGraph meta tag'lerinden veri çıkar"""
        og_mappings = {
            'og:title': 'product_name',
            'og:image': 'images'
        }
        
        for og_property, field_name in og_mappings.items():
            og_tag = soup.find('meta', {'property': og_property})
            if og_tag and og_tag.get('content'):
                if field_name == 'images':
                    if not product_data.get('images'):
                        product_data['images'] = [og_tag.get('content')]
                else:
                    if not product_data.get(field_name):
                        product_data[field_name] = og_tag.get('content')
    
    def _extract_trendyol_images(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """Trendyol resimlerini çıkar"""
        images = []
        
        # Öncelik sırasına göre image selector'ları
        image_selectors = [
            # Güncel selectors
            'div.gallery-modal img',
            'div.product-slide img',
            'div.base-product-image img',
            'img.detail-section-img',
            'div.product-container img[src*="ty-production"]',
            'img[data-test-id="product-image"]',
            # Legacy selectors
            'img.detail-attr-item',
            'div.slick-slide img',
            'div.product-detail-image img'
        ]
        
        for selector in image_selectors:
            imgs = soup.select(selector)
            for img in imgs:
                src = img.get('src') or img.get('data-src')
                if src:
                    # CDN URL'lerini temizle ve full size haline getir
                    if 'ty-production' in src or 'ty-staging' in src:
                        # Boyut parametrelerini kaldır
                        src = re.sub(r'/\d+x\d+/', '/', src)
                        src = re.sub(r'_zoom\.', '.', src)
                        src = re.sub(r'_\d+\.', '.', src)
                    
                    # URL tam değilse tamamla
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = urljoin(base_url, src)
                    
                    if src not in images and src.startswith('http'):
                        images.append(src)
        
        # Eğer hiç resim bulunamazsa, meta tag'lerden dene
        if not images:
            og_images = soup.find_all('meta', {'property': 'og:image'})
            for og in og_images:
                content = og.get('content')
                if content and content.startswith('http'):
                    images.append(content)
        
        return images[:10]  # Maksimum 10 resim döndür
    
    def _extract_hepsiburada_images(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """Hepsiburada resimlerini çıkar"""
        images = []
        
        # Ürün galerisi
        gallery_imgs = soup.find_all('img', {'data-test-id': 'product-image'})
        for img in gallery_imgs:
            if img.get('src'):
                images.append(img['src'])
        
        return images
    
    def _extract_n11_images(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """N11 resimlerini çıkar"""
        images = []
        
        # Ürün galerisi
        gallery_div = soup.find('div', class_='pro-img-list')
        if gallery_div:
            imgs = gallery_div.find_all('img')
            for img in imgs:
                if img.get('src'):
                    images.append(img['src'])
        
        return images
    
    def _extract_generic_images(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """Genel sitelerden resimleri çıkar"""
        images = []
        
        # Çeşitli image selector'ları dene
        image_selectors = [
            'img[itemprop="image"]',
            '.product-image img',
            '.product-img img',
            '.gallery img',
            '.product-photos img'
        ]
        
        for selector in image_selectors:
            imgs = soup.select(selector)
            for img in imgs:
                src = img.get('src') or img.get('data-src')
                if src:
                    # Relative URL'leri absolute'a çevir
                    if src.startswith('/'):
                        src = urljoin(base_url, src)
                    images.append(src)
        
        # Dublicate'leri kaldır
        return list(dict.fromkeys(images))
    
    def _sanitize_product_data(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """Ürün verilerini temizle ve güvenli hale getir"""
        sanitized = {}
        
        # String alanları temizle
        string_fields = ['product_name', 'brand', 'model', 'barcode']
        for field in string_fields:
            if field in product_data and product_data[field]:
                # HTML tag'lerini kaldır ve string'i temizle
                value = str(product_data[field]).strip()
                # Basit HTML tag temizleme
                value = re.sub(r'<[^>]+>', '', value)
                # Fazla boşlukları temizle
                value = re.sub(r'\s+', ' ', value)
                # Maksimum uzunluk kontrolü
                if field == 'product_name' and len(value) > 200:
                    value = value[:200]
                elif len(value) > 100:
                    value = value[:100]
                    
                sanitized[field] = value
        
        # Fiyat alanını kontrol et
        if 'price' in product_data:
            try:
                price = float(product_data['price'])
                if 0 <= price <= 1000000:  # Mantıklı fiyat aralığı
                    sanitized['price'] = price
            except (ValueError, TypeError):
                pass
        
        # Resim URL'lerini kontrol et
        if 'images' in product_data and isinstance(product_data['images'], list):
            validated_images = []
            for img_url in product_data['images'][:10]:  # Maksimum 10 resim
                if isinstance(img_url, str) and img_url.startswith(('http://', 'https://')):
                    # URL uzunluk kontrolü
                    if len(img_url) <= 500:
                        validated_images.append(img_url)
            
            if validated_images:
                sanitized['images'] = validated_images
        
        return sanitized
    
    def _extract_description(self, soup: BeautifulSoup, selectors: List[str]) -> Optional[str]:
        """Extract description from various selectors"""
        for selector in selectors:
            try:
                elem = soup.select_one(selector)
                if elem:
                    # Get text content, preserving some structure
                    description = elem.get_text(separator='\n', strip=True)
                    if description and len(description) > 20:  # Minimum length check
                        return description[:2000]  # Limit to 2000 chars
            except Exception as e:
                logger.debug(f"Error with selector {selector}: {e}")
                continue
        return None
    
    def _extract_features(self, soup: BeautifulSoup, selectors: List[str]) -> Optional[Dict[str, str]]:
        """Extract product features/specifications as key-value pairs"""
        features = {}
        
        for selector in selectors:
            try:
                # Try to find feature tables or lists
                feature_container = soup.select_one(selector)
                if not feature_container:
                    continue
                
                # Look for table rows
                rows = feature_container.select('tr')
                if rows:
                    for row in rows:
                        cells = row.select('td')
                        if len(cells) >= 2:
                            key = cells[0].get_text(strip=True)
                            value = cells[1].get_text(strip=True)
                            if key and value and not key.startswith(('var', 'function', '<')):
                                features[key] = value
                
                # Look for definition lists
                dt_elements = feature_container.select('dt')
                dd_elements = feature_container.select('dd')
                if dt_elements and dd_elements:
                    for dt, dd in zip(dt_elements, dd_elements):
                        key = dt.get_text(strip=True)
                        value = dd.get_text(strip=True)
                        if key and value:
                            features[key] = value
                
                # Look for list items with key:value pattern
                list_items = feature_container.select('li')
                for item in list_items:
                    text = item.get_text(strip=True)
                    if ':' in text:
                        parts = text.split(':', 1)
                        if len(parts) == 2:
                            key = parts[0].strip()
                            value = parts[1].strip()
                            if key and value:
                                features[key] = value
                
                if features:
                    # Limit to 20 features
                    return dict(list(features.items())[:20])
                    
            except Exception as e:
                logger.debug(f"Error extracting features with selector {selector}: {e}")
                continue
        
        return features if features else None
    
    def cleanup(self):
        """Cleanup resources (e.g., close Selenium/Playwright drivers)"""
        if self._selenium_helper:
            try:
                self._selenium_helper.close()
                logger.info("Selenium helper cleaned up")
            except Exception as e:
                logger.error(f"Error during Selenium cleanup: {e}")
                
        if hasattr(self, '_playwright_helper'):
            try:
                self._playwright_helper.close()
                logger.info("Playwright helper cleaned up")
            except Exception as e:
                logger.error(f"Error during Playwright cleanup: {e}")
