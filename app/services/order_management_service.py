import sqlite3
import json
from datetime import datetime, timedelta
from app.database import get_db_connection
from app.services.marketplace.ozon_api_client import OzonAPIClient
from app.services.marketplace.amazon_api_client import AmazonAPIClient
import logging

logger = logging.getLogger(__name__)

class OrderManagementService:
    """
    Comprehensive order management service for all marketplaces
    """
    
    def __init__(self, db_path=None):
        self.db_path = db_path or 'pazaryeri.db'
        self.ozon_client = OzonAPIClient(self.db_path)
        self.amazon_client = AmazonAPIClient(self.db_path)
    
    def sync_marketplace_orders(self, marketplace_name=None, days_back=7):
        """
        Sync orders from all or specific marketplace
        """
        try:
            if marketplace_name:
                return self._sync_single_marketplace(marketplace_name, days_back)
            else:
                return self._sync_all_marketplaces(days_back)
        except Exception as e:
            logger.error(f"Error syncing orders: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _sync_all_marketplaces(self, days_back=7):
        """Sync orders from all active marketplaces"""
        conn = get_db_connection()
        marketplaces = conn.execute('''
            SELECT id, name FROM api_sites 
            WHERE is_active = 1
        ''').fetchall()
        conn.close()
        
        results = {}
        for marketplace in marketplaces:
            try:
                result = self._sync_single_marketplace(marketplace['name'], days_back)
                results[marketplace['name']] = result
            except Exception as e:
                results[marketplace['name']] = {'success': False, 'error': str(e)}
        
        return results
    
    def _sync_single_marketplace(self, marketplace_name, days_back=7):
        """Sync orders from a specific marketplace"""
        # Ozon için RFC3339 formatında tarih oluştur (UTC timezone ile)
        since_date = (datetime.now() - timedelta(days=days_back)).isoformat() + 'Z'
        
        if marketplace_name == 'Ozon':
            return self._sync_ozon_orders(since_date)
        elif marketplace_name == 'Amazon':
            return self._sync_amazon_orders(since_date)
        else:
            return {'success': False, 'error': f'Marketplace {marketplace_name} not supported yet'}
    
    def _sync_ozon_orders(self, since_date):
        """Sync orders from Ozon"""
        try:
            # Get marketplace ID
            conn = get_db_connection()
            marketplace = conn.execute(
                'SELECT id FROM api_sites WHERE name = ?', 
                ('Ozon',)
            ).fetchone()
            
            if not marketplace:
                conn.close()
                return {'success': False, 'error': 'Ozon marketplace not found'}
            
            marketplace_id = marketplace['id']
            
            # Fetch orders from Ozon
            orders_response = self.ozon_client.get_orders(limit=100, since=since_date)
            
            if not orders_response or 'result' not in orders_response:
                conn.close()
                return {'success': False, 'error': 'No orders response from Ozon'}
            
            # Response yapısı: result.postings dizisi
            result = orders_response.get('result', {})
            orders_data = result.get('postings', [])
            
            processed = 0
            new_orders = 0
            updated_orders = 0
            
            for order in orders_data:
                try:
                    # Unfulfilled list endpoint'i zaten detaylı bilgi veriyor
                    posting_number = order.get('posting_number')
                    if not posting_number:
                        continue
                    
                    # Direkt olarak order'ı işle (ekstra detay çağrısına gerek yok)
                    result = self._process_ozon_order(marketplace_id, order, conn)
                    if result == 'new':
                        new_orders += 1
                    elif result == 'updated':
                        updated_orders += 1
                    
                    processed += 1
                
                except Exception as e:
                    logger.error(f"Error processing Ozon order {posting_number}: {str(e)}")
                    continue
            
            conn.close()
            
            return {
                'success': True,
                'processed': processed,
                'new_orders': new_orders,
                'updated_orders': updated_orders
            }
            
        except Exception as e:
            logger.error(f"Error syncing Ozon orders: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _process_ozon_order(self, marketplace_id, order_data, conn):
        """Process and save Ozon order to database"""
        try:
            posting_number = order_data.get('posting_number')
            
            # Check if order exists
            existing_order = conn.execute('''
                SELECT id, order_status FROM orders 
                WHERE marketplace_id = ? AND marketplace_order_id = ?
            ''', (marketplace_id, posting_number)).fetchone()
            
            # Extract order information
            order_info = self._extract_ozon_order_info(order_data)
            
            if existing_order:
                # Update existing order
                if existing_order['order_status'] != order_info['order_status']:
                    # Status changed - update and log
                    self._update_order_status(
                        existing_order['id'], 
                        existing_order['order_status'], 
                        order_info['order_status'],
                        'marketplace_sync',
                        conn
                    )
                
                # Update order details with financial data
                conn.execute('''
                    UPDATE orders SET
                        order_status = ?, payment_status = ?, total_amount = ?,
                        customer_name = ?, customer_email = ?, customer_phone = ?,
                        shipping_address = ?, billing_address = ?, tracking_number = ?,
                        shipped_date = ?, delivered_date = ?, marketplace_data = ?,
                        original_amount = ?, discount_amount = ?, commission_amount = ?, net_payout = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (
                    order_info['order_status'], order_info['payment_status'], 
                    order_info['total_amount'], order_info['customer_name'],
                    order_info['customer_email'], order_info['customer_phone'],
                    order_info['shipping_address'], order_info['billing_address'],
                    order_info['tracking_number'], order_info['shipped_date'],
                    order_info['delivered_date'], json.dumps(order_data),
                    order_info['original_amount'], order_info['discount_amount'],
                    order_info['commission_amount'], order_info['net_payout'],
                    existing_order['id']
                ))
                
                conn.commit()
                return 'updated'
            
            else:
                # Create new order with financial data
                cursor = conn.execute('''
                    INSERT INTO orders (
                        marketplace_id, marketplace_order_id, order_number,
                        order_status, payment_status, total_amount, currency,
                        customer_name, customer_email, customer_phone,
                        shipping_address, billing_address, tracking_number,
                        shipping_method, order_date, shipped_date, delivered_date,
                        marketplace_data, original_amount, discount_amount, 
                        commission_amount, net_payout
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    marketplace_id, posting_number, order_info['order_number'],
                    order_info['order_status'], order_info['payment_status'],
                    order_info['total_amount'], order_info['currency'],
                    order_info['customer_name'], order_info['customer_email'],
                    order_info['customer_phone'], order_info['shipping_address'],
                    order_info['billing_address'], order_info['tracking_number'],
                    order_info['shipping_method'], order_info['order_date'],
                    order_info['shipped_date'], order_info['delivered_date'],
                    json.dumps(order_data), order_info['original_amount'],
                    order_info['discount_amount'], order_info['commission_amount'],
                    order_info['net_payout']
                ))
                
                order_id = cursor.lastrowid
                
                # Process order items with financial data
                self._process_order_items(order_id, order_data.get('products', []), 
                                        order_data.get('financial_data', {}), conn)
                
                # Log status history
                conn.execute('''
                    INSERT INTO order_status_history (order_id, new_status, changed_by, change_reason)
                    VALUES (?, ?, ?, ?)
                ''', (order_id, order_info['order_status'], 'system', 'Order created from marketplace sync'))
                
                conn.commit()
                return 'new'
                
        except Exception as e:
            logger.error(f"Error processing Ozon order: {str(e)}")
            conn.rollback()
            return 'error'
    
    def _extract_ozon_order_info(self, order_data):
        """Extract order information from Ozon API response"""
        # Map Ozon statuses to our statuses
        status_mapping = {
            'awaiting_registration': 'pending',
            'acceptance_in_progress': 'confirmed',
            'awaiting_approve': 'confirmed',
            'awaiting_packaging': 'processing',
            'awaiting_deliver': 'processing',
            'arbitration': 'processing',
            'client_arbitration': 'processing',
            'delivering': 'shipped',
            'driver_pickup': 'shipped',
            'delivered': 'delivered',
            'cancelled': 'cancelled',
            'not_accepted': 'cancelled',
            'sent_by_seller': 'shipped'
        }
        
        ozon_status = order_data.get('status', 'pending')
        order_status = status_mapping.get(ozon_status, 'pending')
        
        # Extract customer info from analytics_data or addressee
        analytics_data = order_data.get('analytics_data', {})
        addressee = order_data.get('addressee', {})
        
        # Customer bilgileri genelde null geliyor, analytics_data'dan şehir/bölge alınabilir
        customer_city = analytics_data.get('city', '')
        customer_region = analytics_data.get('region', '')
        
        # Calculate total amount and financial data
        total_amount = 0
        original_amount = 0
        discount_amount = 0
        commission_amount = 0
        net_payout = 0
        
        financial_data = order_data.get('financial_data', {})
        if financial_data:
            financial_products = financial_data.get('products', [])
            for product in financial_products:
                # Financial data calculations
                quantity = int(product.get('quantity', 1))
                old_price = float(product.get('old_price', 0)) * quantity
                price = float(product.get('price', 0)) * quantity
                discount = float(product.get('total_discount_value', 0))
                commission = float(product.get('commission_amount', 0))
                payout = float(product.get('payout', 0))
                
                original_amount += old_price
                total_amount += price
                discount_amount += discount
                commission_amount += commission
                net_payout += payout
        else:
            # Fallback: normal products dizisinden hesapla
            products = order_data.get('products', [])
            for product in products:
                price = float(product.get('price', 0))
                quantity = int(product.get('quantity', 1))
                total_amount += price * quantity
                original_amount += price * quantity  # No old_price in basic products
        
        # Delivery method bilgileri
        delivery_method = order_data.get('delivery_method', {})
        
        # Müşteri bilgisi oluştur (analytics_data'dan)
        customer_name = f"{customer_region} - {customer_city}" if customer_region or customer_city else "Ozon Customer"
        
        # Para birimi - Ozon genelde RUB kullanır
        currency_code = 'RUB'
        if order_data.get('products'):
            currency_code = order_data['products'][0].get('currency_code', 'RUB')
        
        return {
            'order_number': order_data.get('order_number', ''),
            'order_status': order_status,
            'payment_status': 'paid' if order_status not in ['pending', 'cancelled'] else 'pending',
            'total_amount': total_amount,
            'currency': currency_code,
            'customer_name': customer_name,
            'customer_email': '',  # Ozon API'de email bilgisi yok
            'customer_phone': '',  # Ozon API'de telefon bilgisi yok
            'shipping_address': json.dumps({
                'city': customer_city,
                'region': customer_region,
                'delivery_type': analytics_data.get('delivery_type', ''),
                'warehouse': delivery_method.get('warehouse', '')
            }),
            'billing_address': json.dumps({
                'city': customer_city,
                'region': customer_region
            }),
            'tracking_number': order_data.get('tracking_number', ''),
            'shipping_method': delivery_method.get('name', ''),
            'order_date': order_data.get('in_process_at', order_data.get('shipment_date')),
            'shipped_date': order_data.get('delivering_date'),
            'delivered_date': None,  # Bu bilgi unfulfilled listesinde yok
            # Financial data
            'original_amount': original_amount,
            'discount_amount': discount_amount,
            'commission_amount': commission_amount,
            'net_payout': net_payout
        }
    
    def _process_order_items(self, order_id, products, financial_data, conn):
        """Process and save order items with financial data"""
        # Create a lookup map for financial data by product_id/sku
        financial_products_map = {}
        if financial_data and 'products' in financial_data:
            for fin_product in financial_data['products']:
                # Map by both product_id and sku for better matching
                product_id = str(fin_product.get('product_id', ''))
                sku = str(fin_product.get('sku', ''))
                if product_id:
                    financial_products_map[product_id] = fin_product
                if sku:
                    financial_products_map[sku] = fin_product
        
        for product in products:
            try:
                # Try to find matching local product by SKU
                sku = str(product.get('sku', ''))  # Ozon'da sku sayı olarak geliyor
                offer_id = product.get('offer_id', '')  # Bu seller'ın kendi SKU'su
                local_product = None
                marketplace_product = None
                
                # Önce offer_id ile eşleştirmeyi dene (daha doğru)
                if offer_id:
                    local_product = conn.execute(
                        'SELECT id FROM products WHERE sku = ?', (offer_id,)
                    ).fetchone()
                
                # Bulamazsan Ozon SKU ile dene
                if not local_product and sku:
                    marketplace_product = conn.execute(
                        'SELECT id FROM marketplace_products WHERE external_product_id = ?', (sku,)
                    ).fetchone()
                
                # Get financial data for this product
                fin_data = financial_products_map.get(sku, {})
                
                unit_price = float(product.get('price', 0))
                quantity = int(product.get('quantity', 1))
                total_price = unit_price * quantity
                
                # Extract financial fields
                original_price = float(fin_data.get('old_price', unit_price))
                discount_amount = float(fin_data.get('total_discount_value', 0))
                discount_percent = float(fin_data.get('total_discount_percent', 0))
                commission_amount = float(fin_data.get('commission_amount', 0))
                commission_percent = float(fin_data.get('commission_percent', 0))
                net_payout = float(fin_data.get('payout', total_price))
                
                conn.execute('''
                    INSERT INTO order_items (
                        order_id, product_id, marketplace_product_id, sku,
                        product_name, quantity, unit_price, total_price,
                        marketplace_item_data, original_price, discount_amount,
                        discount_percent, commission_amount, commission_percent, net_payout
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    order_id,
                    local_product['id'] if local_product else None,
                    marketplace_product['id'] if marketplace_product else None,
                    sku,
                    product.get('name', ''),
                    quantity,
                    unit_price,
                    total_price,
                    json.dumps(product),
                    original_price,
                    discount_amount,
                    discount_percent,
                    commission_amount,
                    commission_percent,
                    net_payout
                ))
                
            except Exception as e:
                logger.error(f"Error processing order item: {str(e)}")
                continue
    
    def _update_order_status(self, order_id, old_status, new_status, changed_by, conn):
        """Update order status and log history"""
        # Update order
        conn.execute('''
            UPDATE orders SET order_status = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE id = ?
        ''', (new_status, order_id))
        
        # Log status change
        conn.execute('''
            INSERT INTO order_status_history (order_id, old_status, new_status, changed_by)
            VALUES (?, ?, ?, ?)
        ''', (order_id, old_status, new_status, changed_by))
    
    def get_orders_with_filters(self, marketplace_id=None, status=None, customer_search=None, 
                               date_from=None, date_to=None, limit=50, offset=0):
        """Get orders with various filters"""
        conn = get_db_connection()
        
        query = '''
            SELECT o.*, m.name as marketplace_name,
                   COUNT(oi.id) as item_count
            FROM orders o
            LEFT JOIN api_sites m ON o.marketplace_id = m.id
            LEFT JOIN order_items oi ON o.id = oi.order_id
            WHERE 1=1
        '''
        params = []
        
        if marketplace_id:
            query += ' AND o.marketplace_id = ?'
            params.append(marketplace_id)
        
        if status:
            query += ' AND o.order_status = ?'
            params.append(status)
        
        if customer_search:
            query += ' AND (o.customer_name LIKE ? OR o.customer_email LIKE ?)'
            params.extend([f'%{customer_search}%', f'%{customer_search}%'])
        
        if date_from:
            query += ' AND o.order_date >= ?'
            params.append(date_from)
        
        if date_to:
            query += ' AND o.order_date <= ?'
            params.append(date_to)
        
        query += ' GROUP BY o.id ORDER BY o.created_at DESC LIMIT ? OFFSET ?'
        params.extend([limit, offset])
        
        orders = conn.execute(query, params).fetchall()
        
        # Get total count
        count_query = query.split('GROUP BY')[0].replace('SELECT o.*, m.name as marketplace_name, COUNT(oi.id) as item_count', 'SELECT COUNT(DISTINCT o.id)')
        count_result = conn.execute(count_query, params[:-2]).fetchone()
        total_count = count_result[0] if count_result and count_result[0] is not None else 0
        
        conn.close()
        
        return {
            'orders': [dict(order) for order in orders],
            'total_count': total_count
        }
    
    def get_order_by_id(self, order_id):
        """Get detailed order information by ID"""
        conn = get_db_connection()
        
        # Get order details
        order = conn.execute('''
            SELECT o.*, m.name as marketplace_name
            FROM orders o
            LEFT JOIN api_sites m ON o.marketplace_id = m.id
            WHERE o.id = ?
        ''', (order_id,)).fetchone()
        
        if not order:
            conn.close()
            return None
        
        # Get order items
        items = conn.execute('''
            SELECT oi.*, p.product_name as local_product_name
            FROM order_items oi
            LEFT JOIN products p ON oi.product_id = p.id
            WHERE oi.order_id = ?
        ''', (order_id,)).fetchall()
        
        # Get status history
        history = conn.execute('''
            SELECT * FROM order_status_history
            WHERE order_id = ?
            ORDER BY created_at DESC
        ''', (order_id,)).fetchall()
        
        conn.close()
        
        return {
            'order': dict(order),
            'items': [dict(item) for item in items],
            'history': [dict(h) for h in history]
        }
    
    def update_order_status_manually(self, order_id, new_status, notes=None, changed_by='user'):
        """Manually update order status"""
        conn = get_db_connection()
        
        try:
            # Get current status
            current = conn.execute(
                'SELECT order_status FROM orders WHERE id = ?', (order_id,)
            ).fetchone()
            
            if not current:
                conn.close()
                return {'success': False, 'error': 'Order not found'}
            
            old_status = current['order_status']
            
            if old_status == new_status:
                conn.close()
                return {'success': False, 'error': 'Status is already set to this value'}
            
            # Update order status
            self._update_order_status(order_id, old_status, new_status, changed_by, conn)
            
            # Add notes if provided
            if notes:
                conn.execute('''
                    UPDATE orders SET notes = ?, updated_at = CURRENT_TIMESTAMP 
                    WHERE id = ?
                ''', (notes, order_id))
            
            conn.commit()
            conn.close()
            
            return {'success': True, 'message': 'Order status updated successfully'}
            
        except Exception as e:
            conn.rollback()
            conn.close()
            return {'success': False, 'error': str(e)}
    
    def get_order_statistics(self):
        """Get order statistics for dashboard"""
        conn = get_db_connection()
        
        # Basic counts
        stats = {}
        
        # Total orders
        stats['total_orders'] = conn.execute('SELECT COUNT(*) FROM orders').fetchone()[0]
        
        # Orders by status
        status_counts = conn.execute('''
            SELECT order_status, COUNT(*) as count
            FROM orders
            GROUP BY order_status
        ''').fetchall()
        
        stats['by_status'] = {row['order_status']: row['count'] for row in status_counts}
        
        # Orders by marketplace
        marketplace_counts = conn.execute('''
            SELECT m.name, COUNT(*) as count
            FROM orders o
            JOIN api_sites m ON o.marketplace_id = m.id
            GROUP BY m.name
        ''').fetchall()
        
        stats['by_marketplace'] = {row['name']: row['count'] for row in marketplace_counts}
        
        # Recent orders (last 7 days)
        stats['recent_orders'] = conn.execute('''
            SELECT COUNT(*) FROM orders
            WHERE created_at >= datetime('now', '-7 days')
        ''').fetchone()[0]
        
        # Total revenue
        stats['total_revenue'] = conn.execute('''
            SELECT COALESCE(SUM(total_amount), 0) FROM orders
            WHERE order_status IN ('delivered', 'shipped')
        ''').fetchone()[0]
        
        conn.close()
        return stats
    
    def _sync_amazon_orders(self, since_date):
        """Sync orders from Amazon (placeholder)"""
        # TODO: Implement Amazon orders sync
        return {'success': False, 'error': 'Amazon orders sync not implemented yet'}