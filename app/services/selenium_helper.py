"""
Selenium helper for dynamic content scraping
"""
import logging
from typing import Optional, Dict, Any
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.core.os_manager import ChromeType
from bs4 import BeautifulSoup
import time

logger = logging.getLogger(__name__)

class SeleniumHelper:
    """Helper class for Selenium-based web scraping"""
    
    def __init__(self):
        self.driver = None
        self.wait_timeout = 10
        
    def _get_chrome_options(self) -> Options:
        """Configure Chrome options for headless browsing"""
        options = Options()
        options.add_argument('--headless')  # Run in background
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # Set Chrome binary location for macOS
        import os
        import platform
        if platform.system() == 'Darwin':  # macOS
            chrome_paths = [
                '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
                '/Applications/Chrome.app/Contents/MacOS/Chrome',
                '/Applications/Chromium.app/Contents/MacOS/Chromium'
            ]
            for path in chrome_paths:
                if os.path.exists(path):
                    options.binary_location = path
                    break
        
        return options
    
    def _init_driver(self):
        """Initialize Chrome driver with system ChromeDriver"""
        if not self.driver:
            try:
                import os
                import platform
                
                # Use system ChromeDriver
                if platform.system() == 'Darwin':  # macOS
                    # Check common locations for ChromeDriver
                    chromedriver_paths = [
                        '/opt/homebrew/bin/chromedriver',  # Homebrew ARM64
                        '/usr/local/bin/chromedriver',     # Homebrew Intel
                        '/usr/bin/chromedriver'             # System
                    ]
                    
                    driver_path = None
                    for path in chromedriver_paths:
                        if os.path.exists(path):
                            driver_path = path
                            logger.info(f"Found ChromeDriver at: {path}")
                            break
                    
                    if not driver_path:
                        # Fallback to WebDriverManager
                        logger.warning("System ChromeDriver not found, using WebDriverManager")
                        driver_path = ChromeDriverManager().install()
                else:
                    # For other systems, use WebDriverManager
                    driver_path = ChromeDriverManager().install()
                
                service = Service(driver_path)
                self.driver = webdriver.Chrome(service=service, options=self._get_chrome_options())
                # Execute script to remove webdriver property
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
                logger.info("Chrome driver initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize Chrome driver: {e}")
                raise
    
    def get_page_with_js(self, url: str, wait_for_selector: Optional[str] = None) -> Optional[BeautifulSoup]:
        """
        Load a page with JavaScript rendering and return BeautifulSoup object
        
        Args:
            url: The URL to load
            wait_for_selector: Optional CSS selector to wait for before returning
            
        Returns:
            BeautifulSoup object or None if failed
        """
        try:
            self._init_driver()
            
            logger.info(f"Loading page with Selenium: {url}")
            self.driver.get(url)
            
            # Wait for specific element if selector provided
            if wait_for_selector:
                try:
                    WebDriverWait(self.driver, self.wait_timeout).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, wait_for_selector))
                    )
                    logger.debug(f"Found element: {wait_for_selector}")
                except TimeoutException:
                    logger.warning(f"Timeout waiting for selector: {wait_for_selector}")
            else:
                # Default wait for page to load
                time.sleep(5)
            
            # Scroll to load lazy-loaded content
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
            time.sleep(1)
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(1)
            
            # Get page source and create BeautifulSoup object
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            logger.info("Page loaded successfully with Selenium")
            return soup
            
        except WebDriverException as e:
            logger.error(f"WebDriver error: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in get_page_with_js: {e}")
            return None
    
    def extract_trendyol_description(self, url: str) -> Optional[str]:
        """
        Extract product description from Trendyol using Selenium
        
        Args:
            url: Trendyol product URL
            
        Returns:
            Product description or None
        """
        try:
            # Initialize driver if not already done
            self._init_driver()
            
            logger.info(f"Loading Trendyol page with Selenium: {url}")
            self.driver.get(url)
            
            # Wait for page to load
            time.sleep(3)
            
            # First, try to click on ALL tabs/accordions to reveal hidden content
            try:
                # JavaScript to find and click ALL expandable elements
                click_all_tabs_js = """
                function clickAllExpandables() {
                    let clicked = 0;
                    
                    // Click all tab-like elements
                    const tabSelectors = [
                        'button[class*="tab"]',
                        'button[class*="accordion"]',
                        'div[class*="tab-item"]',
                        'div[class*="accordion-header"]',
                        '[role="tab"]',
                        '[data-toggle="tab"]',
                        '[data-toggle="collapse"]',
                        '.collapsible',
                        '.expandable'
                    ];
                    
                    tabSelectors.forEach(selector => {
                        document.querySelectorAll(selector).forEach(elem => {
                            try {
                                elem.click();
                                clicked++;
                            } catch(e) {}
                        });
                    });
                    
                    // Also try to click elements with description keywords
                    const descriptionKeywords = [
                        'Ürün Açıklaması', 'Açıklama', 'Detay', 'Özellikler',
                        'Ürün Bilgisi', 'Ürün Detayları', 'İçerik', 'Hakkında'
                    ];
                    
                    const allElements = document.querySelectorAll('button, a, div, span, li');
                    allElements.forEach(elem => {
                        const text = elem.innerText || elem.textContent || '';
                        if (descriptionKeywords.some(keyword => text.includes(keyword))) {
                            try {
                                elem.click();
                                clicked++;
                            } catch(e) {}
                        }
                    });
                    
                    console.log('Clicked ' + clicked + ' expandable elements');
                    return clicked > 0;
                }
                return clickAllExpandables();
                """
                
                tab_clicked = self.driver.execute_script(click_all_tabs_js)
                if tab_clicked:
                    logger.info("Clicked on description tab")
                    time.sleep(2)  # Wait for content to load
            except Exception as e:
                logger.debug(f"Tab clicking failed: {e}")
            
            # Scroll to load lazy content
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
            time.sleep(1)
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(1)
            
            # Enhanced JavaScript to extract description
            js_code = """
            function findDescription() {
                // Method 1: Try to extract from window variables (most reliable for Trendyol)
                if (window.__PRODUCT_DETAIL_APP_INITIAL_STATE__) {
                    try {
                        const state = window.__PRODUCT_DETAIL_APP_INITIAL_STATE__;
                        // Try different paths where description might be stored
                        const paths = [
                            'product.contentDescriptions',
                            'product.description',
                            'product.detail.description',
                            'productDetail.contentDescriptions',
                            'productDetail.description'
                        ];
                        
                        for (let path of paths) {
                            let value = state;
                            const parts = path.split('.');
                            for (let part of parts) {
                                if (value && value[part]) {
                                    value = value[part];
                                } else {
                                    value = null;
                                    break;
                                }
                            }
                            
                            if (value) {
                                // If it's an array of content descriptions
                                if (Array.isArray(value)) {
                                    const descriptions = value.map(item => {
                                        if (typeof item === 'object' && item.description) {
                                            return item.description;
                                        }
                                        return item;
                                    }).filter(d => d).join('\\n\\n');
                                    
                                    if (descriptions) {
                                        console.log('Found description in state:', path);
                                        return descriptions;
                                    }
                                } else if (typeof value === 'string' && value.length > 50) {
                                    console.log('Found description in state:', path);
                                    return value;
                                }
                            }
                        }
                    } catch (e) {
                        console.error('Error extracting from state:', e);
                    }
                }
                
                // Method 2: Look for description in HTML containers
                const descriptionSelectors = [
                    // Specific Trendyol selectors
                    'div[class*="detail-description"]',
                    'div[class*="product-detail-container"]',
                    'div[class*="info-wrapper-item"]',
                    'section[class*="detail"] div[class*="html-content"]',
                    'div[class*="tab-content"]:not([style*="display: none"])',
                    'div[class*="tab-pane"].active',
                    'div[data-test-id*="description"]',
                    // Legacy selectors
                    '.product-info-wrapper',
                    '.pr-in-w',
                    '.pr-in-cn',
                    '.info-wrapper',
                    '.info__wrapper'
                ];
                
                for (let selector of descriptionSelectors) {
                    const elements = document.querySelectorAll(selector);
                    for (let elem of elements) {
                        // Check if element is visible
                        const style = window.getComputedStyle(elem);
                        if (style.display === 'none' || style.visibility === 'hidden') continue;
                        
                        const text = elem.innerText || elem.textContent || '';
                        
                        // Filter out navigation, header, footer content
                        const isNavigation = elem.closest('nav, header, footer, [class*="navigation"], [class*="menu"]');
                        if (isNavigation) continue;
                        
                        // Must have substantial content
                        if (text.length > 100 && text.length < 10000) {
                            // Check for product-related keywords
                            const productKeywords = [
                                'Özellikler', 'Ürün', 'Açıklama', 'Detay', 
                                'Kullanım', 'İçerik', 'Malzeme', 'Boyut',
                                'Garanti', 'Teknik', 'Fonksiyon', 'Avantaj'
                            ];
                            
                            // Count keyword matches
                            const keywordMatches = productKeywords.filter(kw => text.includes(kw)).length;
                            
                            if (keywordMatches >= 2 || text.length > 500) {
                                console.log('Found description with selector:', selector);
                                return text.trim();
                            }
                        }
                    }
                }
                
                // Method 3: Deep search in window object for any description data
                try {
                    // Search for description in all window properties
                    for (let key in window) {
                        if (key.includes('PRODUCT') || key.includes('STATE') || key.includes('DATA')) {
                            const obj = window[key];
                            if (obj && typeof obj === 'object') {
                                const jsonStr = JSON.stringify(obj);
                                // Look for description patterns in the JSON
                                const descMatch = jsonStr.match(/"contentDescriptions":\s*\[(.*?)\]/);
                                if (descMatch) {
                                    try {
                                        const descriptions = JSON.parse('[' + descMatch[1] + ']');
                                        if (descriptions.length > 0) {
                                            const descTexts = descriptions.map(d => d.description || d).filter(d => d);
                                            if (descTexts.length > 0) {
                                                console.log('Found descriptions in window.' + key);
                                                return descTexts.join('\\n\\n');
                                            }
                                        }
                                    } catch (e) {}
                                }
                            }
                        }
                    }
                } catch (e) {
                    console.error('Deep search error:', e);
                }
                
                // Last resort - get all text from main content area
                const mainSelectors = ['main', '[role="main"]', '#product-detail-app', '.product-container'];
                for (let selector of mainSelectors) {
                    const main = document.querySelector(selector);
                    if (main) {
                        // Get all paragraphs and divs that might contain description
                        const contentElements = main.querySelectorAll('p, div, section');
                        let descriptionText = '';
                        
                        for (let elem of contentElements) {
                            const text = elem.innerText || elem.textContent || '';
                            
                            // Skip if too short or too long
                            if (text.length < 50 || text.length > 5000) continue;
                            
                            // Skip if it's price, navigation, etc.
                            if (text.includes('Sepete Ekle') || text.includes('TL') || 
                                text.includes('Favorilere') || text.includes('Kargo')) continue;
                            
                            // Look for description-like content
                            const sentences = text.split('.').length;
                            if (sentences > 2) {
                                descriptionText += text + '\n\n';
                            }
                        }
                        
                        if (descriptionText.length > 200) {
                            return descriptionText.trim();
                        }
                    }
                }
                
                return null;
            }
            return findDescription();
            """
            
            js_description = self.driver.execute_script(js_code)
            if js_description:
                logger.info(f"Found description using JavaScript: {len(js_description)} chars")
                return js_description[:2000]
            
            # Final attempt: Extract from React/Vue component data
            try:
                component_js = """
                function findReactVueData() {
                    // Find React fiber or Vue instance
                    const rootElement = document.querySelector('#product-detail-app') || 
                                      document.querySelector('[id*="app"]') || 
                                      document.body;
                    
                    // Try to access React props/state
                    for (let key in rootElement) {
                        if (key.startsWith('__react') || key.startsWith('_react')) {
                            try {
                                const fiber = rootElement[key];
                                if (fiber && fiber.memoizedProps) {
                                    const props = fiber.memoizedProps;
                                    const jsonStr = JSON.stringify(props);
                                    if (jsonStr.includes('contentDescriptions') || jsonStr.includes('description')) {
                                        console.log('Found data in React fiber');
                                        // Extract description from the stringified data
                                        const match = jsonStr.match(/"description":"([^"]+)"/);
                                        if (match) return match[1];
                                        
                                        const descMatch = jsonStr.match(/"contentDescriptions":\[(.*?)\]/);
                                        if (descMatch) {
                                            try {
                                                const descs = JSON.parse('[' + descMatch[1] + ']');
                                                return descs.map(d => d.description || d).join(' ');
                                            } catch(e) {}
                                        }
                                    }
                                }
                            } catch(e) {}
                        }
                    }
                    
                    // Try Vue instance
                    if (window.Vue || rootElement.__vue__) {
                        const vueInstance = rootElement.__vue__;
                        if (vueInstance && vueInstance.$data) {
                            const data = vueInstance.$data;
                            if (data.product && data.product.description) {
                                return data.product.description;
                            }
                        }
                    }
                    
                    return null;
                }
                return findReactVueData();
                """
                
                component_description = self.driver.execute_script(component_js)
                if component_description:
                    logger.info(f"Found description in React/Vue component: {len(component_description)} chars")
                    return component_description[:2000]
                    
            except Exception as e:
                logger.debug(f"Component data extraction failed: {e}")
            
            # If JavaScript extraction failed, try BeautifulSoup as fallback
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # Enhanced selectors for current Trendyol structure
            description_selectors = [
                'div[class*="detail-description"]',
                'div[class*="product-detail-container"]',
                'div.info__wrapper',
                'div.pr-in-cn',
                'div.pr-in-w',
                'div.info-wrapper',
                'div.product-detail-desc-content',
                'div.detail-desc-list',
                'section.detail-border div.html-content',
                'div[class*="info-wrapper"]',
                'div[class*="detail-desc"]',
                'div[class*="pr-in"]',
                'div[class*="tab-content"]',
                'div[class*="tab-pane"]'
            ]
            
            for selector in description_selectors:
                desc_elem = soup.select_one(selector)
                if desc_elem:
                    # Check if element has substantial content
                    description = desc_elem.get_text(separator='\n', strip=True)
                    if description and len(description) > 100:
                        # Filter out non-description content
                        if not any(skip in description[:100] for skip in ['Sepete Ekle', 'Favorilere', 'Kargo']):
                            logger.info(f"Found description with selector: {selector}")
                            return description[:2000]
            
            logger.warning("No description found with Selenium")
            return None
            
        except Exception as e:
            logger.error(f"Error extracting Trendyol description: {e}")
            return None
    
    def close(self):
        """Close the Selenium driver"""
        if self.driver:
            try:
                self.driver.quit()
                self.driver = None
                logger.info("Chrome driver closed")
            except Exception as e:
                logger.error(f"Error closing driver: {e}")
    
    def __del__(self):
        """Cleanup on deletion"""
        self.close()