import sqlite3
from datetime import datetime
from flask import current_app
import json

class CategoryService:
    def __init__(self, db_path=None):
        self.db_path = db_path or current_app.config.get('DATABASE_PATH', 'pazaryeri.db')
    
    def get_categories_tree(self, include_deleted=False):
        """Kategorileri hierarchical tree yapısında getir"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        
        where_clause = "" if include_deleted else "WHERE is_deleted = 0"
        
        categories = conn.execute(f'''
            SELECT id, name, parent_id, created_at, updated_at, is_deleted
            FROM categories
            {where_clause}
            ORDER BY parent_id ASC, name ASC
        ''').fetchall()
        
        conn.close()
        
        # Tree yapısı oluştur
        tree = []
        category_dict = {cat['id']: dict(cat) for cat in categories}
        
        for category in categories:
            cat_dict = dict(category)
            cat_dict['children'] = []
            
            if category['parent_id'] is None:
                tree.append(cat_dict)
            else:
                parent = category_dict.get(category['parent_id'])
                if parent:
                    if 'children' not in parent:
                        parent['children'] = []
                    parent['children'].append(cat_dict)
        
        return tree
    
    def get_category_by_id(self, category_id):
        """ID'ye göre kategori getir"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        
        category = conn.execute('''
            SELECT * FROM categories 
            WHERE id = ? AND is_deleted = 0
        ''', (category_id,)).fetchone()
        
        conn.close()
        return dict(category) if category else None
    
    def create_category(self, name, parent_id=None, user_id=None):
        """Yeni kategori oluştur"""
        conn = sqlite3.connect(self.db_path)
        
        try:
            # Aynı isimde kategori var mı kontrol et
            existing = conn.execute('''
                SELECT id FROM categories 
                WHERE name = ? AND parent_id IS ? AND is_deleted = 0
            ''', (name, parent_id)).fetchone()
            
            if existing:
                return False, "Bu isimde bir kategori zaten mevcut!"
            
            # Kategoriyi ekle
            cursor = conn.execute('''
                INSERT INTO categories (name, parent_id, created_at, updated_at)
                VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            ''', (name, parent_id))
            
            conn.commit()
            return True, cursor.lastrowid
            
        except Exception as e:
            conn.rollback()
            return False, str(e)
        finally:
            conn.close()
    
    def update_category(self, category_id, name, parent_id=None):
        """Kategori güncelle"""
        conn = sqlite3.connect(self.db_path)
        
        try:
            # Kategori mevcut mu kontrol et
            existing_category = conn.execute('''
                SELECT id FROM categories 
                WHERE id = ? AND is_deleted = 0
            ''', (category_id,)).fetchone()
            
            if not existing_category:
                return False, "Kategori bulunamadı!"
            
            # Kendisini parent olarak seçemez
            if parent_id == category_id:
                return False, "Kategori kendisinin alt kategorisi olamaz!"
            
            # Aynı isimde başka kategori var mı kontrol et
            duplicate = conn.execute('''
                SELECT id FROM categories 
                WHERE name = ? AND parent_id IS ? AND id != ? AND is_deleted = 0
            ''', (name, parent_id, category_id)).fetchone()
            
            if duplicate:
                return False, "Bu isimde bir kategori zaten mevcut!"
            
            # Güncelle
            conn.execute('''
                UPDATE categories 
                SET name = ?, parent_id = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (name, parent_id, category_id))
            
            conn.commit()
            return True, "Kategori başarıyla güncellendi!"
            
        except Exception as e:
            conn.rollback()
            return False, str(e)
        finally:
            conn.close()
    
    def soft_delete_category(self, category_id, user_id=None):
        """Kategoriyi soft delete yap"""
        conn = sqlite3.connect(self.db_path)
        
        try:
            # Kategori mevcut mu kontrol et
            category = conn.execute('''
                SELECT id, name FROM categories 
                WHERE id = ? AND is_deleted = 0
            ''', (category_id,)).fetchone()
            
            if not category:
                return False, "Kategori bulunamadı!"
            
            # Alt kategorileri kontrol et
            subcategories = conn.execute('''
                SELECT COUNT(*) FROM categories 
                WHERE parent_id = ? AND is_deleted = 0
            ''', (category_id,)).fetchone()[0]
            
            if subcategories > 0:
                return False, "Alt kategorileri olan kategori silinemez!"
            
            # Kategori eşleştirmelerini de soft delete yap
            conn.execute('''
                UPDATE category_mappings 
                SET is_deleted = 1, deleted_at = CURRENT_TIMESTAMP
                WHERE category_id = ? AND is_deleted = 0
            ''', (category_id,))
            
            # Kategoriyi soft delete yap
            conn.execute('''
                UPDATE categories 
                SET is_deleted = 1, deleted_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (category_id,))
            
            conn.commit()
            return True, "Kategori başarıyla silindi!"
            
        except Exception as e:
            conn.rollback()
            return False, str(e)
        finally:
            conn.close()
    
    def restore_category(self, category_id):
        """Silinen kategoriyi geri getir"""
        conn = sqlite3.connect(self.db_path)
        
        try:
            # Silinen kategori var mı kontrol et
            category = conn.execute('''
                SELECT id FROM categories 
                WHERE id = ? AND is_deleted = 1
            ''', (category_id,)).fetchone()
            
            if not category:
                return False, "Silinmiş kategori bulunamadı!"
            
            # Geri getir
            conn.execute('''
                UPDATE categories 
                SET is_deleted = 0, deleted_at = NULL
                WHERE id = ?
            ''', (category_id,))
            
            conn.commit()
            return True, "Kategori başarıyla geri getirildi!"
            
        except Exception as e:
            conn.rollback()
            return False, str(e)
        finally:
            conn.close()

class MarketplaceCategoryService:
    def __init__(self, db_path=None):
        self.db_path = db_path or current_app.config.get('DATABASE_PATH', 'pazaryeri.db')
    
    def get_marketplace_categories(self, marketplace_id, include_deleted=False):
        """Marketplace kategorilerini getir"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        
        where_clause = "WHERE marketplace_id = ?"
        params = [marketplace_id]
        
        if not include_deleted:
            where_clause += " AND is_deleted = 0"
        
        categories = conn.execute(f'''
            SELECT * FROM marketplace_categories
            {where_clause}
            ORDER BY level ASC, name ASC
        ''', params).fetchall()
        
        conn.close()
        return [dict(cat) for cat in categories]
    
    def create_marketplace_category(self, marketplace_id, marketplace_category_id, 
                                   name, parent_marketplace_category_id=None,
                                   category_path="", level=0, is_leaf=False,
                                   attributes_schema=None):
        """Marketplace kategorisi oluştur"""
        conn = sqlite3.connect(self.db_path)
        
        try:
            # Aynı kategori var mı kontrol et
            existing = conn.execute('''
                SELECT id FROM marketplace_categories
                WHERE marketplace_id = ? AND marketplace_category_id = ? AND is_deleted = 0
            ''', (marketplace_id, marketplace_category_id)).fetchone()
            
            if existing:
                # Güncelle
                conn.execute('''
                    UPDATE marketplace_categories
                    SET name = ?, parent_marketplace_category_id = ?, 
                        category_path = ?, level = ?, is_leaf = ?,
                        attributes_schema = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE marketplace_id = ? AND marketplace_category_id = ?
                ''', (name, parent_marketplace_category_id, category_path, 
                      level, is_leaf, json.dumps(attributes_schema) if attributes_schema else None,
                      marketplace_id, marketplace_category_id))
            else:
                # Yeni ekle
                conn.execute('''
                    INSERT INTO marketplace_categories 
                    (marketplace_id, marketplace_category_id, name, 
                     parent_marketplace_category_id, category_path, level, is_leaf,
                     attributes_schema, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ''', (marketplace_id, marketplace_category_id, name,
                      parent_marketplace_category_id, category_path, level, is_leaf,
                      json.dumps(attributes_schema) if attributes_schema else None))
            
            conn.commit()
            return True, "Marketplace kategorisi başarıyla kaydedildi!"
            
        except Exception as e:
            conn.rollback()
            return False, str(e)
        finally:
            conn.close()
    
    def sync_categories_from_api(self, marketplace_id, categories_data):
        """API'den gelen kategori verilerini senkronize et"""
        success_count = 0
        error_count = 0
        
        for category_data in categories_data:
            success, message = self.create_marketplace_category(
                marketplace_id=marketplace_id,
                marketplace_category_id=category_data.get('category_id'),
                name=category_data.get('name', ''),
                parent_marketplace_category_id=category_data.get('parent_id'),
                category_path=category_data.get('path', ''),
                level=category_data.get('level', 0),
                is_leaf=category_data.get('is_leaf', False),
                attributes_schema=category_data.get('attributes')
            )
            
            if success:
                success_count += 1
            else:
                error_count += 1
                print(f"Kategori sync hatası: {message}")
        
        return success_count, error_count

class CategoryMappingService:
    def __init__(self, db_path=None):
        self.db_path = db_path or current_app.config.get('DATABASE_PATH', 'pazaryeri.db')
    
    def get_mappings(self, category_id=None, marketplace_id=None):
        """Kategori eşleştirmelerini getir"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        
        where_conditions = ["cm.is_deleted = 0"]
        params = []
        
        if category_id:
            where_conditions.append("cm.category_id = ?")
            params.append(category_id)
        
        if marketplace_id:
            where_conditions.append("cm.marketplace_id = ?")
            params.append(marketplace_id)
        
        where_clause = " AND ".join(where_conditions)
        
        mappings = conn.execute(f'''
            SELECT cm.id, cm.category_id, cm.marketplace_id, cm.marketplace_category_id,
                   c.name as category_name, m.name as marketplace_name,
                   mc.name as marketplace_category_name, cm.created_at
            FROM category_mappings cm
            JOIN categories c ON cm.category_id = c.id
            JOIN marketplaces m ON cm.marketplace_id = m.id
            JOIN marketplace_categories mc ON cm.marketplace_category_id = mc.id
            WHERE {where_clause}
            ORDER BY c.name, m.name
        ''', params).fetchall()
        
        conn.close()
        return [dict(mapping) for mapping in mappings]
    
    def create_mapping(self, category_id, marketplace_id, marketplace_category_id):
        """Kategori eşleştirmesi oluştur"""
        conn = sqlite3.connect(self.db_path)
        
        try:
            # Aynı eşleştirme var mı kontrol et
            existing = conn.execute('''
                SELECT id FROM category_mappings
                WHERE category_id = ? AND marketplace_id = ? AND is_deleted = 0
            ''', (category_id, marketplace_id)).fetchone()
            
            if existing:
                return False, "Bu kategori için bu marketplace'te zaten eşleştirme mevcut!"
            
            # Eşleştirmeyi ekle
            conn.execute('''
                INSERT INTO category_mappings 
                (category_id, marketplace_id, marketplace_category_id, created_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (category_id, marketplace_id, marketplace_category_id))
            
            conn.commit()
            return True, "Kategori eşleştirmesi başarıyla oluşturuldu!"
            
        except Exception as e:
            conn.rollback()
            return False, str(e)
        finally:
            conn.close()
    
    def delete_mapping(self, mapping_id):
        """Kategori eşleştirmesini sil"""
        conn = sqlite3.connect(self.db_path)
        
        try:
            # Eşleştirme var mı kontrol et
            existing = conn.execute('''
                SELECT id FROM category_mappings
                WHERE id = ? AND is_deleted = 0
            ''', (mapping_id,)).fetchone()
            
            if not existing:
                return False, "Eşleştirme bulunamadı!"
            
            # Soft delete
            conn.execute('''
                UPDATE category_mappings
                SET is_deleted = 1, deleted_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (mapping_id,))
            
            conn.commit()
            return True, "Eşleştirme başarıyla silindi!"
            
        except Exception as e:
            conn.rollback()
            return False, str(e)
        finally:
            conn.close()