"""
AI Provider Service
Manages AI provider configurations and operations
"""

from app.database import get_db_connection
from datetime import datetime

class AIProviderService:
    """Service class for managing AI providers"""
    
    @staticmethod
    def get_all_providers():
        """Get all AI providers"""
        conn = get_db_connection()
        try:
            providers = conn.execute('''
                SELECT id, name, base_url, api_key, model, is_active, is_free, 
                       rate_limit, rate_limit_window, created_at, updated_at,
                       CASE WHEN api_key IS NOT NULL AND api_key != '' THEN 1 ELSE 0 END as has_credentials
                FROM ai_providers
                ORDER BY name
            ''').fetchall()
            return [dict(provider) for provider in providers]
        finally:
            conn.close()
    
    @staticmethod
    def get_provider_by_id(provider_id):
        """Get a specific AI provider by ID"""
        conn = get_db_connection()
        try:
            provider = conn.execute('''
                SELECT id, name, base_url, api_key, model, is_active, is_free, 
                       rate_limit, rate_limit_window, created_at, updated_at
                FROM ai_providers
                WHERE id = ?
            ''', (provider_id,)).fetchone()
            return dict(provider) if provider else None
        finally:
            conn.close()
    
    @staticmethod
    def create_provider(name, base_url, api_key=None, model=None, is_free=False, 
                       rate_limit=100, rate_limit_window=3600):
        """Create a new AI provider"""
        conn = get_db_connection()
        try:
            cursor = conn.execute('''
                INSERT INTO ai_providers 
                (name, base_url, api_key, model, is_free, rate_limit, rate_limit_window)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (name, base_url, api_key, model, is_free, rate_limit, rate_limit_window))
            
            conn.commit()
            return cursor.lastrowid
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
    
    @staticmethod
    def update_provider(provider_id, name, base_url, api_key=None, model=None, 
                       is_free=False, rate_limit=100, rate_limit_window=3600):
        """Update an existing AI provider"""
        conn = get_db_connection()
        try:
            # If api_key is not provided in update, keep the existing one
            if api_key is None:
                existing = conn.execute(
                    'SELECT api_key FROM ai_providers WHERE id = ?', 
                    (provider_id,)
                ).fetchone()
                if existing:
                    api_key = existing['api_key']
            
            conn.execute('''
                UPDATE ai_providers 
                SET name = ?, base_url = ?, api_key = ?, model = ?, 
                    is_free = ?, rate_limit = ?, rate_limit_window = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (name, base_url, api_key, model, is_free, rate_limit, 
                  rate_limit_window, provider_id))
            
            conn.commit()
            return True
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
    
    @staticmethod
    def toggle_provider_status(provider_id):
        """Toggle the active status of an AI provider"""
        conn = get_db_connection()
        try:
            # Get current status
            current = conn.execute(
                'SELECT is_active FROM ai_providers WHERE id = ?', 
                (provider_id,)
            ).fetchone()
            
            if not current:
                return False
            
            new_status = 0 if current['is_active'] else 1
            
            conn.execute('''
                UPDATE ai_providers 
                SET is_active = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (new_status, provider_id))
            
            conn.commit()
            return True
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
    
    @staticmethod
    def delete_provider(provider_id):
        """Delete an AI provider"""
        conn = get_db_connection()
        try:
            cursor = conn.execute(
                'DELETE FROM ai_providers WHERE id = ?',
                (provider_id,)
            )
            conn.commit()
            return cursor.rowcount > 0
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
    
    @staticmethod
    def get_active_providers():
        """Get all active AI providers with API keys"""
        conn = get_db_connection()
        try:
            providers = conn.execute('''
                SELECT id, name, base_url, api_key, model, is_free, 
                       rate_limit, rate_limit_window
                FROM ai_providers
                WHERE is_active = 1 AND api_key IS NOT NULL AND api_key != ''
                ORDER BY name
            ''').fetchall()
            return [dict(provider) for provider in providers]
        finally:
            conn.close()
    
    @staticmethod
    def get_provider_by_id(provider_id):
        """Get AI provider by ID"""
        conn = get_db_connection()
        try:
            provider = conn.execute('''
                SELECT id, name, base_url, api_key, model, is_active, is_free,
                       rate_limit, rate_limit_window, created_at, updated_at
                FROM ai_providers
                WHERE id = ?
            ''', (provider_id,)).fetchone()
            
            if provider:
                return dict(provider)
            return None
        finally:
            conn.close()
    
    @staticmethod
    def get_default_providers():
        """Get predefined AI provider configurations"""
        return [
            {
                'name': 'OpenRouter',
                'base_url': 'https://openrouter.ai/api/v1',
                'is_free': True,
                'models': ['mistralai/mistral-7b-instruct', 'meta-llama/llama-3-8b-instruct'],
                'description': 'Access to multiple open-source models'
            },
            {
                'name': 'Mistral AI',
                'base_url': 'https://api.mistral.ai/v1',
                'is_free': True,
                'models': ['mistral-tiny', 'mistral-small', 'mistral-medium'],
                'description': 'Fast and efficient language models'
            },
            {
                'name': 'Groq',
                'base_url': 'https://api.groq.com/openai/v1',
                'is_free': True,
                'models': ['mixtral-8x7b-32768', 'llama2-70b-4096'],
                'description': 'Ultra-fast inference with LPU technology'
            },
            {
                'name': 'Google Gemini',
                'base_url': 'https://generativelanguage.googleapis.com/v1beta',
                'is_free': True,
                'models': ['gemini-pro', 'gemini-pro-vision'],
                'description': 'Google\'s multimodal AI model'
            }
        ]