import sqlite3
from datetime import datetime
import time
from app.services.marketplace.amazon_api_client import AmazonAPIClient
from app.services.marketplace.ozon_api_client import OzonAPIClient
import threading
from flask import current_app

class ProductSyncService:
    def __init__(self, db_path=None):
        self.db_path = db_path or current_app.config.get('DATABASE_PATH', 'pazaryeri.db')
        self.amazon_client = AmazonAPIClient(self.db_path)
        self.ozon_client = OzonAPIClient(self.db_path)
        
    def sync_all_marketplaces(self, limit_per_marketplace=50):
        """Tüm aktif marketplace'lerden ürünleri senkronize et"""
        print("=== TÜM MARKETPLACE SENKRONIZASYONU BAŞLIYOR ===")
        
        # Aktif marketplace'leri al
        active_marketplaces = self._get_active_marketplaces()
        
        results = {}
        for marketplace in active_marketplaces:
            marketplace_name = marketplace['name']
            print(f"\n--- {marketplace_name} senkronizasyonu ---")
            
            try:
                if marketplace_name == 'Amazon':
                    result = self.amazon_client.sync_products_to_db(limit=limit_per_marketplace)
                elif marketplace_name == 'Ozon':
                    result = self.ozon_client.sync_products_to_db(limit=limit_per_marketplace)
                else:
                    print(f"{marketplace_name} için henüz client mevcut değil")
                    result = False
                
                results[marketplace_name] = result
                
                # Marketplace'ler arası bekleme
                time.sleep(2)
                
            except Exception as e:
                print(f"{marketplace_name} sync hatası: {e}")
                results[marketplace_name] = False
        
        print(f"\n=== SENKRONIZASYON SONUÇLARI ===")
        for marketplace, success in results.items():
            status = "✅ Başarılı" if success else "❌ Başarısız"
            print(f"{marketplace}: {status}")
        
        return results
    
    def sync_marketplace_by_name(self, marketplace_name, limit=50, keywords=None):
        """Belirli bir marketplace'i senkronize et"""
        print(f"=== {marketplace_name} SENKRONIZASYONU ===")
        
        try:
            if marketplace_name == 'Amazon':
                return self.amazon_client.sync_products_to_db(
                    limit=limit, 
                    keywords=keywords
                )
            elif marketplace_name == 'Ozon':
                return self.ozon_client.sync_products_to_db(limit=limit)
            else:
                print(f"{marketplace_name} için client mevcut değil")
                return False
                
        except Exception as e:
            print(f"{marketplace_name} sync hatası: {e}")
            return False
    
    def get_sync_status(self):
        """Tüm marketplace'lerin sync durumunu getir"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Son sync loglarını al
        cursor.execute('''
        SELECT 
            s.name as marketplace_name,
            l.sync_type,
            l.status,
            l.records_processed,
            l.records_success,
            l.records_failed,
            l.started_at,
            l.completed_at,
            l.error_message
        FROM api_sync_logs l
        JOIN marketplaces s ON l.marketplace_id = s.id
        WHERE s.is_deleted = 0 AND l.id IN (
            SELECT MAX(id) 
            FROM api_sync_logs 
            GROUP BY marketplace_id, sync_type
        )
        ORDER BY l.started_at DESC
        ''')
        
        logs = cursor.fetchall()
        conn.close()
        
        return [dict(zip([col[0] for col in cursor.description], row)) for row in logs]
    
    def get_marketplace_product_counts(self):
        """Marketplace'lere göre ürün sayılarını getir"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT 
            s.name as marketplace_name,
            COUNT(mp.id) as product_count,
            COUNT(CASE WHEN mp.last_updated > datetime('now', '-24 hours') THEN 1 END) as recent_updates
        FROM marketplaces s
        LEFT JOIN marketplace_products mp ON s.id = mp.marketplace_id
        WHERE s.is_active = 1 AND s.is_deleted = 0
        GROUP BY s.id, s.name
        ORDER BY product_count DESC
        ''')
        
        counts = cursor.fetchall()
        conn.close()
        
        return [dict(zip([col[0] for col in cursor.description], row)) for row in counts]
    
    def search_marketplace_products(self, search_term=None, marketplace_name=None, limit=50, offset=0):
        """Marketplace ürünlerinde arama yap"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = '''
        SELECT 
            mp.*,
            s.name as marketplace_name
        FROM marketplace_products mp
        JOIN marketplaces s ON mp.marketplace_id = s.id
        WHERE s.is_deleted = 0
        '''
        params = []
        
        if search_term:
            query += " AND (mp.title LIKE ? OR mp.brand LIKE ? OR mp.description LIKE ?)"
            search_pattern = f"%{search_term}%"
            params.extend([search_pattern, search_pattern, search_pattern])
        
        if marketplace_name:
            query += " AND s.name = ?"
            params.append(marketplace_name)
        
        query += " ORDER BY mp.last_updated DESC LIMIT ? OFFSET ?"
        params.extend([limit, offset])
        
        cursor.execute(query, params)
        products = cursor.fetchall()
        
        # Capture column names from products query before running count query
        column_names = [col[0] for col in cursor.description]
        
        # Total count için ayrı query
        count_query = '''
        SELECT COUNT(*)
        FROM marketplace_products mp
        JOIN marketplaces s ON mp.marketplace_id = s.id
        WHERE s.is_deleted = 0
        '''
        count_params = []
        
        if search_term:
            count_query += " AND (mp.title LIKE ? OR mp.brand LIKE ? OR mp.description LIKE ?)"
            search_pattern = f"%{search_term}%"
            count_params.extend([search_pattern, search_pattern, search_pattern])
        
        if marketplace_name:
            count_query += " AND s.name = ?"
            count_params.append(marketplace_name)
        
        cursor.execute(count_query, count_params)
        count_result = cursor.fetchone()
        total_count = count_result[0] if count_result else 0
        
        # Create products list using saved column names
        products_list = [dict(zip(column_names, row)) for row in products]
        
        conn.close()
        
        return products_list, total_count
    
    def get_product_mapping_suggestions(self, local_product_id):
        """Yerel ürün için marketplace ürün önerilerini getir"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Yerel ürün bilgilerini al
        cursor.execute('SELECT * FROM products WHERE id = ?', (local_product_id,))
        local_product = cursor.fetchone()
        
        if not local_product:
            return []
        
        local_product_dict = dict(zip([col[0] for col in cursor.description], local_product))
        
        # Benzer ürünleri bul (isim, marka, kategori benzerlikleri)
        cursor.execute('''
        SELECT 
            mp.*,
            s.name as marketplace_name,
            (CASE 
                WHEN mp.title LIKE ? THEN 0.4 
                ELSE 0.0 
            END +
            CASE 
                WHEN mp.brand LIKE ? THEN 0.3 
                ELSE 0.0 
            END +
            CASE 
                WHEN mp.category LIKE ? THEN 0.3 
                ELSE 0.0 
            END) as similarity_score
        FROM marketplace_products mp
        JOIN marketplaces s ON mp.marketplace_id = s.id
        WHERE s.is_deleted = 0 AND (mp.title LIKE ? OR mp.brand LIKE ? OR mp.category LIKE ?)
        ORDER BY similarity_score DESC
        LIMIT 10
        ''', (
            f"%{local_product_dict.get('product_name', '')}%",
            f"%{local_product_dict.get('brand', '')}%",
            f"%{local_product_dict.get('category', '')}%",
            f"%{local_product_dict.get('product_name', '')}%",
            f"%{local_product_dict.get('brand', '')}%",
            f"%{local_product_dict.get('category', '')}%"
        ))
        
        suggestions = cursor.fetchall()
        conn.close()
        
        return [dict(zip([col[0] for col in cursor.description], row)) for row in suggestions]
    
    def create_product_mapping(self, local_product_id, marketplace_product_id, mapping_type='manual', confidence=1.0, notes=None):
        """Ürün eşleştirmesi oluştur"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
            INSERT INTO product_mappings 
            (local_product_id, marketplace_product_id, mapping_confidence, 
             mapping_type, mapping_status, notes, created_by)
            VALUES (?, ?, ?, ?, 'active', ?, 'system')
            ''', (local_product_id, marketplace_product_id, confidence, mapping_type, notes))
            
            conn.commit()
            mapping_id = cursor.lastrowid
            print(f"Ürün eşleştirmesi oluşturuldu: ID {mapping_id}")
            return mapping_id
            
        except sqlite3.IntegrityError:
            print("Bu eşleştirme zaten mevcut")
            return None
        finally:
            conn.close()
    
    def get_product_mappings(self, local_product_id=None):
        """Ürün eşleştirmelerini getir"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        query = '''
        SELECT 
            pm.*,
            p.product_name as local_product_name,
            p.sku as local_product_sku,
            mp.title as marketplace_product_title,
            mp.external_product_id,
            s.name as marketplace_name
        FROM product_mappings pm
        LEFT JOIN products p ON pm.local_product_id = p.id
        LEFT JOIN marketplace_products mp ON pm.marketplace_product_id = mp.id
        LEFT JOIN marketplaces s ON mp.marketplace_id = s.id
        WHERE (s.is_deleted = 0 OR s.is_deleted IS NULL) AND pm.mapping_status = 'active'
        '''
        
        params = []
        if local_product_id:
            query += " AND pm.local_product_id = ?"
            params.append(local_product_id)
        
        query += " ORDER BY pm.created_at DESC"
        
        cursor.execute(query, params)
        mappings = cursor.fetchall()
        conn.close()
        
        return [dict(zip([col[0] for col in cursor.description], row)) for row in mappings]
    
    def _get_active_marketplaces(self):
        """Aktif marketplace'leri getir"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT id, name, base_url, marketplace_type, country_code
        FROM marketplaces 
        WHERE is_active = 1 AND is_deleted = 0 AND (api_key IS NOT NULL OR client_id IS NOT NULL)
        ORDER BY name
        ''')
        
        marketplaces = cursor.fetchall()
        conn.close()
        
        return [dict(zip([col[0] for col in cursor.description], row)) for row in marketplaces]
    
    def schedule_sync(self, marketplace_name=None, interval_hours=24):
        """Otomatik senkronizasyon zamanla"""
        def sync_job():
            while True:
                try:
                    if marketplace_name:
                        self.sync_marketplace_by_name(marketplace_name)
                    else:
                        self.sync_all_marketplaces()
                    
                    print(f"Sıradaki sync: {interval_hours} saat sonra")
                    time.sleep(interval_hours * 3600)  # hours to seconds
                    
                except Exception as e:
                    print(f"Scheduled sync hatası: {e}")
                    time.sleep(300)  # 5 dakika bekle ve tekrar dene
        
        # Background thread'de çalıştır
        sync_thread = threading.Thread(target=sync_job, daemon=True)
        sync_thread.start()
        marketplace_text = marketplace_name or 'Tüm marketplaces'
        print(f"Otomatik sync başlatıldı: {marketplace_text} - {interval_hours} saatte bir")

if __name__ == '__main__':
    # Test için sync service'i çalıştır
    sync_service = ProductSyncService()
    
    print("1. Tüm marketplace'leri sync et")
    print("2. Sadece Amazon sync et")
    print("3. Sadece Ozon sync et")
    print("4. Sync durumunu görüntüle")
    
    choice = input("Seçiminiz (1-4): ")
    
    if choice == '1':
        sync_service.sync_all_marketplaces(limit_per_marketplace=10)
    elif choice == '2':
        sync_service.sync_marketplace_by_name('Amazon', limit=10)
    elif choice == '3':
        sync_service.sync_marketplace_by_name('Ozon', limit=10)
    elif choice == '4':
        status = sync_service.get_sync_status()
        for log in status:
            print(f"{log['marketplace_name']}: {log['status']} - {log['records_success']}/{log['records_processed']}")
    else:
        print("Geçersiz seçim!")
