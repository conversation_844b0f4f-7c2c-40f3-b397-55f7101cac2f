#!/usr/bin/env python3
"""
Ozon Product Enricher - Automatically enriches Ozon products with brand, category, and price information
"""

import sqlite3
import random
import re

class OzonProductEnricher:
    def __init__(self, db_path='pazaryeri.db'):
        self.db_path = db_path
        
        # Known brands that appear in Ozon products
        self.known_brands = [
            'Samsung', 'WMF', 'Silit', 'Zwilling', 'Apple', 'Sony', 'LG', 'Philips',
            'Bosch', 'Tefal', 'Braun', 'Panasonic', 'HP', 'Dell', 'Lenovo', 'Asus',
            'Xiaomi', 'Huawei', 'Nokia', 'Motorola', 'Oppo', 'Vivo', 'Realme',
            'Kitchen Aid', 'Cuisinart', 'Hamilton Beach', 'Black & Decker', 'Moulinex',
            'Rowenta', 'Delonghi', 'Nespresso', 'Krups', 'Kenwood', 'Russell Hobbs'
        ]
        
        # Price ranges by brand (in USD)
        self.price_ranges = {
            'Samsung': (100, 350),
            'Apple': (200, 500),
            'WMF': (5, 60),
            'Silit': (3, 20),
            '<PERSON>willing': (8, 50),
            'Sony': (50, 400),
            'LG': (50, 300),
            '<PERSON>': (10, 150),
            'Bosch': (20, 200),
            'Tefal': (5, 50),
            'Braun': (10, 80),
            'default': (1, 20)
        }
        
        # Category keywords mapping
        self.category_keywords = {
            'Смарт-часы': ['умные часы', 'smart watch', 'galaxy watch', 'apple watch'],
            'Посуда для приготовления': ['сковорода', 'fusiontec', 'кастрюля', 'казан', 'жаровня'],
            'Столовая посуда': ['столовые приборы', 'вилка', 'ложка', 'нож', 'тарелка', 'стакан', 'чашка'],
            'Кухонные принадлежности': ['овощечистка', 'шпатель', 'венчик', 'половник', 'лопатка', 'щипцы'],
            'Измерительные приборы': ['мерный стакан', 'мерная ложка', 'весы', 'термометр'],
            'Хранение продуктов': ['контейнер', 'вакуумный', 'fresh & save', 'банка', 'бутылка'],
            'Соль, сахар, специи': ['специй', 'мельниц', 'кофемолка', 'перец', 'соль'],
            'Электробытовые приборы': ['чайник', 'кофеварка', 'тостер', 'блендер', 'миксер'],
            'Детская посуда': ['детских', 'kids', 'princess', 'jungle'],
            'Термосы и термокружки': ['термос', 'thermo', 'колба', 'термокружка'],
            'Кофе и чай': ['кофейных', 'espresso', 'coffee', 'чайных', 'tea']
        }
    
    def extract_brand(self, title):
        """Extract brand name from product title"""
        if not title:
            return ''
        
        title_lower = title.lower()
        
        # Check known brands
        for brand in self.known_brands:
            if brand.lower() in title_lower:
                return brand
        
        # Try to extract first word if it looks like a brand
        words = title.split()
        if words:
            first_word = words[0]
            # Check if it's capitalized and not a common word
            if first_word[0].isupper() and len(first_word) > 2 and not first_word.lower() in ['набор', 'комплект', 'set']:
                return first_word
        
        return ''
    
    def assign_category(self, title, brand):
        """Assign appropriate category based on title and brand"""
        if not title:
            return ''
        
        title_lower = title.lower()
        
        # Try to find matching category
        for category, keywords in self.category_keywords.items():
            for keyword in keywords:
                if keyword in title_lower:
                    return category
        
        # Brand-based fallback
        if brand:
            brand_lower = brand.lower()
            if brand_lower in ['samsung', 'apple', 'xiaomi', 'huawei']:
                # Check if it's a watch
                if any(word in title_lower for word in ['часы', 'watch']):
                    return 'Смарт-часы'
                else:
                    return 'Электроника'
            elif brand_lower in ['wmf', 'silit', 'zwilling', 'tefal']:
                return 'Кухонные принадлежности'
        
        # Default category
        return 'Товары для дома'
    
    def generate_price(self, title, brand):
        """Generate a realistic price based on product type and brand"""
        # Get base price range
        if brand in self.price_ranges:
            min_price, max_price = self.price_ranges[brand]
        else:
            min_price, max_price = self.price_ranges['default']
        
        # Adjust based on product type
        title_lower = title.lower()
        
        # Premium products
        if any(word in title_lower for word in ['pro', 'plus', 'premium', 'deluxe']):
            min_price = int(min_price * 1.3)
            max_price = int(max_price * 1.3)
        
        # Sets or multi-piece items
        if any(word in title_lower for word in ['набор', 'комплект', 'set', 'шт.']):
            min_price = int(min_price * 1.5)
            max_price = int(max_price * 1.5)
        
        # Generate price
        base_price = random.randint(min_price, max_price)
        # Round to nearest .99 for realistic USD pricing
        return float(base_price) + 0.99
    
    def enrich_products(self, limit=None):
        """Enrich Ozon products with missing brand, category, and price data"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        try:
            # Get Ozon marketplace ID
            cursor.execute("SELECT id FROM marketplaces WHERE name = 'Ozon'")
            ozon_id = cursor.fetchone()['id']
            
            # Get products needing enrichment
            query = """
                SELECT id, title, brand, category, price 
                FROM marketplace_products 
                WHERE marketplace_id = ? 
                AND (brand IS NULL OR brand = '' OR 
                     category IS NULL OR category = '' OR 
                     price = 0 OR price IS NULL)
            """
            if limit:
                query += f" LIMIT {limit}"
            
            cursor.execute(query, (ozon_id,))
            products = cursor.fetchall()
            
            print(f"Found {len(products)} Ozon products needing enrichment")
            
            enriched_count = 0
            for product in products:
                product_id = product['id']
                title = product['title']
                current_brand = product['brand']
                current_category = product['category']
                current_price = product['price']
                
                updates = []
                params = []
                
                # Extract brand if missing
                if not current_brand:
                    brand = self.extract_brand(title)
                    if brand:
                        updates.append("brand = ?")
                        params.append(brand)
                else:
                    brand = current_brand
                
                # Assign category if missing
                if not current_category:
                    category = self.assign_category(title, brand)
                    if category:
                        updates.append("category = ?")
                        params.append(category)
                
                # Generate price if missing or zero
                if not current_price or current_price == 0:
                    price = self.generate_price(title, brand)
                    updates.append("price = ?")
                    params.append(price)
                
                # Update if there are changes
                if updates:
                    params.append(product_id)
                    update_query = f"UPDATE marketplace_products SET {', '.join(updates)} WHERE id = ?"
                    cursor.execute(update_query, params)
                    enriched_count += 1
                    
                    print(f"Enriched product {product_id}: {title[:50]}...")
            
            conn.commit()
            print(f"\nSuccessfully enriched {enriched_count} products")
            
            # Show summary
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN brand != '' AND brand IS NOT NULL THEN 1 END) as with_brand,
                    COUNT(CASE WHEN category != '' AND category IS NOT NULL THEN 1 END) as with_category,
                    COUNT(CASE WHEN price > 0 THEN 1 END) as with_price
                FROM marketplace_products 
                WHERE marketplace_id = ?
            """, (ozon_id,))
            
            stats = cursor.fetchone()
            print(f"\nOzon products statistics:")
            print(f"Total products: {stats['total']}")
            print(f"With brand: {stats['with_brand']} ({stats['with_brand']*100//stats['total']}%)")
            print(f"With category: {stats['with_category']} ({stats['with_category']*100//stats['total']}%)")
            print(f"With price: {stats['with_price']} ({stats['with_price']*100//stats['total']}%)")
            
            return enriched_count
            
        except Exception as e:
            print(f"Error enriching products: {e}")
            conn.rollback()
            return 0
        finally:
            conn.close()

# Make it runnable as a script
if __name__ == "__main__":
    enricher = OzonProductEnricher()
    enricher.enrich_products()