import requests
import json
import logging
from bs4 import BeautifulSoup
from urllib.parse import urlparse
from app.services.ai_provider_service import AIProviderService
from typing import Dict, Optional, Any

# Configure logging
logger = logging.getLogger(__name__)

class AIProductExtractor:
    """
    AI-powered product data extraction service
    Uses configured AI providers to extract structured product data from URLs
    """
    
    def __init__(self):
        self.ai_service = AIProviderService()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        
        self.timeout = 15
        self.max_content_size = 2 * 1024 * 1024  # 2MB limit for AI processing
    
    def extract_product_data(self, url: str, ai_provider_id: int) -> Dict[str, Any]:
        """
        Extract product data from URL using AI
        """
        try:
            # Step 1: Fetch and clean the web content
            logger.info(f"Fetching content from URL: {url}")
            raw_content = self._fetch_url_content(url)
            
            if not raw_content:
                return {
                    'success': False,
                    'error': 'URL içeriği alınamadı'
                }
            
            # Step 2: Clean and prepare content for AI
            clean_content = self._clean_content_for_ai(raw_content)
            
            if len(clean_content) > 8000:  # Limit for AI processing
                clean_content = clean_content[:8000] + "..."
            
            # Step 3: Get AI provider and send request
            logger.info(f"Sending content to AI provider: {ai_provider_id}")
            ai_response = self._send_to_ai(clean_content, ai_provider_id)
            
            if not ai_response:
                return {
                    'success': False,
                    'error': 'AI servisi yanıt vermedi'
                }
            
            # Step 4: Parse AI response
            product_data = self._parse_ai_response(ai_response)
            
            if product_data:
                logger.info("Successfully extracted product data")
                return {
                    'success': True,
                    'data': product_data,
                    'ai_response': ai_response[:500] + "..." if len(ai_response) > 500 else ai_response
                }
            else:
                return {
                    'success': False,
                    'error': 'AI yanıtı parse edilemedi',
                    'ai_response': ai_response[:500] + "..." if len(ai_response) > 500 else ai_response
                }
                
        except Exception as e:
            logger.error(f"Error in extract_product_data: {str(e)}")
            return {
                'success': False,
                'error': f'Veri çıkarma hatası: {str(e)}'
            }
    
    def _fetch_url_content(self, url: str) -> Optional[str]:
        """
        Fetch and extract content from URL
        """
        try:
            # Validate URL
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                logger.error(f"Invalid URL: {url}")
                return None
            
            # Fetch content
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            # Check content size
            if len(response.content) > self.max_content_size:
                logger.warning(f"Content too large: {len(response.content)} bytes")
                return None
            
            # Parse with BeautifulSoup
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style", "noscript"]):
                script.decompose()
            
            # Extract text content
            text_content = soup.get_text()
            
            # Also try to find JSON-LD structured data
            json_ld_data = self._extract_json_ld(soup)
            if json_ld_data:
                text_content += f"\n\nSTRUCTURED DATA:\n{json.dumps(json_ld_data, ensure_ascii=False, indent=2)}"
            
            return text_content
            
        except Exception as e:
            logger.error(f"Error fetching URL content: {str(e)}")
            return None
    
    def _extract_json_ld(self, soup: BeautifulSoup) -> Optional[Dict]:
        """
        Extract JSON-LD structured data from page
        """
        try:
            json_ld_scripts = soup.find_all('script', type='application/ld+json')
            for script in json_ld_scripts:
                try:
                    data = json.loads(script.string)
                    if isinstance(data, dict) and data.get('@type') in ['Product', 'ProductGroup']:
                        return data
                    elif isinstance(data, list):
                        for item in data:
                            if isinstance(item, dict) and item.get('@type') in ['Product', 'ProductGroup']:
                                return item
                except json.JSONDecodeError:
                    continue
            return None
        except Exception:
            return None
    
    def _clean_content_for_ai(self, content: str) -> str:
        """
        Clean and prepare content for AI processing
        """
        # Remove extra whitespace and normalize
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if line and len(line) > 3:  # Skip very short lines
                cleaned_lines.append(line)
        
        cleaned_content = '\n'.join(cleaned_lines)
        
        # Remove excessive repetition
        if len(cleaned_content) > 4000:
            # Take first 2000 chars and last 2000 chars
            cleaned_content = cleaned_content[:2000] + "\n...\n" + cleaned_content[-2000:]
        
        return cleaned_content
    
    def _send_to_ai(self, content: str, ai_provider_id: int) -> Optional[str]:
        """
        Send content to AI provider for processing
        """
        try:
            # Get AI provider
            provider = self.ai_service.get_provider_by_id(ai_provider_id)
            if not provider or not provider['is_active']:
                logger.error(f"AI provider not found or inactive: {ai_provider_id}")
                return None
            
            # Prepare the prompt
            prompt = self._create_extraction_prompt(content)
            
            # Prepare API request
            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {provider["api_key"]}'
            }
            
            # Different payload formats for different providers
            if 'openrouter' in provider['base_url'].lower():
                payload = {
                    "model": provider['model'] or "mistralai/mistral-7b-instruct",
                    "messages": [
                        {"role": "user", "content": prompt}
                    ],
                    "max_tokens": 1000,
                    "temperature": 0.1
                }
            else:
                # Generic OpenAI-compatible format
                payload = {
                    "model": provider['model'] or "gpt-3.5-turbo",
                    "messages": [
                        {"role": "user", "content": prompt}
                    ],
                    "max_tokens": 1000,
                    "temperature": 0.1
                }
            
            # Send request
            url = f"{provider['base_url']}/chat/completions"
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if 'choices' in result and len(result['choices']) > 0:
                    return result['choices'][0]['message']['content']
            else:
                logger.error(f"AI API error: {response.status_code} - {response.text}")
            
            return None
            
        except Exception as e:
            logger.error(f"Error sending to AI: {str(e)}")
            return None
    
    def _create_extraction_prompt(self, content: str) -> str:
        """
        Create extraction prompt for AI
        """
        return f"""Aşağıdaki web sitesi içeriğinden ürün bilgilerini çıkar ve SADECE geçerli JSON formatında döndür. Başka hiçbir metin ekleme.

JSON formatı:
{{
    "product_name": "ürün adı",
    "brand": "marka adı",
    "model": "model",
    "price": sayısal_fiyat_veya_null,
    "currency": "TL veya USD veya EUR",
    "description": "kısa açıklama",
    "images": ["resim_url1", "resim_url2"],
    "barcode": "barkod veya null",
    "sku": "ürün kodu veya null",
    "category": "kategori",
    "weight": sayısal_ağırlık_gram_veya_null,
    "dimensions": "boyutlar veya null",
    "features": ["özellik1", "özellik2"]
}}

Önemli kurallar:
1. SADECE JSON döndür, açıklama yapma
2. Türkçe karakterleri doğru kullan
3. Fiyat varsa sayısal olarak yaz (örn: 129.99)
4. Eğer bir bilgi yoksa null kullan
5. Resim URL'leri tam URL olmalı

Web sitesi içeriği:

{content}"""
    
    def _parse_ai_response(self, ai_response: str) -> Optional[Dict[str, Any]]:
        """
        Parse AI response and extract JSON data
        """
        try:
            # Clean the response - remove markdown formatting if present
            cleaned_response = ai_response.strip()
            
            # Remove code block markers if present
            if cleaned_response.startswith('```'):
                lines = cleaned_response.split('\n')
                cleaned_response = '\n'.join(lines[1:-1])
            
            # Find JSON in the response
            json_start = cleaned_response.find('{')
            json_end = cleaned_response.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = cleaned_response[json_start:json_end]
                product_data = json.loads(json_str)
                
                # Validate required fields
                if isinstance(product_data, dict) and 'product_name' in product_data:
                    # Clean and validate data
                    return self._validate_and_clean_data(product_data)
            
            return None
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Error parsing AI response: {str(e)}")
            return None
    
    def _validate_and_clean_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate and clean extracted data
        """
        cleaned_data = {}
        
        # Required string fields
        string_fields = ['product_name', 'brand', 'model', 'description', 'category', 'currency', 'barcode', 'sku', 'dimensions']
        for field in string_fields:
            value = data.get(field)
            if value and isinstance(value, str) and value.strip() and value.lower() != 'null':
                cleaned_data[field] = value.strip()
            else:
                cleaned_data[field] = None
        
        # Numeric fields
        numeric_fields = ['price', 'weight']
        for field in numeric_fields:
            value = data.get(field)
            if value is not None:
                try:
                    cleaned_data[field] = float(value)
                except (ValueError, TypeError):
                    cleaned_data[field] = None
            else:
                cleaned_data[field] = None
        
        # Array fields
        array_fields = ['images', 'features']
        for field in array_fields:
            value = data.get(field)
            if value and isinstance(value, list):
                cleaned_data[field] = [str(item).strip() for item in value if item and str(item).strip()]
            else:
                cleaned_data[field] = []
        
        return cleaned_data
    
    def get_active_ai_providers(self) -> list:
        """
        Get list of active AI providers for selection
        """
        try:
            providers = self.ai_service.get_all_providers()
            active_providers = []
            
            for p in providers:
                if p['is_active']:
                    # Include providers even without API key for testing/configuration
                    provider_info = {
                        'id': p['id'],
                        'name': p['name'],
                        'model': p['model'] or 'Default Model',
                        'is_free': p['is_free'],
                        'has_api_key': bool(p.get('api_key') and p['api_key'].strip())
                    }
                    active_providers.append(provider_info)
            
            return active_providers
        except Exception as e:
            logger.error(f"Error getting active AI providers: {str(e)}")
            return []