import requests
import json
import time
from datetime import datetime, timedelta
import sqlite3
from app.services.api_manager import APIManager
from flask import current_app

class OzonAPIClient:
    def __init__(self, db_path=None):
        self.db_path = db_path or current_app.config.get('DATABASE_PATH', 'pazaryeri.db')
        self.api_manager = APIManager(self.db_path)
        self.base_url = "https://api-seller.ozon.ru"
        
    def get_credentials(self):
        """Ozon API kimlik bilgilerini al"""
        return self.api_manager.get_api_credentials('Ozon')
    
    def make_request(self, endpoint, method='GET', data=None, params=None):
        """Ozon API'ye request gönder"""
        credentials = self.get_credentials()
        if not credentials or not credentials['client_id'] or not credentials['api_key']:
            print("Ozon API kimlik bilgileri eksik!")
            print("Lütfen API Siteleri sayfasından Ozon için Client ID ve API Key girin.")
            return None
        
        url = f"{self.base_url}{endpoint}"
        headers = {
            'Client-Id': credentials['client_id'],
            'Api-Key': credentials['api_key'],
            'Content-Type': 'application/json'
        }
        
        try:
            if method == 'GET':
                response = requests.get(url, headers=headers, params=params, timeout=30)
            elif method == 'POST':
                response = requests.post(url, headers=headers, json=data, timeout=30)
            
            print(f"Ozon API Request: {method} {url}")
            print(f"Request Data: {json.dumps(data, ensure_ascii=False) if data else 'None'}")
            print(f"Response Status: {response.status_code}")
            
            # Rate limiting için bekleme
            time.sleep(1.0)  # Ozon için biraz daha uzun bekleme
            
            if response.status_code == 200:
                result = response.json()
                print(f"Response Success: {len(result.get('result', {}).get('items', [])) if 'result' in result and 'items' in result.get('result', {}) else 'No items'} items")
                return result
            elif response.status_code == 401:
                print(f"Ozon API Authentication Error: Geçersiz API kimlik bilgileri")
                return None
            elif response.status_code == 403:
                print(f"Ozon API Permission Error: Bu endpoint için yetkiniz yok")
                return None
            elif response.status_code == 404:
                print(f"Ozon API Endpoint Not Found: {endpoint}")
                # v2/v3 otomatik fallback kaldırıldı - elle yönetilecek
                return None
            elif response.status_code == 429:
                print(f"Ozon API Rate Limit: Çok fazla istek! 5 saniye bekleniyor...")
                time.sleep(5)
                return None
            else:
                print(f"Ozon API Error {response.status_code}: {response.text[:500]}")
                return None
                
        except requests.exceptions.Timeout:
            print(f"Ozon API Timeout: İstek zaman aşımına uğradı")
            return None
        except requests.exceptions.ConnectionError:
            print(f"Ozon API Connection Error: Bağlantı hatası")
            return None
        except Exception as e:
            print(f"Ozon API request hatası: {e}")
            return None
    
    def get_product_list(self, limit=100, offset=0, filter_options=None):
        """Ozon'dan ürün listesini çek"""
        endpoint = "/v3/product/list"  # v3 kullan
        
        data = {
            "limit": limit,
            "offset": offset
        }
        
        if filter_options:
            data["filter"] = filter_options
        else:
            # Varsayılan filtre - tüm ürünleri göster
            data["filter"] = {
                "visibility": "ALL"
            }
        
        return self.make_request(endpoint, method='POST', data=data)
    
    def get_product_info(self, product_id=None, offer_id=None, sku=None):
        """Ürün detay bilgilerini çek"""
        endpoint = "/v2/product/info"
        
        data = {}
        if product_id:
            data["product_id"] = product_id
        if offer_id:
            data["offer_id"] = offer_id
        if sku:
            data["sku"] = sku
        
        return self.make_request(endpoint, method='POST', data=data)
    
    def get_product_info_list(self, product_ids=None, offer_ids=None, limit=100):
        """Birden fazla ürünün detay bilgilerini çek"""
        endpoint = "/v2/product/info/list"  # v2 kullan detaylı bilgi için
        
        data = {}
        
        if product_ids:
            data["product_id"] = product_ids
        if offer_ids:
            data["offer_id"] = offer_ids
        
        return self.make_request(endpoint, method='POST', data=data)
    
    def get_product_info_by_sku(self, sku_list):
        """SKU listesi ile ürün bilgisi çek"""
        endpoint = "/v2/product/info/list"
        
        data = {
            "sku": sku_list
        }
        
        return self.make_request(endpoint, method='POST', data=data)
    
    def get_product_info_stocks(self, filter_options=None, limit=100, offset=0):
        """Ürün stok bilgilerini çek"""
        endpoint = "/v3/product/info/stocks"
        
        data = {
            "limit": limit,
            "offset": offset
        }
        
        if filter_options:
            data["filter"] = filter_options
        
        return self.make_request(endpoint, method='POST', data=data)
    
    def get_categories(self, language="RU", category_id=None):
        """Ozon kategorilerini çek - yeni description-category endpoint kullanarak"""
        endpoint = "/v1/description-category/tree"
        
        data = {
            "language": language
        }
        
        # Eğer belirli bir kategori ID'si verilmişse ekle
        if category_id:
            data["category_id"] = category_id
        
        print(f"Ozon kategori isteği (description-category): {endpoint}")
        print(f"Request data: {data}")
        
        result = self.make_request(endpoint, method='POST', data=data)
        
        # Detaylı debug bilgisi
        if result:
            print(f"✅ Description-category endpoint başarılı, keys: {list(result.keys())}")
            if 'result' in result:
                if isinstance(result['result'], list):
                    print(f"Kategori sayısı: {len(result['result'])}")
                else:
                    print(f"Result type: {type(result['result'])}")
            else:
                print("Response'da 'result' anahtarı bulunamadı")
                print(f"Full response: {result}")
        else:
            print("❌ Description-category endpoint başarısız, legacy endpoint denenecek")
        
        return result
    
    def get_categories_legacy(self, language="RU", category_id=None):
        """Ozon kategorilerini çek - eski v1/category/tree endpoint kullanarak"""
        endpoint = "/v1/category/tree"
        
        data = {
            "language": language
        }
        
        # Eğer belirli bir kategori ID'si verilmişse ekle
        if category_id:
            data["category_id"] = category_id
        
        print(f"Ozon kategori isteği (legacy): {endpoint}")
        print(f"Request data: {data}")
        
        result = self.make_request(endpoint, method='POST', data=data)
        
        # Detaylı debug bilgisi
        if result:
            print(f"✅ Legacy endpoint başarılı, keys: {list(result.keys())}")
            if 'result' in result:
                if isinstance(result['result'], list):
                    print(f"Kategori sayısı: {len(result['result'])}")
                else:
                    print(f"Result type: {type(result['result'])}")
        else:
            print("❌ Legacy endpoint de başarısız")
        
        return result
    
    def get_category_attributes(self, category_id, language="DEFAULT"):
        """Kategori özelliklerini çek"""
        endpoint = "/v2/category/attribute"
        
        data = {
            "category_id": category_id,
            "language": language
        }
        
        return self.make_request(endpoint, method='POST', data=data)
    
    def get_product_prices(self, product_ids):
        """Ürün fiyat bilgilerini çek"""
        endpoint = "/v4/product/info/prices"
        
        data = {
            "filter": {
                "product_id": product_ids,
                "visibility": "ALL"
            }
        }
        
        return self.make_request(endpoint, method='POST', data=data)
    
    def get_product_attributes(self, product_ids):
        """Ürün özellik bilgilerini çek (marka, kategori vs.)"""
        endpoint = "/v3/products/info/attributes"
        
        data = {
            "filter": {
                "product_id": product_ids,
                "visibility": "ALL"
            }
        }
        
        return self.make_request(endpoint, method='POST', data=data)
    
    def sync_products_to_db(self, limit=100):
        """Ozon ürünlerini veritabanına senkronize et"""
        print(f"Ozon ürün senkronizasyonu başlıyor... (Limit: {limit})")
        
        # Import enricher at the top of the method to avoid circular imports
        from app.services.ozon_product_enricher import OzonProductEnricher
        
        # Sync log başlat
        sync_log_id = self._create_sync_log('product_sync', 'running')
        
        try:
            # Ozon'dan ürün listesini çek
            product_data = self.get_product_list(limit=limit)
            
            if not product_data:
                self._update_sync_log(sync_log_id, 'failed', 0, 0, 0, "API isteği başarısız")
                return False
            
            if 'result' not in product_data:
                error_msg = product_data.get('message', 'Bilinmeyen hata')
                self._update_sync_log(sync_log_id, 'failed', 0, 0, 0, f"API Hatası: {error_msg}")
                print(f"Ozon API response format hatası: {json.dumps(product_data, ensure_ascii=False)[:200]}")
                return False
            
            # Ürün listesi boş olabilir
            items = product_data.get('result', {}).get('items', [])
            if not items:
                print("Ozon'da ürün bulunamadı veya API erişim yetkisi yok")
                self._update_sync_log(sync_log_id, 'completed', 0, 0, 0, "Ürün bulunamadı")
                return True  # Bu bir hata değil, sadece ürün yok
            
            products = items
            processed = 0
            success = 0
            failed = 0
            
            # Batch olarak ürün ID'lerini topla (API efficiency için)
            product_ids = [p.get('product_id') for p in products if p.get('product_id')]
            
            # Fiyat bilgilerini toplu olarak çek
            price_data = {}
            if product_ids:
                print(f"Fiyat bilgileri çekiliyor... ({len(product_ids)} ürün)")
                prices_result = self.get_product_prices(product_ids)
                if prices_result and 'result' in prices_result:
                    for price_item in prices_result['result'].get('items', []):
                        pid = price_item.get('product_id')
                        if pid:
                            price_data[pid] = price_item
            
            # Ürün özelliklerini toplu olarak çek
            attributes_data = {}
            if product_ids:
                print(f"Ürün özellikleri çekiliyor... ({len(product_ids)} ürün)")
                attrs_result = self.get_product_attributes(product_ids)
                if attrs_result and 'result' in attrs_result:
                    for attr_item in attrs_result['result'].get('items', []):
                        pid = attr_item.get('id')
                        if pid:
                            attributes_data[pid] = attr_item
            
            # Her ürün için detaylı bilgi çek ve merge et
            for product in products:
                try:
                    product_id = product.get('product_id')
                    
                    # Description bilgisini çek
                    detail_result = self.make_request(
                        "/v1/product/info/description",
                        method='POST',
                        data={"product_id": product_id}
                    )
                    
                    # Tüm verileri birleştir
                    merged_product = {**product}
                    
                    # Description verisi
                    if detail_result and 'result' in detail_result:
                        detailed_product = detail_result['result']
                        merged_product.update({
                            'name': detailed_product.get('name', ''),
                            'description': detailed_product.get('description', ''),
                            'images': detailed_product.get('images', []),
                            'weight': detailed_product.get('weight', 0),
                            'dimensions': detailed_product.get('dimensions', {}),
                            'barcode': detailed_product.get('barcode', '')
                        })
                    
                    # Fiyat verisi
                    if product_id in price_data:
                        price_info = price_data[product_id]
                        merged_product.update({
                            'price': price_info.get('price', {}).get('price', '0'),
                            'currency_code': price_info.get('price', {}).get('currency_code', 'RUB'),
                            'old_price': price_info.get('price', {}).get('old_price', '0')
                        })
                    
                    # Attributes verisi (marka, kategori vs.)
                    if product_id in attributes_data:
                        attr_info = attributes_data[product_id]
                        # Attributes'dan brand bilgisini çıkar
                        attributes = attr_info.get('attributes', [])
                        for attr in attributes:
                            attr_id = attr.get('attribute_id')
                            if attr_id == 85:  # Brand attribute ID (genellikle 85)
                                values = attr.get('values', [])
                                if values:
                                    merged_product['brand'] = values[0].get('value', '')
                        
                        # Kategori bilgisi
                        merged_product['category_name'] = attr_info.get('category_name', '')
                    
                    if self._save_marketplace_product(merged_product, 'Ozon'):
                        success += 1
                    else:
                        failed += 1
                    
                    processed += 1
                    
                except Exception as e:
                    print(f"Ürün işleme hatası (ID: {product.get('product_id', 'N/A')}): {e}")
                    failed += 1
                    processed += 1
                
                # Rate limiting
                time.sleep(0.2)  # Daha az bekleme çünkü batch işlem yapıyoruz
            
            self._update_sync_log(sync_log_id, 'completed', processed, success, failed)
            print(f"Ozon sync tamamlandı: {success}/{processed} başarılı")
            
            # Run enrichment process after sync
            if success > 0:
                print("\n🔄 Running product enrichment...")
                enricher = OzonProductEnricher(db_path=self.db_path)
                enriched = enricher.enrich_products()
                print(f"✅ Enrichment completed: {enriched} products enriched")
            
            return True
            
        except Exception as e:
            self._update_sync_log(sync_log_id, 'failed', 0, 0, 0, str(e))
            print(f"Ozon sync hatası: {e}")
            return False
    
    def sync_categories_to_db(self):
        """Ozon kategorilerini veritabanına senkronize et"""
        print("Ozon kategori senkronizasyonu başlıyor...")
        
        try:
            categories_data = None
            
            # Fallback stratejisi: doğru endpoint ve dil kombinasyonları dene
            fallback_options = [
                {"language": "RU", "method": "description-category"},
                {"language": "EN", "method": "description-category"},
                {"language": "RU", "method": "legacy"},
                {"language": "EN", "method": "legacy"}
            ]
            
            for option in fallback_options:
                print(f"Deneniyor: {option['method']} endpoint, {option['language']} dili")
                
                try:
                    if option['method'] == 'description-category':
                        categories_data = self.get_categories(language=option['language'])
                    else:
                        # legacy endpoint için backup
                        categories_data = self.get_categories_legacy(language=option['language'])
                    
                    if categories_data and 'result' in categories_data and categories_data['result']:
                        print(f"✅ Başarılı: {option['method']} endpoint, {option['language']} dili")
                        break
                    else:
                        print(f"❌ Başarısız: {option['method']} endpoint, {option['language']} dili")
                        categories_data = None
                        
                except Exception as e:
                    print(f"❌ Hata: {option['method']} endpoint - {e}")
                    continue
            
            if not categories_data:
                print("Hiçbir endpoint/dil kombinasyonu çalışmadı")
                return False
            
            if 'result' not in categories_data:
                print(f"Response'da 'result' anahtarı bulunamadı. Response: {categories_data}")
                return False
            
            categories = categories_data['result']
            
            if not categories:
                print("Kategori listesi boş geldi")
                return False
            
            print(f"API'den {len(categories)} kategori alındı, veritabanına kaydediliyor...")
            
            processed = 0
            successful = 0
            failed = 0
            
            for i, category in enumerate(categories):
                print(f"İşleniyor: {i+1}/{len(categories)} - {category.get('category_name', 'N/A')}")
                
                if self._save_category(category, 'Ozon'):
                    successful += 1
                else:
                    failed += 1
                    print(f"Kaydetme başarısız: {category}")
                
                processed += 1
            
            print(f"Ozon kategori sync tamamlandı:")
            print(f"  Toplam işlenen: {processed}")
            print(f"  Başarılı: {successful}")
            print(f"  Başarısız: {failed}")
            
            return successful > 0
            
        except Exception as e:
            print(f"Ozon kategori sync hatası: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    
    def _save_marketplace_product(self, product_data, marketplace_name):
        """Marketplace ürününü veritabanına kaydet"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Marketplace ID'sini bul
            cursor.execute('SELECT id FROM marketplaces WHERE name = ? AND is_deleted = 0', (marketplace_name,))
            marketplace_result = cursor.fetchone()
            if not marketplace_result:
                return False
            
            marketplace_id = marketplace_result[0]
            
            # Ozon verilerini normalize et
            # Farklı veri alanlarını kontrol et (API response formatı değişebilir)
            
            # Fiyat bilgisini işle
            price_value = 0.0
            if product_data.get('price'):
                try:
                    price_str = str(product_data.get('price', '0')).replace(',', '.')
                    price_value = float(price_str)
                except (ValueError, TypeError):
                    price_value = 0.0
            
            # Brand bilgisini farklı kaynaklardan çek
            brand_value = (
                product_data.get('brand') or 
                product_data.get('manufacturer') or 
                ''
            )
            
            # Kategori bilgisini farklı kaynaklardan çek
            category_value = (
                product_data.get('category_name') or 
                product_data.get('category') or 
                ''
            )
            
            normalized_data = {
                'marketplace_id': marketplace_id,
                'external_product_id': str(product_data.get('product_id') or product_data.get('id', '')),
                'title': product_data.get('name') or product_data.get('title', ''),
                'description': product_data.get('description', ''),
                'brand': brand_value,
                'category': category_value,
                'price': price_value,
                'currency': product_data.get('currency_code', 'USD'),
                'availability': product_data.get('state') or product_data.get('status', 'unknown'),
                'condition_type': 'new',
                'images': json.dumps(product_data.get('images') or product_data.get('primary_image', [])),
                'attributes': json.dumps(product_data.get('attributes', {})),
                'seller_sku': product_data.get('offer_id') or product_data.get('sku', ''),
                'upc': product_data.get('barcode', ''),
                'model_number': product_data.get('model', ''),
                'manufacturer': brand_value,  # Use same as brand for consistency
                'weight': float(product_data.get('weight', 0.0)) / 1000 if product_data.get('weight') else 0.0  # gram to kg
            }
            
            # Debug logging için
            if normalized_data['external_product_id']:
                print(f"Saving product {normalized_data['external_product_id']}: "
                      f"Brand='{normalized_data['brand']}', "
                      f"Category='{normalized_data['category']}', "
                      f"Price={normalized_data['price']}")
            
            # Ürünü kaydet (varsa güncelle)
            cursor.execute('''
            INSERT OR REPLACE INTO marketplace_products 
            (marketplace_id, external_product_id, title, description, brand, category, 
             price, currency, availability, condition_type, images, attributes, 
             seller_sku, upc, model_number, manufacturer, weight, last_updated)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                normalized_data['marketplace_id'], normalized_data['external_product_id'],
                normalized_data['title'], normalized_data['description'], normalized_data['brand'],
                normalized_data['category'], normalized_data['price'], normalized_data['currency'],
                normalized_data['availability'], normalized_data['condition_type'],
                normalized_data['images'], normalized_data['attributes'], 
                normalized_data['seller_sku'], normalized_data['upc'], 
                normalized_data['model_number'], normalized_data['manufacturer'], 
                normalized_data['weight'], datetime.now()
            ))
            
            conn.commit()
            return True
            
        except Exception as e:
            print(f"Ozon ürün kaydetme hatası: {e}")
            return False
        finally:
            conn.close()
    
    def _save_category(self, category_data, marketplace_name):
        """Kategoriyi veritabanına kaydet"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Marketplace ID'sini bul
            cursor.execute('SELECT id FROM marketplaces WHERE name = ? AND is_deleted = 0', (marketplace_name,))
            marketplace_result = cursor.fetchone()
            if not marketplace_result:
                print(f"Marketplace '{marketplace_name}' bulunamadı")
                return False
            
            marketplace_id = marketplace_result[0]
            
            # Ozon kategori verilerini normalize et - hem v1/description-category hem legacy formatını destekle
            # description-category API'de alanlar: description_category_id, category_name, type_name
            # legacy API'de alanlar: description_category_id, category_name, type_name, parent_category_id
            
            # ID alanı - farklı endpoint'lerde farklı isimlerle gelebilir
            category_id = str(category_data.get('description_category_id') or 
                            category_data.get('category_id') or 
                            category_data.get('id', ''))
            
            # Name alanı
            category_name = (category_data.get('category_name') or 
                           category_data.get('name') or 
                           category_data.get('title', ''))
            
            # Parent ID - description-category'de olmayabilir
            parent_category_id = None
            if category_data.get('parent_category_id'):
                parent_category_id = str(category_data.get('parent_category_id'))
            elif category_data.get('parent_id'):
                parent_category_id = str(category_data.get('parent_id'))
            
            # Category path/type
            category_path = (category_data.get('type_name') or 
                           category_data.get('path') or 
                           category_data.get('type', ''))
            
            # Level'ı category_path'den hesapla (yoksa 0)
            level = category_path.count(' > ') if category_path else 0
            
            # is_leaf'i children varlığından belirle (v1'de children array var)
            children = category_data.get('children', [])
            is_leaf = len(children) == 0
            
            attributes_schema = json.dumps(category_data.get('attributes', []))
            
            # Debug log
            print(f"Kaydediliyor: ID={category_id}, Name={category_name}, Level={level}, IsLeaf={is_leaf}")
            
            # Önce var mı kontrol et
            cursor.execute('''
                SELECT id FROM marketplace_categories 
                WHERE marketplace_id = ? AND marketplace_category_id = ?
            ''', (marketplace_id, category_id))
            
            existing = cursor.fetchone()
            
            if existing:
                # Güncelle
                cursor.execute('''
                UPDATE marketplace_categories 
                SET name = ?, parent_marketplace_category_id = ?, 
                    category_path = ?, level = ?, is_leaf = ?,
                    attributes_schema = ?, updated_at = CURRENT_TIMESTAMP
                WHERE marketplace_id = ? AND marketplace_category_id = ?
                ''', (
                    category_name, parent_category_id, category_path, 
                    level, is_leaf, attributes_schema, marketplace_id, category_id
                ))
            else:
                # Yeni ekle
                cursor.execute('''
                INSERT INTO marketplace_categories 
                (marketplace_id, marketplace_category_id, name, parent_marketplace_category_id, 
                 category_path, level, is_leaf, attributes_schema, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ''', (
                    marketplace_id, category_id, category_name, parent_category_id,
                    category_path, level, is_leaf, attributes_schema
                ))
            
            conn.commit()
            
            # Eğer alt kategoriler varsa onları da recursive olarak kaydet
            if children:
                for child in children:
                    self._save_category(child, marketplace_name)
            
            return True
            
        except Exception as e:
            print(f"Kategori kaydetme hatası: {e}")
            print(f"Category data: {category_data}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            conn.close()
    
    def _create_sync_log(self, sync_type, status):
        """Sync log oluştur"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Ozon marketplace ID'sini bul
        cursor.execute('SELECT id FROM marketplaces WHERE name = ? AND is_deleted = 0', ('Ozon',))
        marketplace_result = cursor.fetchone()
        if not marketplace_result:
            raise Exception("Ozon marketplace bulunamadı")
        
        marketplace_id = marketplace_result[0]
        
        cursor.execute('''
        INSERT INTO api_sync_logs (marketplace_id, sync_type, status, started_at)
        VALUES (?, ?, ?, ?)
        ''', (marketplace_id, sync_type, status, datetime.now()))
        
        log_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return log_id
    
    def _update_sync_log(self, log_id, status, processed=0, success=0, failed=0, error_message=None):
        """Sync log güncelle"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        UPDATE api_sync_logs 
        SET status = ?, records_processed = ?, records_success = ?, records_failed = ?,
            error_message = ?, completed_at = ?
        WHERE id = ?
        ''', (status, processed, success, failed, error_message, datetime.now(), log_id))
        
        conn.commit()
        conn.close()
    
    def create_product(self, product_data):
        """Ozon'a yeni ürün oluştur"""
        endpoint = "/v3/product/import"
        
        # Zorunlu alanları kontrol et
        required_fields = ['name', 'category_id', 'offer_id', 'price', 'vat', 'description']
        for field in required_fields:
            if field not in product_data:
                print(f"Hata: {field} alanı zorunlu!")
                return None
        
        # Ozon formatına göre veri hazırla
        data = {
            "items": [{
                "attributes": product_data.get('attributes', []),
                "barcode": product_data.get('barcode', ''),
                "category_id": product_data['category_id'],
                "color_image": product_data.get('color_image', ''),
                "complex_attributes": product_data.get('complex_attributes', []),
                "currency_code": product_data.get('currency_code', 'USD'),
                "depth": product_data.get('depth', 0),
                "dimension_unit": product_data.get('dimension_unit', 'cm'),
                "height": product_data.get('height', 0),
                "images": product_data.get('images', []),
                "name": product_data['name'],
                "offer_id": product_data['offer_id'],
                "price": str(product_data['price']),
                "primary_image": product_data.get('primary_image', ''),
                "vat": str(product_data['vat']),
                "weight": product_data.get('weight', 0),
                "weight_unit": product_data.get('weight_unit', 'g'),
                "width": product_data.get('width', 0),
                "description": product_data['description']
            }]
        }
        
        print(f"Ozon'a ürün gönderiliyor: {product_data['name']}")
        result = self.make_request(endpoint, method='POST', data=data)
        
        if result and 'result' in result and 'task_id' in result['result']:
            print(f"✅ Ürün yükleme başlatıldı. Task ID: {result['result']['task_id']}")
            return result['result']['task_id']
        else:
            print(f"❌ Ürün yükleme başarısız: {result}")
            return None
    
    def check_import_status(self, task_id):
        """Ürün import durumunu kontrol et"""
        endpoint = "/v1/product/import/info"
        
        data = {
            "task_id": task_id
        }
        
        return self.make_request(endpoint, method='POST', data=data)
    
    def update_product(self, product_data):
        """Ozon'da mevcut ürünü güncelle"""
        endpoint = "/v1/product/attributes/update"
        
        # Ürün ID zorunlu
        if 'product_id' not in product_data:
            print("Hata: product_id alanı zorunlu!")
            return None
        
        data = {
            "items": [{
                "attributes": product_data.get('attributes', []),
                "product_id": product_data['product_id']
            }]
        }
        
        print(f"Ozon ürünü güncelleniyor: {product_data['product_id']}")
        return self.make_request(endpoint, method='POST', data=data)
    
    def update_product_price(self, prices):
        """Ozon'da ürün fiyatlarını güncelle"""
        endpoint = "/v1/product/import/prices"
        
        # Fiyat verilerini hazırla
        price_items = []
        for price_data in prices:
            price_items.append({
                "auto_action_enabled": price_data.get('auto_action_enabled', 'UNKNOWN'),
                "currency_code": price_data.get('currency_code', 'USD'),
                "min_price": str(price_data.get('min_price', '0')),
                "offer_id": price_data['offer_id'],
                "old_price": str(price_data.get('old_price', '0')),
                "price": str(price_data['price']),
                "price_strategy_enabled": price_data.get('price_strategy_enabled', 'UNKNOWN')
            })
        
        data = {
            "prices": price_items
        }
        
        print(f"Ozon fiyatları güncelleniyor: {len(price_items)} ürün")
        return self.make_request(endpoint, method='POST', data=data)
    
    def update_product_stocks(self, stocks):
        """Ozon'da ürün stoklarını güncelle"""
        endpoint = "/v2/product/stocks"
        
        # Stok verilerini hazırla
        stock_items = []
        for stock_data in stocks:
            stock_items.append({
                "offer_id": stock_data['offer_id'],
                "stock": stock_data['stock'],
                "warehouse_id": stock_data.get('warehouse_id', 0)  # 0 = ana depo
            })
        
        data = {
            "stocks": stock_items
        }
        
        print(f"Ozon stokları güncelleniyor: {len(stock_items)} ürün")
        return self.make_request(endpoint, method='POST', data=data)
    
    def get_product_attributes(self, category_id, attribute_type="ALL"):
        """Kategori için gereken özellikleri al"""
        endpoint = "/v3/category/attribute"
        
        data = {
            "attribute_type": attribute_type,
            "category_id": [category_id],
            "language": "DEFAULT"
        }
        
        print(f"Kategori özellikleri alınıyor: {category_id}")
        return self.make_request(endpoint, method='POST', data=data)
    
    # ORDER MANAGEMENT METHODS
    
    def get_orders(self, limit=100, offset=0, since=None, status=None):
        """Ozon'dan işlenmemiş siparişleri çek"""
        endpoint = "/v3/posting/fbs/unfulfilled/list"
        
        data = {
            "limit": min(limit, 1000),  # API max limit is 1000
            "offset": offset,
            "filter": {}
        }
        
        # Tarih filtresi - cutoff_from ve cutoff_to kullan
        if since:
            # ISO format'ı RFC3339'a çevir
            if 'T' in since and not since.endswith('Z'):
                # Eğer timezone bilgisi yoksa UTC olarak ekle
                if '+' not in since and '-' not in since.split('T')[1]:
                    since = since + 'Z'
            
            # cutoff_from: verilen tarihten itibaren
            data["filter"]["cutoff_from"] = since
            
            # cutoff_to: şu anki zaman (UTC)
            from datetime import datetime
            data["filter"]["cutoff_to"] = datetime.utcnow().isoformat() + 'Z'
        
        # Durum filtresi
        if status:
            data["filter"]["status"] = status
        
        # Ek veriler - daha detaylı bilgi almak için
        data["with"] = {
            "analytics_data": True,
            "financial_data": True,
            "barcodes": False,
            "translit": False,
            "legal_info": False
        }
        
        print(f"Ozon siparişleri çekiliyor: limit={limit}, offset={offset}")
        return self.make_request(endpoint, method='POST', data=data)
    
    def get_order_details(self, posting_number):
        """Sipariş detaylarını al"""
        endpoint = "/v3/posting/fbs/get"
        
        data = {
            "posting_number": posting_number
        }
        
        print(f"Sipariş detayları alınıyor: {posting_number}")
        return self.make_request(endpoint, method='POST', data=data)
    
    def confirm_order(self, posting_number):
        """Siparişi onayla"""
        endpoint = "/v3/posting/fbs/ship"
        
        data = {
            "posting_number": [posting_number]
        }
        
        print(f"Sipariş onaylanıyor: {posting_number}")
        return self.make_request(endpoint, method='POST', data=data)
    
    def cancel_order(self, posting_number, cancel_reason_id):
        """Siparişi iptal et"""
        endpoint = "/v3/posting/fbs/cancel"
        
        data = {
            "posting_number": posting_number,
            "cancel_reason_id": cancel_reason_id
        }
        
        print(f"Sipariş iptal ediliyor: {posting_number}")
        return self.make_request(endpoint, method='POST', data=data)
    
    def get_cancel_reasons(self):
        """İptal nedenlerini al"""
        endpoint = "/v3/posting/fbs/cancel-reason"
        
        print("İptal nedenleri alınıyor")
        return self.make_request(endpoint, method='POST', data={})
    
    def update_tracking_number(self, posting_number, tracking_number):
        """Kargo takip numarasını güncelle"""
        endpoint = "/v3/posting/fbs/tracking-number"
        
        data = {
            "posting_number": posting_number,
            "tracking_number": tracking_number
        }
        
        print(f"Takip numarası güncelleniyor: {posting_number} -> {tracking_number}")
        return self.make_request(endpoint, method='POST', data=data)
    
    def get_shipping_labels(self, posting_numbers):
        """Kargo etiketlerini al"""
        endpoint = "/v3/posting/fbs/package-label"
        
        data = {
            "posting_number": posting_numbers if isinstance(posting_numbers, list) else [posting_numbers]
        }
        
        print(f"Kargo etiketleri alınıyor: {len(data['posting_number'])} sipariş")
        return self.make_request(endpoint, method='POST', data=data)
    
    def get_order_analytics(self, date_from, date_to):
        """Sipariş analitiğini al"""
        endpoint = "/v1/analytics/data"
        
        data = {
            "date_from": date_from,
            "date_to": date_to,
            "metrics": ["revenue", "ordered_units", "hits_tocart"],
            "dimension": ["day"],
            "filters": []
        }
        
        print(f"Sipariş analitiği alınıyor: {date_from} - {date_to}")
        return self.make_request(endpoint, method='POST', data=data)
    
