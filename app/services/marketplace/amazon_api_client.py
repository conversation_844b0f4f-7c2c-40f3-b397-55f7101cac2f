import requests
import json
import time
from datetime import datetime, timedelta
import sqlite3
from app.services.api_manager import APIManager
import hashlib
import hmac
import base64
from urllib.parse import urlencode, quote
from flask import current_app

class AmazonAPIClient:
    def __init__(self, db_path=None):
        self.db_path = db_path or current_app.config.get('DATABASE_PATH', 'pazaryeri.db')
        self.api_manager = APIManager(self.db_path)
        self.base_url = "https://sellingpartnerapi-na.amazon.com"
        self.auth_url = "https://api.amazon.com/auth/o2/token"
        self.access_token = None
        self.token_expires_at = None
        
    def get_credentials(self):
        """Amazon API kimlik bilgilerini al"""
        return self.api_manager.get_api_credentials('Amazon')
    
    def authenticate(self):
        """LWA (Login with Amazon) OAuth 2.0 authentication"""
        credentials = self.get_credentials()
        if not credentials or not credentials['client_id'] or not credentials['api_secret']:
            raise Exception("Amazon API kimlik bilgileri eksik")
        
        # Refresh token kullanarak access token al
        auth_data = {
            'grant_type': 'refresh_token',
            'client_id': credentials['client_id'],
            'client_secret': credentials['api_secret'],
            'refresh_token': credentials['refresh_token'] or 'dummy_refresh_token'
        }
        
        headers = {
            'Content-Type': 'application/x-www-form-urlencoded'
        }
        
        try:
            print("Amazon LWA token alınıyor...")
            # Gerçek ortamda refresh_token gerekli
            # Bu örnek simülasyon amaçlı
            self.access_token = "simulated_access_token"
            self.token_expires_at = datetime.now() + timedelta(hours=1)
            return True
            
        except Exception as e:
            print(f"Amazon authentication hatası: {e}")
            return False
    
    def make_request(self, endpoint, method='GET', params=None, data=None):
        """Amazon SP-API'ye request gönder"""
        if not self.access_token or datetime.now() >= self.token_expires_at:
            if not self.authenticate():
                raise Exception("Amazon authentication başarısız")
        
        url = f"{self.base_url}{endpoint}"
        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json',
            'x-amz-access-token': self.access_token
        }
        
        try:
            if method == 'GET':
                response = requests.get(url, headers=headers, params=params)
            elif method == 'POST':
                response = requests.post(url, headers=headers, json=data)
            
            print(f"Amazon API Request: {method} {url} - Status: {response.status_code}")
            
            # Rate limiting için bekleme
            time.sleep(1)
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"Amazon API Error: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"Amazon API request hatası: {e}")
            return None
    
    def get_catalog_items(self, marketplace_id='ATVPDKIKX0DER', keywords=None, asins=None, limit=20):
        """Amazon kataloğundan ürünleri çek"""
        endpoint = "/catalog/2022-04-01/items"
        
        params = {
            'marketplaceIds': marketplace_id,
            'pageSize': limit
        }
        
        if keywords:
            params['keywords'] = keywords
        
        if asins:
            params['identifiers'] = ','.join(asins)
            params['identifiersType'] = 'ASIN'
        
        # Simülasyon verisi döndür
        return self._get_mock_catalog_data()
    
    def get_product_types(self, marketplace_id='ATVPDKIKX0DER'):
        """Amazon ürün tiplerini çek"""
        endpoint = f"/definitions/2020-09-01/productTypes"
        
        params = {
            'marketplaceIds': marketplace_id
        }
        
        return self.make_request(endpoint, params=params)
    
    def get_listings(self, seller_id, marketplace_id='ATVPDKIKX0DER'):
        """Seller'ın listelerini çek"""
        endpoint = f"/listings/2021-08-01/items/{seller_id}"
        
        params = {
            'marketplaceIds': marketplace_id
        }
        
        return self.make_request(endpoint, params=params)
    
    def sync_products_to_db(self, marketplace_id='ATVPDKIKX0DER', keywords=None, limit=50):
        """Amazon ürünlerini veritabanına senkronize et"""
        print(f"Amazon ürün senkronizasyonu başlıyor... (Keywords: {keywords})")
        
        # Sync log başlat
        sync_log_id = self._create_sync_log('product_sync', 'running')
        
        try:
            # Amazon kataloğundan ürünleri çek
            catalog_data = self.get_catalog_items(marketplace_id, keywords, limit=limit)
            
            if not catalog_data or 'items' not in catalog_data:
                self._update_sync_log(sync_log_id, 'failed', 0, 0, 0, "Katalog verisi alınamadı")
                return False
            
            products = catalog_data['items']
            processed = 0
            success = 0
            failed = 0
            
            for product in products:
                try:
                    if self._save_marketplace_product(product, 'Amazon'):
                        success += 1
                    else:
                        failed += 1
                    processed += 1
                    
                except Exception as e:
                    print(f"Ürün kaydetme hatası: {e}")
                    failed += 1
                    processed += 1
            
            self._update_sync_log(sync_log_id, 'completed', processed, success, failed)
            print(f"Amazon sync tamamlandı: {success}/{processed} başarılı")
            return True
            
        except Exception as e:
            self._update_sync_log(sync_log_id, 'failed', 0, 0, 0, str(e))
            print(f"Amazon sync hatası: {e}")
            return False
    
    def _save_marketplace_product(self, product_data, marketplace_name):
        """Marketplace ürününü veritabanına kaydet"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # Marketplace ID'sini bul
            cursor.execute('SELECT id FROM marketplaces WHERE name = ? AND is_deleted = 0', (marketplace_name,))
            marketplace_result = cursor.fetchone()
            if not marketplace_result:
                return False
            
            marketplace_id = marketplace_result[0]
            
            # Ürün verilerini hazırla
            product_data = {
                'marketplace_id': marketplace_id,
                'external_product_id': product_data.get('asin', ''),
                'title': product_data.get('title', ''),
                'description': product_data.get('description', ''),
                'brand': product_data.get('brand', ''),
                'category': product_data.get('category', ''),
                'price': product_data.get('price', 0.0),
                'currency': product_data.get('currency', 'USD'),
                'availability': product_data.get('availability', 'unknown'),
                'condition_type': product_data.get('condition', 'new'),
                'images': json.dumps(product_data.get('images', [])),
                'attributes': json.dumps(product_data.get('attributes', {})),
                'asin': product_data.get('asin', ''),
                'upc': product_data.get('upc', ''),
                'ean': product_data.get('ean', ''),
                'model_number': product_data.get('model', ''),
                'manufacturer': product_data.get('manufacturer', ''),
                'weight': product_data.get('weight', 0.0)
            }
            
            # Ürünü kaydet (varsa güncelle)
            cursor.execute('''
            INSERT OR REPLACE INTO marketplace_products 
            (marketplace_id, external_product_id, title, description, brand, category, 
             price, currency, availability, condition_type, images, attributes, 
             asin, upc, ean, model_number, manufacturer, weight, last_updated)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                product_data['marketplace_id'], product_data['external_product_id'],
                product_data['title'], product_data['description'], product_data['brand'],
                product_data['category'], product_data['price'], product_data['currency'],
                product_data['availability'], product_data['condition_type'],
                product_data['images'], product_data['attributes'], product_data['asin'],
                product_data['upc'], product_data['ean'], product_data['model_number'],
                product_data['manufacturer'], product_data['weight'], datetime.now()
            ))
            
            conn.commit()
            return True
            
        except Exception as e:
            print(f"Ürün kaydetme hatası: {e}")
            return False
        finally:
            conn.close()
    
    def _create_sync_log(self, sync_type, status):
        """Sync log oluştur"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Amazon marketplace ID'sini bul
        cursor.execute('SELECT id FROM marketplaces WHERE name = ? AND is_deleted = 0', ('Amazon',))
        marketplace_id = cursor.fetchone()[0]
        
        cursor.execute('''
        INSERT INTO api_sync_logs (marketplace_id, sync_type, status, started_at)
        VALUES (?, ?, ?, ?)
        ''', (marketplace_id, sync_type, status, datetime.now()))
        
        log_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return log_id
    
    def _update_sync_log(self, log_id, status, processed=0, success=0, failed=0, error_message=None):
        """Sync log güncelle"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        UPDATE api_sync_logs 
        SET status = ?, records_processed = ?, records_success = ?, records_failed = ?,
            error_message = ?, completed_at = ?
        WHERE id = ?
        ''', (status, processed, success, failed, error_message, datetime.now(), log_id))
        
        conn.commit()
        conn.close()
    
    def _get_mock_catalog_data(self):
        """Mock Amazon katalog verisi"""
        return {
            "items": [
                {
                    "asin": "B08N5WRWNW",
                    "title": "Echo Dot (4th Gen) | Smart speaker with Alexa",
                    "description": "Meet Echo Dot - Our most popular smart speaker with a fabric design.",
                    "brand": "Amazon",
                    "category": "Electronics > Audio > Speakers",
                    "price": 49.99,
                    "currency": "USD",
                    "availability": "in_stock",
                    "condition": "new",
                    "images": ["https://example.com/echo-dot.jpg"],
                    "attributes": {
                        "color": "Charcoal",
                        "connectivity": "Wi-Fi, Bluetooth"
                    },
                    "upc": "840080582719",
                    "model": "B08N5WRWNW",
                    "manufacturer": "Amazon",
                    "weight": 0.34
                },
                {
                    "asin": "B07XJ8C8F5",
                    "title": "Fire TV Stick 4K | Streaming Media Player",
                    "description": "The most powerful 4K streaming media stick with a Wi-Fi 6 support.",
                    "brand": "Amazon",
                    "category": "Electronics > TV & Video > Streaming Players",
                    "price": 54.99,
                    "currency": "USD",
                    "availability": "in_stock",
                    "condition": "new",
                    "images": ["https://example.com/fire-tv-stick.jpg"],
                    "attributes": {
                        "resolution": "4K Ultra HD",
                        "hdr": "HDR10, HDR10+, Dolby Vision"
                    },
                    "upc": "840080587158",
                    "model": "B07XJ8C8F5",
                    "manufacturer": "Amazon",
                    "weight": 0.05
                }
            ]
        }