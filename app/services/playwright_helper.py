"""
Playwright helper for dynamic content scraping - Alternative to Selenium
"""
import logging
from typing import Optional
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)

class PlaywrightHelper:
    """Helper class for Playwright-based web scraping"""
    
    def __init__(self):
        self.playwright = None
        self.browser = None
        self.initialized = False
        
    def _init_browser(self):
        """Initialize Playwright browser"""
        if not self.initialized:
            try:
                from playwright.sync_api import sync_playwright
                
                self.playwright = sync_playwright().start()
                self.browser = self.playwright.chromium.launch(
                    headless=True,
                    args=['--no-sandbox', '--disable-dev-shm-usage']
                )
                self.initialized = True
                logger.info("Playwright browser initialized successfully")
                
            except ImportError:
                logger.error("Playwright not installed. Please run: pip install playwright && playwright install chromium")
                raise
            except Exception as e:
                logger.error(f"Failed to initialize Playwright: {e}")
                raise
    
    def extract_trendyol_description(self, url: str) -> Optional[str]:
        """
        Extract product description from Trendyol using Playwright
        
        Args:
            url: Trendyol product URL
            
        Returns:
            Product description or None
        """
        try:
            self._init_browser()
            
            # Create a new page
            page = self.browser.new_page()
            
            logger.info(f"Loading Trendyol page: {url}")
            
            # Navigate to the URL
            page.goto(url, wait_until="networkidle", timeout=30000)
            
            # Wait for content to load
            page.wait_for_timeout(3000)
            
            # First, try to click on description tab/section if it exists
            try:
                # JavaScript to find and click description tab
                click_tab_js = """
                () => {
                    // Look for tab buttons containing description-related text
                    const tabSelectors = [
                        'button', 'a', 'div[role="tab"]', 'li[role="tab"]', 
                        '[class*="tab"]', '[class*="accordion"]'
                    ];
                    
                    const descriptionKeywords = [
                        'Ürün Açıklaması', 'Açıklama', 'Detay', 'Özellikler',
                        'Ürün Bilgisi', 'Ürün Detayları', 'Product Description'
                    ];
                    
                    for (let selector of tabSelectors) {
                        const elements = document.querySelectorAll(selector);
                        for (let elem of elements) {
                            const text = elem.innerText || elem.textContent || '';
                            if (descriptionKeywords.some(keyword => text.includes(keyword))) {
                                console.log('Found description tab:', text);
                                elem.click();
                                return true;
                            }
                        }
                    }
                    return false;
                }
                """
                
                tab_clicked = page.evaluate(click_tab_js)
                if tab_clicked:
                    logger.info("Clicked on description tab")
                    page.wait_for_timeout(2000)  # Wait for content to load
            except Exception as e:
                logger.debug(f"Tab clicking failed: {e}")
            
            # Scroll to load lazy content
            page.evaluate("window.scrollTo(0, document.body.scrollHeight/2)")
            page.wait_for_timeout(1000)
            page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            page.wait_for_timeout(1000)
            
            # Enhanced JavaScript to extract description
            js_extract = """
            () => {
                // First try to find specific Trendyol description containers
                const descriptionSelectors = [
                    // New Trendyol selectors based on current structure
                    'div[class*="detail-description"]',
                    'div[class*="product-detail-container"]',
                    'div[class*="info-wrapper-item"]',
                    'section[class*="detail"] div[class*="html-content"]',
                    'div[class*="tab-content"]:not([style*="display: none"])',
                    'div[class*="tab-pane"].active',
                    'div[data-test-id*="description"]',
                    // Legacy selectors
                    '.product-info-wrapper',
                    '.pr-in-w',
                    '.pr-in-cn',
                    '.info-wrapper',
                    '.info__wrapper',
                    'div.product-detail-desc-content',
                    'div.detail-desc-list'
                ];
                
                for (let selector of descriptionSelectors) {
                    const elements = document.querySelectorAll(selector);
                    for (let elem of elements) {
                        // Check if element is visible
                        const style = window.getComputedStyle(elem);
                        if (style.display === 'none' || style.visibility === 'hidden') continue;
                        
                        const text = elem.innerText || elem.textContent || '';
                        
                        // Filter out navigation, header, footer content
                        const isNavigation = elem.closest('nav, header, footer, [class*="navigation"], [class*="menu"]');
                        if (isNavigation) continue;
                        
                        // Must have substantial content
                        if (text.length > 100 && text.length < 10000) {
                            // Check for product-related keywords
                            const productKeywords = [
                                'Özellikler', 'Ürün', 'Açıklama', 'Detay', 
                                'Kullanım', 'İçerik', 'Malzeme', 'Boyut',
                                'Garanti', 'Teknik', 'Fonksiyon', 'Avantaj'
                            ];
                            
                            // Count keyword matches
                            const keywordMatches = productKeywords.filter(kw => text.includes(kw)).length;
                            
                            if (keywordMatches >= 2 || text.length > 500) {
                                console.log('Found description with selector:', selector);
                                return text.trim();
                            }
                        }
                    }
                }
                
                // Try to extract from window variables
                if (window.__PRODUCT_DETAIL_APP_INITIAL_STATE__) {
                    try {
                        const state = window.__PRODUCT_DETAIL_APP_INITIAL_STATE__;
                        if (state.product && state.product.description) {
                            return state.product.description;
                        }
                    } catch (e) {}
                }
                
                return null;
            }
            """
            
            # Try JavaScript extraction
            js_description = page.evaluate(js_extract)
            if js_description:
                logger.info(f"Found description with JavaScript: {len(js_description)} chars")
                page.close()
                return js_description[:2000]
            
            # If JavaScript failed, try direct element selection
            enhanced_selectors = [
                'div[class*="detail-description"]',
                'div[class*="product-detail-container"]',
                'div.info__wrapper',
                'div.pr-in-cn div.info-wrapper',
                'div.product-detail-desc-content',
                'div.detail-desc-list',
                'section.detail-border div.html-content',
                'div[class*="info-wrapper"]',
                'div[class*="detail-desc"]',
                'div.pr-in-w',
                'div.product-detail-container div.info',
                'div[class*="tab-content"]',
                'div[class*="tab-pane"]'
            ]
            
            for selector in enhanced_selectors:
                try:
                    element = page.query_selector(selector)
                    if element:
                        text = element.inner_text()
                        if text and len(text) > 100:
                            # Filter out non-description content
                            if not any(skip in text[:100] for skip in ['Sepete Ekle', 'Favorilere', 'Kargo']):
                                logger.info(f"Found description with selector: {selector}")
                                page.close()
                                return text[:2000]
                except:
                    continue
            
            page.close()
            logger.warning("No description found with Playwright")
            return None
            
        except Exception as e:
            logger.error(f"Error extracting Trendyol description with Playwright: {e}")
            return None
    
    def close(self):
        """Close the Playwright browser"""
        if self.browser:
            try:
                self.browser.close()
                logger.info("Browser closed")
            except Exception as e:
                logger.error(f"Error closing browser: {e}")
                
        if self.playwright:
            try:
                self.playwright.stop()
                logger.info("Playwright stopped")
            except Exception as e:
                logger.error(f"Error stopping playwright: {e}")
                
        self.initialized = False
    
    def __del__(self):
        """Cleanup on deletion"""
        self.close()