import sqlite3
from datetime import datetime, timedelta
import json
from flask import current_app

class APIManager:
    def __init__(self, db_path=None):
        self.db_path = db_path or current_app.config.get('DATABASE_PATH', 'pazaryeri.db')
    
    def list_marketplaces(self):
        """Tüm marketplace'leri listele"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT id, name, base_url, marketplace_type, country_code, is_active,
               CASE 
                   WHEN api_key IS NOT NULL THEN '✓'
                   ELSE '✗'
               END as has_credentials
        FROM marketplaces
        WHERE is_deleted = 0
        ORDER BY name
        ''')
        
        sites = cursor.fetchall()
        conn.close()
        
        print("\n=== MARKETPLACE'LER ===")
        print(f"{'ID':<4} {'İsim':<20} {'Tür':<15} {'Ülke':<6} {'Aktif':<8} {'API Key':<10} {'URL':<40}")
        print("-" * 110)
        
        for site in sites:
            active = "Evet" if site[5] else "Hayır"
            print(f"{site[0]:<4} {site[1]:<20} {site[3]:<15} {site[4]:<6} {active:<8} {site[6]:<10} {site[2]:<40}")
    
    def update_marketplace_credentials(self, site_id, client_id=None, api_key=None, api_secret=None):
        """Marketplace kimlik bilgilerini güncelle"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        update_fields = []
        params = []
        
        if client_id is not None:
            update_fields.append("client_id = ?")
            params.append(client_id)
        
        if api_key is not None:
            update_fields.append("api_key = ?")
            params.append(api_key)
        
        if api_secret is not None:
            update_fields.append("api_secret = ?")
            params.append(api_secret)
        
        if update_fields:
            update_fields.append("updated_at = ?")
            params.append(datetime.now())
            params.append(site_id)
            
            query = f"UPDATE marketplaces SET {', '.join(update_fields)} WHERE id = ? AND is_deleted = 0"
            cursor.execute(query, params)
            conn.commit()
            
            print(f"Marketplace ID {site_id} için kimlik bilgileri güncellendi.")
        
        conn.close()
    
    # Keep old method for backward compatibility
    def update_api_credentials(self, site_id, client_id=None, api_key=None, api_secret=None):
        """API kimlik bilgilerini güncelle (backward compatibility)"""
        return self.update_marketplace_credentials(site_id, client_id, api_key, api_secret)
    
    def get_marketplace_credentials(self, marketplace_name):
        """Bir marketplace'in API kimlik bilgilerini getir"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT client_id, api_key, api_secret, access_token, base_url, 
               rate_limit, rate_limit_window
        FROM marketplaces
        WHERE name = ? AND is_active = 1 AND is_deleted = 0
        ''', (marketplace_name,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return {
                'client_id': result[0],
                'api_key': result[1],
                'api_secret': result[2],
                'access_token': result[3],
                'base_url': result[4],
                'rate_limit': result[5],
                'rate_limit_window': result[6]
            }
        return None
    
    # Keep old method for backward compatibility
    def get_api_credentials(self, site_name):
        """Bir sitenin API kimlik bilgilerini getir (backward compatibility)"""
        return self.get_marketplace_credentials(site_name)
    
    def toggle_marketplace_status(self, marketplace_id):
        """Marketplace'in aktif/pasif durumunu değiştir"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT is_active FROM marketplaces WHERE id = ? AND is_deleted = 0', (marketplace_id,))
        result = cursor.fetchone()
        if not result:
            conn.close()
            print(f"Marketplace ID {marketplace_id} bulunamadı.")
            return
            
        current_status = result[0]
        new_status = 0 if current_status else 1
        cursor.execute('UPDATE marketplaces SET is_active = ?, updated_at = ? WHERE id = ? AND is_deleted = 0', 
                      (new_status, datetime.now(), marketplace_id))
        
        conn.commit()
        conn.close()
        
        status_text = "aktif" if new_status else "pasif"
        print(f"Marketplace ID {marketplace_id} artık {status_text}.")
    
    # Keep old method for backward compatibility
    def toggle_site_status(self, site_id):
        """Sitenin aktif/pasif durumunu değiştir (backward compatibility)"""
        return self.toggle_marketplace_status(site_id)
    
    def add_new_api_site(self, name, base_url, marketplace_type='marketplace', country_code='US'):
        """Yeni API sitesi ekle"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
            INSERT INTO api_sites (name, base_url, marketplace_type, country_code)
            VALUES (?, ?, ?, ?)
            ''', (name, base_url, marketplace_type, country_code))
            
            conn.commit()
            print(f"{name} başarıyla eklendi.")
        except sqlite3.IntegrityError:
            print(f"{name} zaten mevcut.")
        
        conn.close()

if __name__ == '__main__':
    manager = APIManager()
    
    while True:
        print("\n=== API YÖNETİMİ ===")
        print("1. API Sitelerini Listele")
        print("2. API Kimlik Bilgilerini Güncelle")
        print("3. Site Durumunu Değiştir (Aktif/Pasif)")
        print("4. Yeni API Sitesi Ekle")
        print("5. Çıkış")
        
        choice = input("\nSeçiminiz (1-5): ")
        
        if choice == '1':
            manager.list_api_sites()
        
        elif choice == '2':
            manager.list_api_sites()
            site_id = input("\nGüncellenecek site ID: ")
            
            print("\nBoş bırakılan alanlar güncellenmez.")
            client_id = input("Client ID: ").strip() or None
            api_key = input("API Key: ").strip() or None
            api_secret = input("API Secret: ").strip() or None
            
            try:
                manager.update_api_credentials(int(site_id), client_id, api_key, api_secret)
            except ValueError:
                print("Geçersiz ID!")
        
        elif choice == '3':
            manager.list_api_sites()
            site_id = input("\nDurum değiştirilecek site ID: ")
            
            try:
                manager.toggle_site_status(int(site_id))
            except ValueError:
                print("Geçersiz ID!")
        
        elif choice == '4':
            name = input("Site adı: ")
            base_url = input("API Base URL: ")
            marketplace_type = input("Tür (marketplace/b2b) [marketplace]: ").strip() or 'marketplace'
            country_code = input("Ülke kodu (US/TR/RU vb.) [US]: ").strip().upper() or 'US'
            
            manager.add_new_api_site(name, base_url, marketplace_type, country_code)
        
        elif choice == '5':
            break
        
        else:
            print("Geçersiz seçim!")
