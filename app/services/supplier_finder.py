import requests
from bs4 import BeautifulSoup
import sqlite3
from datetime import datetime
import time
import re
from flask import current_app

class SupplierFinder:
    def __init__(self, db_path=None):
        self.db_path = db_path or current_app.config.get('DATABASE_PATH', 'pazaryeri.db')
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
    
    def search_hepsiburada(self, product_name):
        """Hepsiburada'da ürün ara"""
        results = []
        try:
            search_url = f"https://www.hepsiburada.com/ara?q={product_name.replace(' ', '+')}"
            # Not: Gerçek scraping için selenium veya API kullanmak gerekir
            print(f"Hepsiburada'da arıyor: {product_name}")
            
            # Örnek veri
            results.append({
                'supplier_name': 'Hepsiburada',
                'product_url': search_url,
                'price': 0,
                'availability': 'unknown'
            })
        except Exception as e:
            print(f"Hepsiburada arama hatası: {e}")
        return results
    
    def search_trendyol(self, product_name):
        """Trendyol'da ürün ara"""
        results = []
        try:
            search_url = f"https://www.trendyol.com/sr?q={product_name.replace(' ', '%20')}"
            print(f"Trendyol'da arıyor: {product_name}")
            
            # Örnek veri
            results.append({
                'supplier_name': 'Trendyol',
                'product_url': search_url,
                'price': 0,
                'availability': 'unknown'
            })
        except Exception as e:
            print(f"Trendyol arama hatası: {e}")
        return results
    
    def search_n11(self, product_name):
        """N11'de ürün ara"""
        results = []
        try:
            search_url = f"https://www.n11.com/arama?q={product_name.replace(' ', '+')}"
            print(f"N11'de arıyor: {product_name}")
            
            # Örnek veri
            results.append({
                'supplier_name': 'N11',
                'product_url': search_url,
                'price': 0,
                'availability': 'unknown'
            })
        except Exception as e:
            print(f"N11 arama hatası: {e}")
        return results
    
    def find_suppliers_for_product(self, product_id):
        """Bir ürün için tüm sitelerde tedarikçi ara"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Ürün bilgisini al
        cursor.execute('SELECT product_name, sku FROM products WHERE id = ?', (product_id,))
        product = cursor.fetchone()
        
        if not product:
            print(f"Ürün bulunamadı: {product_id}")
            return
        
        product_name = product[0]
        print(f"\n'{product_name}' için tedarikçiler aranıyor...")
        
        # Tüm sitelerde ara
        all_results = []
        all_results.extend(self.search_hepsiburada(product_name))
        all_results.extend(self.search_trendyol(product_name))
        all_results.extend(self.search_n11(product_name))
        
        # Sonuçları veritabanına kaydet
        for result in all_results:
            # First get or create supplier
            supplier_name = result['supplier_name']
            cursor.execute('SELECT id FROM suppliers WHERE name = ?', (supplier_name,))
            supplier = cursor.fetchone()
            
            if not supplier:
                # Create new supplier
                website = result['product_url'].split('/')[0] + '//' + result['product_url'].split('/')[2]
                cursor.execute('''
                INSERT INTO suppliers (name, website, description)
                VALUES (?, ?, ?)
                ''', (supplier_name, website, f"{supplier_name} - Online mağaza"))
                supplier_id = cursor.lastrowid
            else:
                supplier_id = supplier[0]
            
            # Add product-supplier relationship
            cursor.execute('''
            INSERT OR REPLACE INTO product_suppliers (product_id, supplier_id, supplier_product_url, supplier_price)
            VALUES (?, ?, ?, ?)
            ''', (product_id, supplier_id, result['product_url'], result.get('price', 0)))
        
        conn.commit()
        conn.close()
        print(f"'{product_name}' için {len(all_results)} tedarikçi bulundu ve kaydedildi.")
    
    def find_all_product_suppliers(self):
        """Tüm ürünler için tedarikçi bul"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT id FROM products')
        products = cursor.fetchall()
        conn.close()
        
        for product in products:
            self.find_suppliers_for_product(product[0])
            time.sleep(1)  # Site yükünü azaltmak için bekleme

if __name__ == '__main__':
    finder = SupplierFinder()
    finder.find_all_product_suppliers()