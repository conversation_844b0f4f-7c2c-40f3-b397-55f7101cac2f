from flask import Flask
import os
import sys
import logging
import json
from logging.handlers import RotatingFileHandler

def create_app(config_name='default'):
    """Create Flask application using the app factory pattern"""
    from app.config import config
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Ensure directories exist
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    # Configure logging
    if not app.debug and not app.testing:
        file_handler = RotatingFileHandler(
            app.config['LOG_FILE'],
            maxBytes=10240000,  # 10MB
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('PazarYeri startup')
    
    # Register blueprints
    from app.routes import main, products, api, marketplace, categories, ai_providers, marketplace_upload, marketplace_history, ai_extraction, orders, settings
    app.register_blueprint(main.bp)
    app.register_blueprint(products.bp)
    app.register_blueprint(api.bp)
    app.register_blueprint(marketplace.bp)
    app.register_blueprint(categories.bp)
    app.register_blueprint(ai_providers.bp)
    app.register_blueprint(marketplace_upload.bp)
    app.register_blueprint(marketplace_history.bp)
    app.register_blueprint(ai_extraction.bp)
    app.register_blueprint(orders.bp)
    app.register_blueprint(settings.bp)
    
    # Register custom Jinja2 filters
    @app.template_filter('fromjson')
    def fromjson_filter(value):
        """Convert a JSON string to a Python object"""
        if not value:
            return {}
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            # Return empty dict/list if parsing fails
            return {} if value and value.strip().startswith('{') else []
    
    @app.template_filter('tojson_pretty')
    def tojson_pretty_filter(value):
        """Convert a Python object to pretty-printed JSON"""
        try:
            return json.dumps(value, indent=2, ensure_ascii=False)
        except (TypeError, ValueError):
            return str(value)
    
    # Initialize database
    with app.app_context():
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from database import create_database, add_sample_data, add_api_sites, add_default_ai_providers, add_default_settings
        create_database()
        # Only add sample data if database is empty
        import sqlite3
        conn = sqlite3.connect(app.config['DATABASE_PATH'])
        try:
            count = conn.execute('SELECT COUNT(*) FROM products').fetchone()[0]
            if count == 0:
                # add_sample_data()  # Commented out - no mock data
                add_api_sites()
                add_default_ai_providers()
                add_default_settings()
        except:
            # Table might not exist yet
            # add_sample_data()  # Commented out - no mock data
            add_api_sites()
            add_default_ai_providers()
            add_default_settings()
        finally:
            conn.close()
    
    return app