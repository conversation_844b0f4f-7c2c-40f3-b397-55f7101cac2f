{% extends "base.html" %}

{% block title %}Marketplace Ürünler - PazarYeri{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-shop-window"></i> Marketplace Ürünler</h1>
    <div class="d-flex gap-2">
        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#syncModal">
            <i class="bi bi-arrow-repeat"></i> Sync Başlat
        </button>
        <span class="badge bg-info fs-6">{{ total }} ürün</span>
    </div>
</div>

<!-- <PERSON><PERSON> ve Filtreler -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-6">
                <label for="search" class="form-label"><PERSON><PERSON></label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search_term }}" placeholder="<PERSON><PERSON><PERSON><PERSON> adı, marka veya açıklama...">
            </div>
            <div class="col-md-4">
                <label for="marketplace" class="form-label">Marketplace</label>
                <select class="form-select" id="marketplace" name="marketplace">
                    <option value="">Tüm Marketplace'ler</option>
                    {% for marketplace in marketplaces %}
                    <option value="{{ marketplace[1] }}" 
                            {% if marketplace_filter == marketplace[1] %}selected{% endif %}>
                        {{ marketplace[1] }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="bi bi-search"></i> Filtrele
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Ürün Listesi -->
<div class="card">
    <div class="card-body">
        {% if products %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Marketplace</th>
                            <th>Ürün</th>
                            <th>Marka</th>
                            <th>Kategori</th>
                            <th>Fiyat</th>
                            <th>Durum</th>
                            <th>Son Güncelleme</th>
                            <th>İşlemler</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in products %}
                        <tr>
                            <td>
                                <span class="badge bg-primary">{{ product.marketplace_name }}</span>
                            </td>
                            <td>
                                <strong>{{ product.title[:50] }}{% if product.title|length > 50 %}...{% endif %}</strong><br>
                                <small class="text-muted">ID: {{ product.external_product_id }}</small>
                            </td>
                            <td>{{ product.brand or '-' }}</td>
                            <td>
                                <small>{{ product.category or '-' }}</small>
                            </td>
                            <td class="text-end">
                                <strong>{{ product.price }} {{ product.currency }}</strong>
                            </td>
                            <td>
                                {% if product.availability == 'in_stock' %}
                                    <span class="badge bg-success">Stokta</span>
                                {% elif product.availability == 'out_of_stock' %}
                                    <span class="badge bg-danger">Stok Yok</span>
                                {% else %}
                                    <span class="badge bg-warning">{{ product.availability }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ product.last_updated.split(' ')[0] if product.last_updated else '-' }}</small>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" 
                                            class="btn btn-outline-info view-details-btn"
                                            data-product='{{ product | tojson }}'
                                            data-bs-toggle="modal" 
                                            data-bs-target="#productModal">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                    <a href="{{ url_for('marketplace.product_mappings') }}?product_id={{ product.id }}" 
                                       class="btn btn-outline-success" 
                                       title="Eşleştir">
                                        <i class="bi bi-arrow-left-right"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if total_count > per_page %}
            <nav aria-label="Ürün sayfaları">
                <ul class="pagination justify-content-center">
                    {% set total_pages = (total_count + per_page - 1) // per_page %}
                    {% for page_num in range(1, total_pages + 1) %}
                        <li class="page-item {% if page_num == current_page %}active{% endif %}">
                            <a class="page-link" href="?page={{ page_num }}&search={{ search_term }}&marketplace={{ marketplace_filter }}">
                                {{ page_num }}
                            </a>
                        </li>
                    {% endfor %}
                </ul>
            </nav>
            {% endif %}

        {% else %}
            <div class="text-center text-muted py-5">
                <i class="bi bi-inbox display-1"></i><br>
                <h5>Ürün bulunamadı</h5>
                <p>Arama kriterlerinizi değiştirin veya yeni ürünleri senkronize edin.</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Sync Modal -->
<div class="modal fade" id="syncModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Marketplace Sync</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="syncForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="syncMarketplace" class="form-label">Marketplace</label>
                        <select class="form-select" id="syncMarketplace" name="marketplace" required>
                            <option value="all">Tüm Marketplace'ler</option>
                            {% for marketplace in marketplaces %}
                            <option value="{{ marketplace[0] }}">{{ marketplace[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="syncLimit" class="form-label">Ürün Limiti</label>
                        <input type="number" class="form-control" id="syncLimit" name="limit" 
                               value="20" min="1" max="100">
                        <div class="form-text">Bir seferde kaç ürün senkronize edilsin (max 100)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-success">Sync Başlat</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Product Details Modal -->
<div class="modal fade" id="productModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ürün Detayları</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="productDetails">
                <!-- Product details will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Sync form
    $('#syncForm').submit(function(e) {
        e.preventDefault();
        
        const button = $(this).find('button[type="submit"]');
        const originalText = button.html();
        
        button.prop('disabled', true);
        button.html('<i class="bi bi-hourglass-split"></i> Senkronize ediliyor...');
        
        $.ajax({
            url: '/marketplace-sync',
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                if (response.status === 'success') {
                    $('#syncModal').modal('hide');
                    alert('Senkronizasyon tamamlandı!');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    alert('Hata: ' + response.message);
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                alert('Hata: ' + (response ? response.message : 'Bilinmeyen hata'));
            },
            complete: function() {
                button.prop('disabled', false);
                button.html(originalText);
            }
        });
    });
    
    // Product details modal
    $('.view-details-btn').click(function() {
        const product = $(this).data('product');
        
        let html = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Genel Bilgiler</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Marketplace:</strong></td><td>${product.marketplace_name}</td></tr>
                        <tr><td><strong>External ID:</strong></td><td>${product.external_product_id}</td></tr>
                        <tr><td><strong>Başlık:</strong></td><td>${product.title}</td></tr>
                        <tr><td><strong>Marka:</strong></td><td>${product.brand || '-'}</td></tr>
                        <tr><td><strong>Kategori:</strong></td><td>${product.category || '-'}</td></tr>
                        <tr><td><strong>Fiyat:</strong></td><td>${product.price} ${product.currency}</td></tr>
                        <tr><td><strong>Durum:</strong></td><td>${product.availability}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>Teknik Bilgiler</h6>
                    <table class="table table-sm">
                        <tr><td><strong>ASIN:</strong></td><td>${product.asin || '-'}</td></tr>
                        <tr><td><strong>UPC:</strong></td><td>${product.upc || '-'}</td></tr>
                        <tr><td><strong>Model:</strong></td><td>${product.model_number || '-'}</td></tr>
                        <tr><td><strong>Üretici:</strong></td><td>${product.manufacturer || '-'}</td></tr>
                        <tr><td><strong>Ağırlık:</strong></td><td>${product.weight || '-'} kg</td></tr>
                        <tr><td><strong>Son Güncelleme:</strong></td><td>${product.last_updated || '-'}</td></tr>
                    </table>
                </div>
            </div>
            
            ${product.description ? `
            <div class="mt-3">
                <h6>Açıklama</h6>
                <p class="text-muted">${product.description}</p>
            </div>
            ` : ''}
        `;
        
        $('#productDetails').html(html);
    });
});
</script>
{% endblock %}