{% extends "base.html" %}

{% block title %}<PERSON><PERSON><PERSON><PERSON><PERSON> - PazarYeri{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-box"></i> <PERSON><PERSON><PERSON><PERSON><PERSON></h1>
    <a href="{{ url_for('products.add_product') }}" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> <PERSON><PERSON>
    </a>
</div>

<!-- Filters Section -->
<div class="card mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0"><i class="bi bi-funnel"></i> Filtreler</h5>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ url_for('products.products') }}" id="filterForm">
            <div class="row g-3">
                <!-- Search Filter -->
                <div class="col-md-4">
                    <label for="search" class="form-label">Ara<PERSON></label>
                    <input type="text" class="form-control" id="search" name="search" 
                           placeholder="Ürün adı, SKU veya barkod..." 
                           value="{{ filters.search or '' }}">
                </div>
                
                <!-- Brand Filter -->
                <div class="col-md-3">
                    <label for="brand" class="form-label">Marka</label>
                    <select class="form-select" id="brand" name="brand">
                        <option value="">Tüm Markalar</option>
                        {% for brand in brands %}
                        <option value="{{ brand }}" {% if filters.brand == brand %}selected{% endif %}>
                            {{ brand }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Price Range -->
                <div class="col-md-2">
                    <label for="min_price" class="form-label">Min. Fiyat</label>
                    <input type="number" class="form-control" id="min_price" name="min_price" 
                           placeholder="₺0" step="0.01" min="0"
                           value="{{ filters.min_price or '' }}">
                </div>
                
                <div class="col-md-2">
                    <label for="max_price" class="form-label">Max. Fiyat</label>
                    <input type="number" class="form-control" id="max_price" name="max_price" 
                           placeholder="₺999999" step="0.01" min="0"
                           value="{{ filters.max_price or '' }}">
                </div>
                
                <!-- Supplier Status -->
                <div class="col-md-3">
                    <label class="form-label">Tedarikçi Durumu</label>
                    <div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="supplier_status" 
                                   id="supplier_all" value="all" 
                                   {% if not filters.supplier_status or filters.supplier_status == 'all' %}checked{% endif %}>
                            <label class="form-check-label" for="supplier_all">Tümü</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="supplier_status" 
                                   id="supplier_with" value="with" 
                                   {% if filters.supplier_status == 'with' %}checked{% endif %}>
                            <label class="form-check-label" for="supplier_with">Tedarikçili</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="supplier_status" 
                                   id="supplier_without" value="without" 
                                   {% if filters.supplier_status == 'without' %}checked{% endif %}>
                            <label class="form-check-label" for="supplier_without">Tedarikçisiz</label>
                        </div>
                    </div>
                </div>
                
                <!-- Filter Buttons -->
                <div class="col-md-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i> Filtrele
                    </button>
                    <a href="{{ url_for('products.products') }}" class="btn btn-secondary">
                        <i class="bi bi-x-circle"></i> Temizle
                    </a>
                    {% if active_filters %}
                    <span class="text-muted ms-3">
                        <i class="bi bi-info-circle"></i> {{ filtered_count }} / {{ total_count }} ürün gösteriliyor
                    </span>
                    {% endif %}
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>ID</th>
                        <th>Ürün Adı</th>
                        <th>SKU</th>
                        <th>Model</th>
                        <th>Marka</th>
                        <th>Barkod</th>
                        <th>Min. Fiyat</th>
                        <th>Max. Fiyat</th>
                        <th>Ort. Fiyat</th>
                        <th>Fiyat Güncelleme</th>
                        <th>Tedarikçi</th>
                        <th>Desi</th>
                        <th>Kar Marjı %</th>
                        <th>İşlemler</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products %}
                    <tr>
                        <td>{{ product.id }}</td>
                        <td>
                            <a href="{{ url_for('products.product_detail', id=product.id) }}" class="text-decoration-none">
                                {{ product.product_name }}
                            </a>
                        </td>
                        <td><code>{{ product.sku }}</code></td>
                        <td>{{ product.model or '-' }}</td>
                        <td>{{ product.brand or '-' }}</td>
                        <td>{{ product.barcode or '-' }}</td>
                        <td>
                            {% if product.stored_min_price %}
                                <span class="text-success fw-bold" 
                                      {% if product.price_updated_at %}
                                      title="Güncelleme: {{ product.price_updated_at }}"
                                      {% endif %}>
                                    ₺{{ "%.2f"|format(product.stored_min_price) }}
                                </span>
                            {% elif product.supplier_min_price %}
                                <span class="text-info">₺{{ "%.2f"|format(product.supplier_min_price) }}</span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.stored_max_price %}
                                <span class="fw-bold">₺{{ "%.2f"|format(product.stored_max_price) }}</span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.stored_avg_price %}
                                <span>₺{{ "%.2f"|format(product.stored_avg_price) }}</span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.price_updated_at %}
                                <small>
                                    {% if product.price_updated_at is string %}
                                        {{ product.price_updated_at }}
                                    {% else %}
                                        {{ product.price_updated_at.strftime('%d.%m.%Y %H:%M') }}
                                    {% endif %}
                                </small>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.supplier_count > 0 %}
                                <span class="badge bg-primary">{{ product.supplier_count }}</span>
                            {% else %}
                                <span class="badge bg-secondary">0</span>
                            {% endif %}
                        </td>
                        <td>{{ product.desi or 0 }}</td>
                        <td>{{ product.profit_margin or 0 }}%</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('products.product_detail', id=product.id) }}" 
                                   class="btn btn-outline-primary" title="Detay">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <button type="button" 
                                        class="btn btn-outline-success find-suppliers-btn" 
                                        data-product-id="{{ product.id }}" 
                                        title="Tedarikçi Bul">
                                    <i class="bi bi-search"></i>
                                </button>
                                <button type="button" 
                                        class="btn btn-outline-warning upload-to-marketplace-btn" 
                                        data-product-id="{{ product.id }}"
                                        data-product-name="{{ product.product_name }}"
                                        title="Marketplace'e Yükle">
                                    <i class="bi bi-cloud-upload"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="14" class="text-center text-muted py-4">
                            <i class="bi bi-inbox display-1"></i><br>
                            Henüz ürün bulunmuyor.
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Marketplace Upload Modal -->
<div class="modal fade" id="uploadToMarketplaceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Marketplace'e Ürün Yükle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="uploadToMarketplaceForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="selectedProduct" class="form-label">Seçilen Ürün</label>
                        <input type="text" class="form-control" id="selectedProduct" readonly>
                        <input type="hidden" id="selectedProductId" name="product_id">
                    </div>
                    
                    <div class="mb-3">
                        <label for="marketplace" class="form-label">Marketplace</label>
                        <select class="form-select" id="marketplace" name="marketplace_id" required>
                            <option value="">Marketplace seçin...</option>
                            <option value="3">Ozon</option>
                            <!-- More marketplaces can be added here -->
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="uploadPrice" class="form-label">Fiyat</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="uploadPrice" name="price" 
                                   step="0.01" min="0" required placeholder="0.00">
                            <span class="input-group-text">USD</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="uploadVat" class="form-label">KDV Oranı</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="uploadVat" name="vat" 
                                   value="0.1" step="0.01" min="0" max="1" required>
                            <span class="input-group-text">%</span>
                        </div>
                        <div class="form-text">Ozon için gerekli (örn: 0.1 = %10 KDV)</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="uploadOfferId" class="form-label">Offer ID (SKU)</label>
                        <input type="text" class="form-control" id="uploadOfferId" name="offer_id" 
                               required placeholder="Benzersiz ürün kodu">
                        <div class="form-text">Marketplace'te ürünü tanımlamak için kullanılır</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="uploadCategoryId" class="form-label">Kategori ID</label>
                        <input type="number" class="form-control" id="uploadCategoryId" name="category_id" 
                               required placeholder="Ozon kategori ID'si">
                        <div class="form-text">Ozon'da ürünün ait olacağı kategori</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="uploadDescription" class="form-label">Açıklama</label>
                        <textarea class="form-control" id="uploadDescription" name="description" 
                                  rows="4" required placeholder="Ürün açıklaması..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="uploadWeight" class="form-label">Ağırlık (gram)</label>
                        <input type="number" class="form-control" id="uploadWeight" name="weight" 
                               min="0" placeholder="0">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="bi bi-cloud-upload"></i> Yükle
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $('.find-suppliers-btn').click(function() {
        const productId = $(this).data('product-id');
        const button = $(this);
        
        button.prop('disabled', true);
        button.html('<i class="bi bi-hourglass-split"></i>');
        
        $.post(`/product/${productId}/find-suppliers`)
            .done(function(response) {
                button.html('<i class="bi bi-check-circle text-success"></i>');
                setTimeout(() => {
                    button.prop('disabled', false);
                    button.html('<i class="bi bi-search"></i>');
                }, 3000);
            })
            .fail(function() {
                button.html('<i class="bi bi-x-circle text-danger"></i>');
                setTimeout(() => {
                    button.prop('disabled', false);
                    button.html('<i class="bi bi-search"></i>');
                }, 3000);
            });
    });
    
    // Marketplace upload button click handler
    $('.upload-to-marketplace-btn').click(function() {
        const productId = $(this).data('product-id');
        const productName = $(this).data('product-name');
        
        // Fill modal with product data
        $('#selectedProduct').val(productName);
        $('#selectedProductId').val(productId);
        $('#uploadOfferId').val('SKU_' + productId); // Default offer ID
        
        // Show modal
        $('#uploadToMarketplaceModal').modal('show');
    });
    
    // Upload form submission
    $('#uploadToMarketplaceForm').submit(function(e) {
        e.preventDefault();
        
        const button = $(this).find('button[type="submit"]');
        const originalText = button.html();
        
        button.prop('disabled', true);
        button.html('<i class="bi bi-hourglass-split"></i> Yükleniyor...');
        
        $.ajax({
            url: '/api/marketplace/upload',
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                if (response.status === 'success') {
                    $('#uploadToMarketplaceModal').modal('hide');
                    alert('Ürün yükleme başarıyla başlatıldı!');
                    // Reset form
                    $('#uploadToMarketplaceForm')[0].reset();
                } else {
                    alert('Hata: ' + response.message);
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                alert('Hata: ' + (response ? response.message : 'Bilinmeyen hata'));
            },
            complete: function() {
                button.prop('disabled', false);
                button.html(originalText);
            }
        });
    });
    
    // Reset modal when closed
    $('#uploadToMarketplaceModal').on('hidden.bs.modal', function () {
        $('#uploadToMarketplaceForm')[0].reset();
    });
});
</script>
{% endblock %}
