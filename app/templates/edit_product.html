{% extends "base.html" %}

{% block title %}{{ product.product_name }} - Düzenle - PazarYeri{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-3">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ url_for('products.products') }}"><PERSON>rünler</a></li>
                <li class="breadcrumb-item"><a href="{{ url_for('products.product_detail', id=product.id) }}">{{ product.product_name }}</a></li>
                <li class="breadcrumb-item active">Düzenle</li>
            </ol>
        </nav>

        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h4 class="mb-0"><i class="bi bi-pencil-square"></i> <PERSON><PERSON><PERSON><PERSON><PERSON></h4>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Temel Bilgiler -->
                            <h5 class="mb-3"><i class="bi bi-info-circle"></i> Temel Bilgiler</h5>
                            
                            <div class="mb-3">
                                <label for="product_name" class="form-label">Ürün Adı *</label>
                                <input type="text" class="form-control" id="product_name" name="product_name" 
                                       value="{{ product.product_name }}" required>
                            </div>

                            <div class="mb-3">
                                <label for="sku" class="form-label">SKU *</label>
                                <input type="text" class="form-control" id="sku" name="sku" 
                                       value="{{ product.sku }}" required>
                                <div class="form-text">Stok Kodu (Benzersiz olmalı)</div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="model" class="form-label">Model</label>
                                        <input type="text" class="form-control" id="model" name="model" 
                                               value="{{ product.model or '' }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="brand" class="form-label">Marka</label>
                                        <input type="text" class="form-control" id="brand" name="brand" 
                                               value="{{ product.brand or '' }}">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="barcode" class="form-label">Barkod</label>
                                <input type="text" class="form-control" id="barcode" name="barcode" 
                                       value="{{ product.barcode or '' }}">
                            </div>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="desi" class="form-label">Desi</label>
                                        <input type="number" class="form-control" id="desi" name="desi" 
                                               step="0.01" min="0" value="{{ product.desi or 0 }}">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="profit_margin" class="form-label">Kar Marjı (%)</label>
                                        <input type="number" class="form-control" id="profit_margin" name="profit_margin" 
                                               step="0.01" min="0" value="{{ product.profit_margin or 15 }}">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="commission_rate" class="form-label">Komisyon Oranı (%)</label>
                                        <input type="number" class="form-control" id="commission_rate" name="commission_rate" 
                                               step="0.01" min="0" value="{{ product.commission_rate or 8 }}">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="stock_multiplier" class="form-label">Stok Çarpanı</label>
                                        <input type="number" class="form-control" id="stock_multiplier" name="stock_multiplier" 
                                               min="1" value="{{ product.stock_multiplier or 1 }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="gtip" class="form-label">GTIP</label>
                                        <input type="text" class="form-control" id="gtip" name="gtip" 
                                               value="{{ product.gtip or '' }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <!-- Mevcut Resimler -->
                            <h5 class="mb-3"><i class="bi bi-images"></i> Mevcut Resimler</h5>
                            
                            {% if images %}
                            <div id="existingImages" class="row g-2 mb-3">
                                {% for image in images %}
                                <div class="col-lg-4 col-md-6" id="existing-image-{{ image.id }}">
                                    <div class="card">
                                        <div class="position-relative">
                                            <img src="{{ url_for('static', filename=image.image_path) }}" 
                                                 class="card-img-top" style="height: 120px; object-fit: cover;" 
                                                 alt="Product Image">
                                            <div class="position-absolute top-0 end-0 m-1">
                                                <input type="checkbox" class="btn-check" id="delete-{{ image.id }}" 
                                                       name="delete_images" value="{{ image.id }}">
                                                <label class="btn btn-danger btn-sm rounded-circle" for="delete-{{ image.id }}" 
                                                       title="Sil">
                                                    <i class="bi bi-trash"></i>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="card-body p-2">
                                            <small class="text-muted">Resim {{ loop.index }}</small>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            
                            <div class="alert alert-info">
                                <small><i class="bi bi-info-circle"></i> Silmek istediğiniz resimleri işaretleyin.</small>
                            </div>
                            {% else %}
                            <div class="alert alert-secondary">
                                <i class="bi bi-image"></i> Bu ürünün henüz resmi bulunmuyor.
                            </div>
                            {% endif %}

                            <!-- Yeni Resim Ekleme -->
                            <h5 class="mb-3 mt-4"><i class="bi bi-camera-fill"></i> Yeni Resim Ekle</h5>
                            
                            <!-- Drag & Drop Area -->
                            <div class="mb-3">
                                <div id="dropArea" class="border border-2 border-dashed border-warning rounded p-4 text-center position-relative" 
                                     style="min-height: 120px; cursor: pointer;">
                                    <div id="dropText">
                                        <i class="bi bi-cloud-upload display-4 text-warning mb-2"></i>
                                        <p class="mb-2"><strong>Yeni resimleri buraya sürükleyin</strong> veya <span class="text-warning">dosya seçmek için tıklayın</span></p>
                                        <small class="text-muted">PNG, JPG, JPEG, GIF, WEBP - Her biri max 16MB</small>
                                    </div>
                                    <input type="file" class="form-control position-absolute w-100 h-100 opacity-0" 
                                           id="images" name="images" accept="image/*" multiple style="top: 0; left: 0; cursor: pointer;">
                                </div>
                            </div>

                            <!-- Additional Image Upload Button -->
                            <div class="mb-3">
                                <button type="button" class="btn btn-outline-warning btn-sm" onclick="$('#additionalImages').click()">
                                    <i class="bi bi-plus-circle"></i> Daha Fazla Resim Ekle
                                </button>
                                <input type="file" id="additionalImages" accept="image/*" multiple style="display: none;">
                            </div>

                            <!-- New Image Preview -->
                            <div id="imagePreview" class="row g-2 mb-3">
                                <!-- New image previews will be shown here -->
                            </div>

                            <!-- Image Count and Status -->
                            <div id="imageStats" class="mb-3" style="display: none;">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="bi bi-images"></i> <span id="imageCount">0</span> yeni resim seçildi
                                    </small>
                                    <small class="text-muted">
                                        Toplam boyut: <span id="totalSize">0 MB</span>
                                    </small>
                                </div>
                                <div class="progress mt-1" style="height: 4px;">
                                    <div id="imageProgress" class="progress-bar bg-warning" role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>

                            <!-- Tedarikçi Linkleri -->
                            <h5 class="mb-3 mt-4"><i class="bi bi-truck"></i> Mevcut Tedarikçiler</h5>
                            
                            {% if suppliers %}
                            <div class="alert alert-info">
                                <small><i class="bi bi-info-circle"></i> Mevcut tedarikçiler. Yeni tedarikçi eklemek için aşağıdaki formu kullanın.</small>
                            </div>
                            <div class="mb-3">
                                {% for supplier in suppliers %}
                                <div class="d-flex justify-content-between align-items-center border rounded p-2 mb-1">
                                    <div>
                                        <strong>{{ supplier.name }}</strong>
                                        {% if supplier.supplier_product_url %}
                                            <br><small><a href="{{ supplier.supplier_product_url }}" target="_blank">{{ supplier.supplier_product_url[:50] }}...</a></small>
                                        {% endif %}
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            onclick="removeSupplier({{ product.id }}, {{ supplier.id }})">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                                {% endfor %}
                            </div>
                            {% endif %}

                            <h6 class="mb-3">Yeni Tedarikçi Linkleri Ekle</h6>
                            <div id="supplierLinks">
                                <div class="mb-3">
                                    <label class="form-label">Tedarikçi Linki</label>
                                    <input type="url" class="form-control" name="supplier_links[]" placeholder="https://...">
                                    <div class="form-text">Hepsiburada, Trendyol, N11 vb. linkler otomatik olarak tanınacaktır</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <button type="button" class="btn btn-sm btn-outline-warning" onclick="addSupplierLinkField()">
                                    <i class="bi bi-plus"></i> Başka Link Ekle
                                </button>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-warning">
                            <i class="bi bi-check-circle"></i> Değişiklikleri Kaydet
                        </button>
                        <a href="{{ url_for('products.product_detail', id=product.id) }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> İptal
                        </a>
                        <a href="{{ url_for('products.products') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-list"></i> Ürün Listesi
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
let selectedFiles = new DataTransfer();
const currentImageCount = {{ images|length }};

$(document).ready(function() {
    // Initialize drag and drop for new images
    initializeDragAndDrop();
    
    // Handle main file input change
    $('#images').change(function() {
        handleFileSelection(this.files);
    });
    
    // Handle additional file input change
    $('#additionalImages').change(function() {
        handleFileSelection(this.files);
        this.value = '';
    });
    
    // Update checkbox labels when checked
    $('input[name="delete_images"]').change(function() {
        const label = $('label[for="' + $(this).attr('id') + '"]');
        if ($(this).is(':checked')) {
            label.removeClass('btn-danger').addClass('btn-success');
            label.find('i').removeClass('bi-trash').addClass('bi-check');
            label.attr('title', 'Silinecek');
        } else {
            label.removeClass('btn-success').addClass('btn-danger');
            label.find('i').removeClass('bi-check').addClass('bi-trash');
            label.attr('title', 'Sil');
        }
    });
});

function initializeDragAndDrop() {
    const dropArea = document.getElementById('dropArea');
    
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });
    
    ['dragenter', 'dragover'].forEach(eventName => {
        dropArea.addEventListener(eventName, highlight, false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        dropArea.addEventListener(eventName, unhighlight, false);
    });
    
    dropArea.addEventListener('drop', handleDrop, false);
}

function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

function highlight(e) {
    $('#dropArea').addClass('border-success bg-light');
}

function unhighlight(e) {
    $('#dropArea').removeClass('border-success bg-light');
}

function handleDrop(e) {
    const dt = e.dataTransfer;
    const files = dt.files;
    handleFileSelection(files);
}

function handleFileSelection(files) {
    if (files.length === 0) return;
    
    let addedFiles = 0;
    let skippedFiles = 0;
    
    for (let file of files) {
        if (file.type.match('image.*')) {
            // Check total limit (existing + new + current selection)
            const totalImages = currentImageCount + selectedFiles.files.length;
            if (totalImages >= 10) {
                showNotification('warning', 'En fazla 10 resim olabilir!');
                break;
            }
            
            // Check file size (16MB)
            if (file.size > 16 * 1024 * 1024) {
                showNotification('warning', `"${file.name}" dosyası çok büyük (16MB limit)`);
                skippedFiles++;
                continue;
            }
            
            // Check if file already exists
            let exists = false;
            for (let existingFile of selectedFiles.files) {
                if (existingFile.name === file.name && existingFile.size === file.size) {
                    exists = true;
                    break;
                }
            }
            
            if (exists) {
                showNotification('info', `"${file.name}" zaten eklenmiş`);
                skippedFiles++;
                continue;
            }
            
            selectedFiles.items.add(file);
            addedFiles++;
        } else {
            showNotification('warning', `"${file.name}" desteklenmeyen dosya türü`);
            skippedFiles++;
        }
    }
    
    if (addedFiles > 0) {
        showNotification('success', `${addedFiles} yeni resim eklendi`);
    }
    
    document.getElementById('images').files = selectedFiles.files;
    updateImagePreview();
    updateDropAreaText();
}

function updateImagePreview() {
    $('#imagePreview').empty();
    
    for (let i = 0; i < selectedFiles.files.length; i++) {
        const file = selectedFiles.files[i];
        const fileId = `new-image-${i}`;
        
        if (file.type.match('image.*')) {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const preview = `
                    <div class="col-lg-4 col-md-6" id="${fileId}">
                        <div class="card border-success">
                            <div class="position-relative">
                                <img src="${e.target.result}" class="card-img-top" style="height: 120px; object-fit: cover;" alt="Preview">
                                <div class="position-absolute top-0 end-0 m-1">
                                    <button type="button" class="btn btn-danger btn-sm rounded-circle" 
                                            onclick="removeNewImage(${i})" title="Resmi Sil">
                                        <i class="bi bi-x"></i>
                                    </button>
                                </div>
                                <div class="position-absolute bottom-0 start-0 m-1">
                                    <span class="badge bg-success">YENİ</span>
                                </div>
                            </div>
                            <div class="card-body p-2">
                                <small class="text-muted d-block text-truncate" title="${file.name}">${file.name}</small>
                                <small class="text-muted">${(file.size / 1024 / 1024).toFixed(2)} MB</small>
                            </div>
                        </div>
                    </div>
                `;
                $('#imagePreview').append(preview);
            };
            
            reader.readAsDataURL(file);
        }
    }
}

function removeNewImage(index) {
    const newFiles = new DataTransfer();
    for (let i = 0; i < selectedFiles.files.length; i++) {
        if (i !== index) {
            newFiles.items.add(selectedFiles.files[i]);
        }
    }
    selectedFiles = newFiles;
    
    document.getElementById('images').files = selectedFiles.files;
    updateImagePreview();
    updateDropAreaText();
}

function updateDropAreaText() {
    const fileCount = selectedFiles.files.length;
    if (fileCount > 0) {
        $('#dropText').html(`
            <i class="bi bi-images display-4 text-success mb-2"></i>
            <p class="mb-2"><strong>${fileCount} yeni resim seçildi</strong></p>
            <small class="text-muted">Daha fazla resim eklemek için buraya sürükleyin veya tıklayın</small>
        `);
        $('#dropArea').removeClass('border-warning').addClass('border-success');
    } else {
        $('#dropText').html(`
            <i class="bi bi-cloud-upload display-4 text-warning mb-2"></i>
            <p class="mb-2"><strong>Yeni resimleri buraya sürükleyin</strong> veya <span class="text-warning">dosya seçmek için tıklayın</span></p>
            <small class="text-muted">PNG, JPG, JPEG, GIF, WEBP - Her biri max 16MB</small>
        `);
        $('#dropArea').removeClass('border-success').addClass('border-warning');
    }
    
    updateImageStats();
}

function updateImageStats() {
    const fileCount = selectedFiles.files.length;
    
    if (fileCount > 0) {
        let totalSize = 0;
        for (let file of selectedFiles.files) {
            totalSize += file.size;
        }
        
        $('#imageCount').text(fileCount);
        $('#totalSize').text((totalSize / 1024 / 1024).toFixed(2));
        $('#imageStats').show();
    } else {
        $('#imageStats').hide();
    }
}

function addSupplierLinkField() {
    const newField = `
        <div class="mb-3">
            <div class="input-group">
                <input type="url" class="form-control" name="supplier_links[]" placeholder="https://...">
                <button class="btn btn-outline-danger" type="button" onclick="$(this).closest('.mb-3').remove()">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        </div>
    `;
    $('#supplierLinks').append(newField);
}

function removeSupplier(productId, supplierId) {
    if (confirm('Bu tedarikçi ilişkisini silmek istediğinizden emin misiniz?')) {
        $.ajax({
            url: `/api/product-supplier/delete/${productId}/${supplierId}`,
            method: 'DELETE'
        })
        .done(function(response) {
            if (response.status === 'success') {
                showNotification('success', 'Tedarikçi ilişkisi silindi');
                location.reload();
            } else {
                showNotification('error', response.message);
            }
        })
        .fail(function() {
            showNotification('error', 'Tedarikçi silinirken hata oluştu!');
        });
    }
}

function showNotification(type, message) {
    $('.notification-toast').remove();
    
    const iconMap = {
        'success': 'check-circle',
        'warning': 'exclamation-triangle', 
        'error': 'x-circle',
        'info': 'info-circle'
    };
    
    const alertClass = `alert-${type === 'error' ? 'danger' : type}`;
    const icon = iconMap[type] || 'info-circle';
    
    const notification = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed notification-toast" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 400px;">
            <i class="bi bi-${icon}"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('body').append(notification);
    
    setTimeout(() => {
        notification.alert('close');
    }, 4000);
}

// Form validation
$('form').submit(function(e) {
    const existingImages = $('input[name="delete_images"]:not(:checked)').length;
    const newImages = selectedFiles.files.length;
    const totalImages = existingImages + newImages;
    
    if (totalImages === 0) {
        if (!confirm('Bu ürünün hiç resmi kalmayacak. Devam etmek istediğinizden emin misiniz?')) {
            e.preventDefault();
            return false;
        }
    }
    
    // Check if any files are too large
    for (let file of selectedFiles.files) {
        if (file.size > 16 * 1024 * 1024) {
            e.preventDefault();
            showNotification('error', `"${file.name}" dosyası çok büyük. Maksimum dosya boyutu 16MB'dir.`);
            return false;
        }
    }
    
    // Show loading state
    if (newImages > 0) {
        $('button[type="submit"]').prop('disabled', true).html('<i class="bi bi-hourglass-split"></i> Güncelleniyor...');
    }
});
</script>

<style>
.card-img-top {
    transition: transform 0.2s;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

#dropArea.border-success {
    background-color: rgba(25, 135, 84, 0.1);
}

.btn-check:checked + .btn {
    transform: scale(0.9);
}

.notification-toast {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}
</style>
{% endblock %}