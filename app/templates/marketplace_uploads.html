{% extends "base.html" %}

{% block title %}Marketplace Yüklemeleri - PazarYeri{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-cloud-upload"></i> Marketplace Yüklemeleri</h1>
    <span class="badge bg-info fs-6">{{ uploads | length }} yükleme</span>
</div>

<div class="card">
    <div class="card-body">
        {% if uploads %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>ID</th>
                            <th>Ürün</th>
                            <th>SKU</th>
                            <th>Marketplace</th>
                            <th>Durum</th>
                            <th>Marketplace ID</th>
                            <th>Tarih</th>
                            <th>Hata</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for upload in uploads %}
                        <tr>
                            <td>{{ upload.id }}</td>
                            <td>{{ upload.product_name }}</td>
                            <td><code>{{ upload.sku }}</code></td>
                            <td>
                                <span class="badge bg-primary">{{ upload.marketplace_name }}</span>
                            </td>
                            <td>
                                {% if upload.status == 'success' %}
                                    <span class="badge bg-success">Başarılı</span>
                                {% elif upload.status == 'failed' %}
                                    <span class="badge bg-danger">Başarısız</span>
                                {% elif upload.status == 'uploading' %}
                                    <span class="badge bg-warning">Yükleniyor</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ upload.status }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if upload.marketplace_product_id %}
                                    <code>{{ upload.marketplace_product_id }}</code>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ upload.created_at.split(' ')[0] if upload.created_at else '-' }}</small>
                            </td>
                            <td>
                                {% if upload.error_message %}
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-danger"
                                            data-bs-toggle="tooltip"
                                            title="{{ upload.error_message }}">
                                        <i class="bi bi-exclamation-triangle"></i>
                                    </button>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center text-muted py-4">
                <i class="bi bi-cloud-upload display-1"></i><br>
                <h5>Henüz yükleme yapılmamış</h5>
                <p>Ürünler sayfasından marketplace'e ürün yükleyebilirsiniz.</p>
                <a href="{{ url_for('products.products') }}" class="btn btn-primary">
                    <i class="bi bi-box"></i> Ürünlere Git
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Tooltip'leri etkinleştir
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}