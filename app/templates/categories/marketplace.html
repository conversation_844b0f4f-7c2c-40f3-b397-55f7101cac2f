{% extends "base.html" %}

{% block title %}Marketplace Kategoriler - PazarYeri{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-shop"></i> Marketplace Kategoriler</h2>
    <a href="{{ url_for('categories.categories') }}" class="btn btn-secondary">
        <i class="bi bi-arrow-left"></i> Kategori Yönetimine Dön
    </a>
</div>

<!-- Marketplace Selection -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-funnel"></i> Marketplace Seç
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-8">
                <label for="marketplace_id" class="form-label">Marketplace</label>
                <select class="form-select" id="marketplace_id" name="marketplace_id" onchange="this.form.submit()">
                    <option value="">Marketplace seçin...</option>
                    {% for marketplace in marketplaces %}
                        <option value="{{ marketplace.id }}" 
                                {% if selected_marketplace_id == marketplace.id %}selected{% endif %}>
                            {{ marketplace.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                {% if selected_marketplace_id %}
                    <form method="POST" action="{{ url_for('categories.sync_marketplace_categories', marketplace_id=selected_marketplace_id) }}" class="d-inline">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-arrow-clockwise"></i> Kategorileri Senkronize Et
                        </button>
                    </form>
                {% endif %}
            </div>
        </form>
    </div>
</div>

<!-- Categories Table -->
{% if selected_marketplace_id %}
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                {% for marketplace in marketplaces %}
                    {% if marketplace.id == selected_marketplace_id %}
                        {{ marketplace.name }} Kategorileri
                    {% endif %}
                {% endfor %}
            </h5>
            <span class="badge bg-primary">{{ marketplace_categories|length }} kategori</span>
        </div>
        <div class="card-body">
            {% if marketplace_categories %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Marketplace Kategori ID</th>
                                <th>Kategori Adı</th>
                                <th>Üst Kategori ID</th>
                                <th>Seviye</th>
                                <th>Yaprak</th>
                                <th>Oluşturma Tarihi</th>
                                <th>İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for category in marketplace_categories %}
                            <tr>
                                <td>{{ category.id }}</td>
                                <td>
                                    <code>{{ category.marketplace_category_id }}</code>
                                </td>
                                <td>
                                    {% for i in range(category.level) %}
                                        <span class="text-muted">──</span>
                                    {% endfor %}
                                    {{ category.name }}
                                    {% if category.is_leaf %}
                                        <span class="badge bg-success ms-2">Yaprak</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if category.parent_marketplace_category_id %}
                                        <code>{{ category.parent_marketplace_category_id }}</code>
                                    {% else %}
                                        <span class="text-muted">Ana Kategori</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ category.level }}</span>
                                </td>
                                <td>
                                    {% if category.is_leaf %}
                                        <i class="bi bi-check-circle text-success" title="Yaprak kategori"></i>
                                    {% else %}
                                        <i class="bi bi-dash-circle text-muted" title="Dallanıyor"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if category.created_at %}
                                        {% if category.created_at is string %}
                                            {{ category.created_at }}
                                        {% else %}
                                            {{ category.created_at.strftime('%d.%m.%Y %H:%M') }}
                                        {% endif %}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('categories.add_mapping') }}?marketplace_category_id={{ category.id }}" 
                                       class="btn btn-sm btn-outline-primary" 
                                       title="Eşleştir">
                                        <i class="bi bi-arrow-left-right"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="text-center py-4">
                    <i class="bi bi-shop text-muted" style="font-size: 3rem;"></i>
                    <p class="text-muted mt-2">
                        Bu marketplace için kategori bulunamadı.
                    </p>
                    <form method="POST" action="{{ url_for('categories.sync_marketplace_categories', marketplace_id=selected_marketplace_id) }}" class="d-inline">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-arrow-clockwise"></i> Kategorileri Senkronize Et
                        </button>
                    </form>
                </div>
            {% endif %}
        </div>
    </div>
{% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-shop text-muted" style="font-size: 3rem;"></i>
            <p class="text-muted mt-2">Kategorileri görüntülemek için bir marketplace seçin.</p>
        </div>
    </div>
{% endif %}

<!-- Info Card -->
<div class="card mt-4 border-info">
    <div class="card-header bg-info text-white">
        <h6 class="mb-0">
            <i class="bi bi-info-circle"></i> Marketplace Kategoriler Hakkında
        </h6>
    </div>
    <div class="card-body">
        <ul class="mb-0">
            <li><strong>Senkronizasyon:</strong> Marketplace kategorileri API'den otomatik olarak çekilir.</li>
            <li><strong>Seviye:</strong> Kategori hiyerarşisindeki derinliği gösterir (0 = ana kategori).</li>
            <li><strong>Yaprak Kategori:</strong> Alt kategorileri olmayan, ürün atanabilen kategorilerdir.</li>
            <li><strong>Eşleştirme:</strong> Marketplace kategorilerini local kategorilerle eşleştirebilirsiniz.</li>
            <li><strong>Otomatik Güncelleme:</strong> Senkronizasyon sırasında mevcut kategoriler güncellenir.</li>
        </ul>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit form when marketplace selection changes
document.getElementById('marketplace_id').addEventListener('change', function() {
    if (this.value) {
        this.form.submit();
    }
});

// Show loading state for sync button
document.querySelectorAll('form[action*="sync"]').forEach(form => {
    form.addEventListener('submit', function(e) {
        const button = this.querySelector('button[type="submit"]');
        button.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Senkronize ediliyor...';
        button.disabled = true;
    });
});
</script>
{% endblock %}