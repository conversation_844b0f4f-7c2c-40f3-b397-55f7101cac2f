{% extends "base.html" %}

{% block title %}<PERSON><PERSON>i Eşleştirm<PERSON> - PazarYeri{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-arrow-left-right"></i> Kategori Eşleştirmesi E<PERSON>
                </h5>
                <a href="{{ url_for('categories.category_mappings') }}" class="btn btn-sm btn-secondary">
                    <i class="bi bi-arrow-left"></i> Geri <PERSON>ön
                </a>
            </div>
            <div class="card-body">
                <form method="POST" id="mappingForm">
                    <div class="row">
                        <!-- Local Category Selection -->
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-tags"></i> Local Kategori
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="category_id" class="form-label">Kategori Seç <span class="text-danger">*</span></label>
                                        <select class="form-select" id="category_id" name="category_id" required>
                                            <option value="">Local kategori seçin...</option>
                                            {% for category in categories %}
                                                <option value="{{ category.id }}">{{ category.name }}</option>
                                            {% endfor %}
                                        </select>
                                        <div class="form-text">Eşleştirmek istediğiniz local kategoriyi seçin.</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Marketplace Category Selection -->
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-shop"></i> Marketplace Kategori
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="marketplace_id" class="form-label">Marketplace <span class="text-danger">*</span></label>
                                        <select class="form-select" id="marketplace_id" name="marketplace_id" required onchange="loadMarketplaceCategories()">
                                            <option value="">Marketplace seçin...</option>
                                            {% for marketplace in marketplaces %}
                                                <option value="{{ marketplace.id }}">{{ marketplace.name }}</option>
                                            {% endfor %}
                                        </select>
                                        <div class="form-text">Önce marketplace'i seçin, sonra kategori listesi yüklenecek.</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="marketplace_category_id" class="form-label">Marketplace Kategori <span class="text-danger">*</span></label>
                                        <select class="form-select" id="marketplace_category_id" name="marketplace_category_id" required disabled>
                                            <option value="">Önce marketplace seçin...</option>
                                        </select>
                                        <div class="form-text">Eşleştirmek istediğiniz marketplace kategorisini seçin.</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="d-flex justify-content-center gap-2 mt-4">
                        <button type="submit" class="btn btn-primary" disabled id="submitBtn">
                            <i class="bi bi-check-circle"></i> Eşleştirme Oluştur
                        </button>
                        <a href="{{ url_for('categories.category_mappings') }}" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> İptal
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Preview Card -->
        <div class="card mt-4" id="previewCard" style="display: none;">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="bi bi-eye"></i> Eşleştirme Önizleme
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-5">
                        <div class="p-3 bg-light rounded">
                            <i class="bi bi-tags text-primary" style="font-size: 2rem;"></i>
                            <h6 class="mt-2">Local Kategori</h6>
                            <span id="previewLocalCategory" class="fw-bold text-primary"></span>
                        </div>
                    </div>
                    <div class="col-md-2 d-flex align-items-center justify-content-center">
                        <i class="bi bi-arrow-left-right text-info" style="font-size: 2rem;"></i>
                    </div>
                    <div class="col-md-5">
                        <div class="p-3 bg-light rounded">
                            <i class="bi bi-shop text-success" style="font-size: 2rem;"></i>
                            <h6 class="mt-2">Marketplace Kategori</h6>
                            <span id="previewMarketplaceCategory" class="fw-bold text-success"></span>
                            <br>
                            <small class="text-muted" id="previewMarketplaceName"></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Help Card -->
        <div class="card mt-4 border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="bi bi-lightbulb"></i> Eşleştirme Rehberi
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Eşleştirme Kuralları:</h6>
                        <ul>
                            <li>Her local kategori için marketplace başına sadece bir eşleştirme yapılabilir</li>
                            <li>Aynı marketplace kategorisi birden fazla local kategori ile eşleştirilebilir</li>
                            <li>Eşleştirmeler ürün senkronizasyonunda kullanılır</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>İpuçları:</h6>
                        <ul>
                            <li>Benzer anlamdaki kategorileri eşleştirin</li>
                            <li>Marketplace kategorilerini önce senkronize etmeyi unutmayın</li>
                            <li>Yaprak kategorileri (alt kategorisi olmayan) tercih edin</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function loadMarketplaceCategories() {
    const marketplaceSelect = document.getElementById('marketplace_id');
    const categorySelect = document.getElementById('marketplace_category_id');
    const submitBtn = document.getElementById('submitBtn');
    
    if (!marketplaceSelect.value) {
        categorySelect.innerHTML = '<option value="">Önce marketplace seçin...</option>';
        categorySelect.disabled = true;
        submitBtn.disabled = true;
        return;
    }
    
    // Show loading
    categorySelect.innerHTML = '<option value="">Kategoriler yükleniyor...</option>';
    categorySelect.disabled = true;
    
    // Fetch categories
    fetch(`/categories/api/marketplace-categories/${marketplaceSelect.value}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                categorySelect.innerHTML = '<option value="">Marketplace kategori seçin...</option>';
                
                data.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    categorySelect.appendChild(option);
                });
                
                categorySelect.disabled = false;
            } else {
                categorySelect.innerHTML = '<option value="">Kategori yüklenemedi</option>';
                alert('Kategoriler yüklenirken hata oluştu!');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            categorySelect.innerHTML = '<option value="">Hata oluştu</option>';
            alert('Kategoriler yüklenirken hata oluştu!');
        });
}

function updatePreview() {
    const localCategorySelect = document.getElementById('category_id');
    const marketplaceSelect = document.getElementById('marketplace_id');
    const marketplaceCategorySelect = document.getElementById('marketplace_category_id');
    const submitBtn = document.getElementById('submitBtn');
    const previewCard = document.getElementById('previewCard');
    
    // Check if all fields are selected
    const allSelected = localCategorySelect.value && marketplaceSelect.value && marketplaceCategorySelect.value;
    
    if (allSelected) {
        // Update preview
        document.getElementById('previewLocalCategory').textContent = 
            localCategorySelect.options[localCategorySelect.selectedIndex].text;
        document.getElementById('previewMarketplaceCategory').textContent = 
            marketplaceCategorySelect.options[marketplaceCategorySelect.selectedIndex].text;
        document.getElementById('previewMarketplaceName').textContent = 
            marketplaceSelect.options[marketplaceSelect.selectedIndex].text;
        
        previewCard.style.display = 'block';
        submitBtn.disabled = false;
    } else {
        previewCard.style.display = 'none';
        submitBtn.disabled = true;
    }
}

// Add event listeners
document.getElementById('category_id').addEventListener('change', updatePreview);
document.getElementById('marketplace_id').addEventListener('change', updatePreview);
document.getElementById('marketplace_category_id').addEventListener('change', updatePreview);

// Form validation
document.getElementById('mappingForm').addEventListener('submit', function(e) {
    const localCategory = document.getElementById('category_id').value;
    const marketplace = document.getElementById('marketplace_id').value;
    const marketplaceCategory = document.getElementById('marketplace_category_id').value;
    
    if (!localCategory || !marketplace || !marketplaceCategory) {
        e.preventDefault();
        alert('Lütfen tüm alanları doldurun!');
        return false;
    }
});

// Auto-focus on first select
document.getElementById('category_id').focus();
</script>
{% endblock %}