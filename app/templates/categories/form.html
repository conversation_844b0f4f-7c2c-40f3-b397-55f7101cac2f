{% extends "base.html" %}

{% block title %}{{ title }} - PazarYeri{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-tags"></i> {{ title }}
                </h5>
                <a href="{{ url_for('categories.categories') }}" class="btn btn-sm btn-secondary">
                    <i class="bi bi-arrow-left"></i> Geri Dön
                </a>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="name" class="form-label">Kategori Adı <span class="text-danger">*</span></label>
                        <input type="text" 
                               class="form-control" 
                               id="name" 
                               name="name" 
                               value="{{ category.name if category else '' }}"
                               required
                               placeholder="Kategori adını girin">
                        <div class="form-text"><PERSON>gori için açıklayıcı bir ad girin.</div>
                    </div>

                    <div class="mb-3">
                        <label for="parent_id" class="form-label">Üst Kategori</label>
                        <select class="form-select" id="parent_id" name="parent_id">
                            <option value="">Ana Kategori (Üst kategorisi yok)</option>
                            {% for parent in parent_categories %}
                                <option value="{{ parent.id }}" 
                                        {% if category and category.parent_id == parent.id %}selected{% endif %}>
                                    {{ parent.name }}
                                </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">
                            Bu kategori bir alt kategori ise, üst kategorisini seçin. 
                            Boş bırakırsanız ana kategori olarak oluşturulur.
                        </div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle"></i> 
                            {% if category %}Güncelle{% else %}Ekle{% endif %}
                        </button>
                        <a href="{{ url_for('categories.categories') }}" class="btn btn-secondary">
                            <i class="bi bi-x-circle"></i> İptal
                        </a>
                    </div>
                </form>
            </div>
        </div>

        {% if category %}
        <!-- Category Info Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="bi bi-info-circle"></i> Kategori Bilgileri
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>ID:</strong> {{ category.id }}<br>
                        <strong>Oluşturma Tarihi:</strong> 
                        {% if category.created_at %}
                            {% if category.created_at is string %}
                                {{ category.created_at }}
                            {% else %}
                                {{ category.created_at.strftime('%d.%m.%Y %H:%M') }}
                            {% endif %}
                        {% else %}
                            Bilinmiyor
                        {% endif %}<br>
                    </div>
                    <div class="col-md-6">
                        <strong>Son Güncelleme:</strong> 
                        {% if category.updated_at %}
                            {% if category.updated_at is string %}
                                {{ category.updated_at }}
                            {% else %}
                                {{ category.updated_at.strftime('%d.%m.%Y %H:%M') }}
                            {% endif %}
                        {% else %}
                            Bilinmiyor
                        {% endif %}<br>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Help Card -->
        <div class="card mt-4 border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="bi bi-lightbulb"></i> Kategori Yönetimi Hakkında
                </h6>
            </div>
            <div class="card-body">
                <ul class="mb-0">
                    <li><strong>Ana Kategoriler:</strong> Üst kategorisi olmayan kategorilerdir. Genel sınıflandırma için kullanılır.</li>
                    <li><strong>Alt Kategoriler:</strong> Bir üst kategoriye bağlı olan kategorilerdir. Daha spesifik sınıflandırma için kullanılır.</li>
                    <li><strong>Kategori Adı:</strong> Benzersiz ve açıklayıcı olmalıdır.</li>
                    <li><strong>Silme:</strong> Alt kategorileri olan kategoriler silinemez. Önce alt kategorileri silmelisiniz.</li>
                    <li><strong>Eşleştirme:</strong> Local kategoriler marketplace kategorileri ile eşleştirilebilir.</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const nameInput = document.getElementById('name');
    if (!nameInput.value.trim()) {
        e.preventDefault();
        alert('Kategori adı gerekli!');
        nameInput.focus();
        return false;
    }
});

// Auto-focus on name input
document.getElementById('name').focus();
</script>
{% endblock %}