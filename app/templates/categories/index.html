{% extends "base.html" %}

{% block title %}Kate<PERSON>iler - PazarYeri{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-tags"></i> <PERSON><PERSON><PERSON>önetim<PERSON></h2>
    <a href="{{ url_for('categories.add_category') }}" class="btn btn-primary">
        <i class="bi bi-plus-circle"></i> <PERSON><PERSON><PERSON>
    </a>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Local Kategoriler</h5>
                        <h3>{{ categories|length }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-tags fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Marketplace Kategoriler</h5>
                        <h3>{{ marketplace_categories_count }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-shop fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Kategori Eşleştirmeleri</h5>
                        <h3>{{ mappings_count }}</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-arrow-left-right fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Navigation Tabs -->
<ul class="nav nav-tabs mb-3" id="categoryTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="local-tab" data-bs-toggle="tab" data-bs-target="#local" type="button" role="tab">
            <i class="bi bi-tags"></i> Local Kategoriler
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="marketplace-tab" data-bs-toggle="tab" data-bs-target="#marketplace" type="button" role="tab">
            <i class="bi bi-shop"></i> Marketplace Kategoriler
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="mappings-tab" data-bs-toggle="tab" data-bs-target="#mappings" type="button" role="tab">
            <i class="bi bi-arrow-left-right"></i> Kategori Eşleştirmeleri
        </button>
    </li>
</ul>

<!-- Tab Content -->
<div class="tab-content" id="categoryTabContent">
    <!-- Local Categories Tab -->
    <div class="tab-pane fade show active" id="local" role="tabpanel">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Local Kategoriler</h5>
                <a href="{{ url_for('categories.add_category') }}" class="btn btn-sm btn-primary">
                    <i class="bi bi-plus"></i> Yeni Kategori
                </a>
            </div>
            <div class="card-body">
                {% if categories %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Kategori Adı</th>
                                    <th>Üst Kategori</th>
                                    <th>Oluşturma Tarihi</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for category in categories %}
                                <tr>
                                    <td>{{ category.id }}</td>
                                    <td>
                                        {% if category.parent_id %}
                                            <span class="text-muted ms-3">└─</span>
                                        {% endif %}
                                        {{ category.name }}
                                    </td>
                                    <td>
                                        {% if category.parent_id %}
                                            {% for parent in categories %}
                                                {% if parent.id == category.parent_id %}
                                                    {{ parent.name }}
                                                {% endif %}
                                            {% endfor %}
                                        {% else %}
                                            <span class="text-muted">Ana Kategori</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if category.created_at %}
                                            {% if category.created_at is string %}
                                                {{ category.created_at }}
                                            {% else %}
                                                {{ category.created_at.strftime('%d.%m.%Y %H:%M') }}
                                            {% endif %}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('categories.edit_category', id=category.id) }}" 
                                               class="btn btn-outline-primary" title="Düzenle">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="deleteCategory({{ category.id }}, '{{ category.name }}')" 
                                                    title="Sil">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-tags text-muted" style="font-size: 3rem;"></i>
                        <p class="text-muted mt-2">Henüz kategori eklenmemiş.</p>
                        <a href="{{ url_for('categories.add_category') }}" class="btn btn-primary">
                            <i class="bi bi-plus"></i> İlk Kategoriyi Ekle
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Marketplace Categories Tab -->
    <div class="tab-pane fade" id="marketplace" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Marketplace Kategoriler</h5>
            </div>
            <div class="card-body">
                <a href="{{ url_for('categories.marketplace_categories') }}" class="btn btn-primary">
                    <i class="bi bi-arrow-right"></i> Marketplace Kategorileri Yönet
                </a>
            </div>
        </div>
    </div>

    <!-- Category Mappings Tab -->
    <div class="tab-pane fade" id="mappings" role="tabpanel">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Kategori Eşleştirmeleri</h5>
            </div>
            <div class="card-body">
                <a href="{{ url_for('categories.category_mappings') }}" class="btn btn-primary">
                    <i class="bi bi-arrow-right"></i> Kategori Eşleştirmelerini Yönet
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function deleteCategory(categoryId, categoryName) {
    if (confirm(`"${categoryName}" kategorisini silmek istediğinizden emin misiniz?`)) {
        fetch(`/categories/delete/${categoryId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                location.reload();
            } else {
                alert('Hata: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Bir hata oluştu!');
        });
    }
}
</script>
{% endblock %}