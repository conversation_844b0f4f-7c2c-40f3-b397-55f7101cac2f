{% extends "base.html" %}

{% block title %}<PERSON><PERSON><PERSON>ş<PERSON>rmeleri - PazarYeri{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="bi bi-arrow-left-right"></i> <PERSON>gor<PERSON>şleştirmeleri</h2>
    <div class="btn-group">
        <a href="{{ url_for('categories.add_mapping') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Eşleştirme Ekle
        </a>
        <a href="{{ url_for('categories.categories') }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Kategori Yönetimine Dön
        </a>
    </div>
</div>

<!-- Mappings Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-list-ul"></i> Akt<PERSON>
        </h5>
        <span class="badge bg-primary">{{ mappings|length }} eşleştirme</span>
    </div>
    <div class="card-body">
        {% if mappings %}
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Local Kategori</th>
                            <th>Marketplace</th>
                            <th>Marketplace Kategori</th>
                            <th>Oluşturma Tarihi</th>
                            <th>İşlemler</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for mapping in mappings %}
                        <tr>
                            <td>{{ mapping.id }}</td>
                            <td>
                                <span class="fw-bold text-primary">
                                    {{ mapping.category_name }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-info">
                                    {{ mapping.marketplace_name }}
                                </span>
                            </td>
                            <td>
                                {{ mapping.marketplace_category_name }}
                            </td>
                            <td>
                                {% if mapping.created_at %}
                                    {% if mapping.created_at is string %}
                                        {{ mapping.created_at }}
                                    {% else %}
                                        {{ mapping.created_at.strftime('%d.%m.%Y %H:%M') }}
                                    {% endif %}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>
                                <button type="button" 
                                        class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteMapping({{ mapping.id }}, '{{ mapping.category_name }}', '{{ mapping.marketplace_name }}')" 
                                        title="Eşleştirmeyi Sil">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="bi bi-arrow-left-right text-muted" style="font-size: 3rem;"></i>
                <p class="text-muted mt-3">Henüz kategori eşleştirmesi oluşturulmamış.</p>
                <p class="text-muted">Local kategorilerinizi marketplace kategorileri ile eşleştirerek ürün senkronizasyonunu geliştirebilirsiniz.</p>
                <a href="{{ url_for('categories.add_mapping') }}" class="btn btn-primary">
                    <i class="bi bi-plus"></i> İlk Eşleştirmeyi Oluştur
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Stats and Info Cards -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card border-success">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="bi bi-check-circle"></i> Eşleştirme Avantajları
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li><i class="bi bi-check text-success"></i> Otomatik kategori atama</li>
                    <li><i class="bi bi-check text-success"></i> Ürün senkronizasyonu iyileştirme</li>
                    <li><i class="bi bi-check text-success"></i> Marketplace uyumluluğu</li>
                    <li><i class="bi bi-check text-success"></i> Veri tutarlılığı</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="bi bi-lightbulb"></i> Eşleştirme Rehberi
                </h6>
            </div>
            <div class="card-body">
                <ol class="mb-0">
                    <li>Local kategorilerinizi oluşturun</li>
                    <li>Marketplace kategorilerini senkronize edin</li>
                    <li>Benzer kategorileri eşleştirin</li>
                    <li>Ürün senkronizasyonunu test edin</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="card mt-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="bi bi-lightning"></i> Hızlı İşlemler
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <a href="{{ url_for('categories.categories') }}" class="btn btn-outline-primary w-100 mb-2">
                    <i class="bi bi-tags"></i><br>
                    Local Kategoriler
                </a>
            </div>
            <div class="col-md-4">
                <a href="{{ url_for('categories.marketplace_categories') }}" class="btn btn-outline-success w-100 mb-2">
                    <i class="bi bi-shop"></i><br>
                    Marketplace Kategoriler
                </a>
            </div>
            <div class="col-md-4">
                <a href="{{ url_for('categories.add_mapping') }}" class="btn btn-outline-info w-100 mb-2">
                    <i class="bi bi-plus-circle"></i><br>
                    Yeni Eşleştirme
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function deleteMapping(mappingId, categoryName, marketplaceName) {
    const message = `"${categoryName}" - "${marketplaceName}" eşleştirmesini silmek istediğinizden emin misiniz?`;
    
    if (confirm(message)) {
        fetch(`/categories/mappings/delete/${mappingId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                location.reload();
            } else {
                alert('Hata: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Bir hata oluştu!');
        });
    }
}

// Add confirmation for all delete operations
document.querySelectorAll('.btn-outline-danger').forEach(button => {
    button.addEventListener('click', function(e) {
        if (!confirm('Bu işlemi gerçekleştirmek istediğinizden emin misiniz?')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}