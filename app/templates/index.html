{% extends "base.html" %}

{% block title %}Ana <PERSON>fa - PazarYeri{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="bi bi-speedometer2"></i> Dashboard
        </h1>
    </div>
</div>

<!-- İstatistik Kartları -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Toplam Ürün</h6>
                        <h2 class="mb-0">{{ products_count }}</h2>
                    </div>
                    <i class="bi bi-box fs-1 opacity-50"></i>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('products.products') }}" class="text-white text-decoration-none">
                    Ürünleri Görüntüle <i class="bi bi-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Tedarikçiler</h6>
                        <h2 class="mb-0">{{ suppliers_count }}</h2>
                    </div>
                    <i class="bi bi-truck fs-1 opacity-50"></i>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('main.suppliers') }}" class="text-white text-decoration-none">
                    Tedarikçileri Görüntüle <i class="bi bi-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Aktif Marketplaces</h6>
                        <h2 class="mb-0">{{ marketplaces_count }}</h2>
                    </div>
                    <i class="bi bi-shop fs-1 opacity-50"></i>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('api.marketplaces') }}" class="text-white text-decoration-none">
                    Marketplaces Yönet <i class="bi bi-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Marketplace Ürünler</h6>
                        <h2 class="mb-0">{{ marketplace_products_count }}</h2>
                    </div>
                    <i class="bi bi-shop-window fs-1 opacity-50"></i>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('marketplace.marketplace_products') }}" class="text-white text-decoration-none">
                    Marketplace Ürünleri Görüntüle <i class="bi bi-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- İkinci Satır İstatistikler -->
<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-secondary">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Ürün Eşleştirmeleri</h6>
                        <h2 class="mb-0">{{ product_mappings_count }}</h2>
                    </div>
                    <i class="bi bi-arrow-left-right fs-1 opacity-50"></i>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('marketplace.product_mappings') }}" class="text-white text-decoration-none">
                    Eşleştirmeleri Yönet <i class="bi bi-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card text-white bg-dark">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">Tedarikçi Eşleşmeleri</h6>
                        <h2 class="mb-0">{{ matches_count }}</h2>
                    </div>
                    <i class="bi bi-link-45deg fs-1 opacity-50"></i>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('main.suppliers') }}" class="text-white text-decoration-none">
                    Tedarikçileri Görüntüle <i class="bi bi-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="bi bi-arrow-repeat"></i> Son Sync İşlemleri</h5>
            </div>
            <div class="card-body">
                {% if recent_syncs %}
                    {% for sync in recent_syncs %}
                    <div class="d-flex justify-content-between align-items-center mb-2 {% if not loop.last %}border-bottom pb-2{% endif %}">
                        <div>
                            <strong>{{ sync.marketplace_name }}</strong>
                            <small class="text-muted d-block">{{ sync.started_at.split(' ')[0] if sync.started_at else '-' }}</small>
                        </div>
                        <div class="text-end">
                            {% if sync.status == 'completed' %}
                                <span class="badge bg-success">✓ {{ sync.records_success }}/{{ sync.records_processed }}</span>
                            {% elif sync.status == 'failed' %}
                                <span class="badge bg-danger">✗ Başarısız</span>
                            {% else %}
                                <span class="badge bg-warning">⏳ {{ sync.status }}</span>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                    <div class="text-center mt-3">
                        <a href="{{ url_for('marketplace.sync_status') }}" class="btn btn-sm btn-outline-success">
                            Tüm Sync Logları
                        </a>
                    </div>
                {% else %}
                    <div class="text-center text-muted">
                        <i class="bi bi-arrow-repeat fs-1"></i><br>
                        <small>Henüz sync işlemi yapılmadı</small><br>
                        <a href="{{ url_for('marketplace.marketplace_products') }}" class="btn btn-sm btn-success mt-2">
                            İlk Sync'i Başlat
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Hızlı İşlemler -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="bi bi-lightning"></i> Hızlı İşlemler</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('products.add_product') }}" class="btn btn-outline-primary">
                        <i class="bi bi-plus-circle"></i> Yeni Ürün Ekle
                    </a>
                    <a href="{{ url_for('marketplace.marketplace_products') }}" class="btn btn-outline-warning">
                        <i class="bi bi-arrow-repeat"></i> Marketplace Sync
                    </a>
                    <a href="{{ url_for('marketplace.product_mappings') }}" class="btn btn-outline-success">
                        <i class="bi bi-arrow-left-right"></i> Ürün Eşleştir
                    </a>
                    <a href="{{ url_for('api.marketplaces') }}" class="btn btn-outline-info">
                        <i class="bi bi-gear"></i> Marketplace Ayarları
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Sistem Bilgisi</h5>
            </div>
            <div class="card-body">
                <p><strong>Versiyon:</strong> 1.0.0</p>
                <p><strong>Veritabanı:</strong> SQLite</p>
                <p><strong>Framework:</strong> Flask + Bootstrap 5</p>
                <p class="mb-0"><strong>Desteklenen Siteler:</strong> Amazon, Ozon, eBay, Alibaba, Hepsiburada, Trendyol, N11 ve daha fazlası...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}