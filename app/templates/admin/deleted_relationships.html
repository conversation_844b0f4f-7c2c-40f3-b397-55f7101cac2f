{% extends "base.html" %}

{% block title %}<PERSON><PERSON><PERSON> Admin <PERSON> PazarY<PERSON>{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-trash"></i> <PERSON>line<PERSON>n-Tedarikçi İlişkileri</h1>
    <div>
        <span class="badge bg-warning">{{ deleted_relationships|length }} <PERSON><PERSON><PERSON></span>
        <a href="{{ url_for('main.suppliers') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Tedarikçilere Dön
        </a>
    </div>
</div>

{% if deleted_relationships %}
<div class="alert alert-info">
    <i class="bi bi-info-circle"></i> Bu sayfada soft delete ile silinen ürün-tedarikçi ilişkileri görüntülenir. 
    İlişkileri geri yükleyebilir veya kalıcı olarak silebilirsiniz.
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Ürün</th>
                        <th>Tedarikçi</th>
                        <th>Ürün Linki</th>
                        <th>Fiyat</th>
                        <th>Silinme Tarihi</th>
                        <th>İşlemler</th>
                    </tr>
                </thead>
                <tbody>
                    {% for rel in deleted_relationships %}
                    <tr id="relationship-{{ rel.product_id }}-{{ rel.supplier_id }}">
                        <td>
                            <strong>{{ rel.product_name }}</strong>
                            <br><small class="text-muted">SKU: {{ rel.sku }}</small>
                        </td>
                        <td>
                            <strong>{{ rel.supplier_name }}</strong>
                            {% if rel.supplier_website %}
                                <br><small class="text-muted">
                                    <a href="{{ rel.supplier_website }}" target="_blank" class="text-decoration-none">
                                        {{ rel.supplier_website }}
                                    </a>
                                </small>
                            {% endif %}
                        </td>
                        <td>
                            {% if rel.supplier_product_url %}
                                <a href="{{ rel.supplier_product_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-link-45deg"></i> Ürüne Git
                                </a>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if rel.supplier_price %}
                                <strong>₺{{ "%.2f"|format(rel.supplier_price) }}</strong>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ rel.deleted_at }}</small>
                            {% if rel.deleted_by %}
                                <br><small class="text-muted">Silen: User {{ rel.deleted_by }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-success" 
                                        onclick="restoreRelationship({{ rel.product_id }}, {{ rel.supplier_id }})"
                                        title="Geri Yükle">
                                    <i class="bi bi-arrow-counterclockwise"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" 
                                        onclick="hardDeleteRelationship({{ rel.product_id }}, {{ rel.supplier_id }})"
                                        title="Kalıcı Sil">
                                    <i class="bi bi-trash-fill"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Bulk Actions -->
<div class="mt-3">
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Toplu İşlemler</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <button class="btn btn-success" onclick="bulkRestore()">
                        <i class="bi bi-arrow-counterclockwise"></i> Tümünü Geri Yükle
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-danger" onclick="bulkHardDelete()">
                        <i class="bi bi-trash-fill"></i> Tümünü Kalıcı Sil
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

{% else %}
<div class="card">
    <div class="card-body text-center py-5">
        <i class="bi bi-check-circle display-1 text-success"></i>
        <h3 class="mt-3">Hiç Silinen İlişki Yok</h3>
        <p class="text-muted">Tüm ürün-tedarikçi ilişkileri aktif durumda.</p>
        <a href="{{ url_for('main.suppliers') }}" class="btn btn-primary">
            <i class="bi bi-truck"></i> Tedarikçileri Görüntüle
        </a>
    </div>
</div>
{% endif %}

{% endblock %}

{% block extra_js %}
<script>
function restoreRelationship(productId, supplierId) {
    if (confirm('Bu ilişkiyi geri yüklemek istediğinizden emin misiniz?')) {
        $.post(`/api/product-supplier/restore/${productId}/${supplierId}`)
            .done(function(response) {
                if (response.status === 'success') {
                    // Remove from table
                    $(`#relationship-${productId}-${supplierId}`).fadeOut(500, function() {
                        $(this).remove();
                        
                        // If no more relationships, reload page to show empty state
                        if ($('tbody tr').length === 0) {
                            location.reload();
                        }
                    });
                    
                    // Update badge count
                    const currentCount = parseInt($('.badge.bg-warning').text().split(' ')[0]);
                    $('.badge.bg-warning').text(`${currentCount - 1} Silinen İlişki`);
                    
                    showToast('success', response.message);
                } else {
                    showToast('error', response.message);
                }
            })
            .fail(function() {
                showToast('error', 'İlişki geri yüklenirken hata oluştu!');
            });
    }
}

function hardDeleteRelationship(productId, supplierId) {
    if (confirm('Bu ilişkiyi kalıcı olarak silmek istediğinizden emin misiniz? Bu işlem geri alınamaz!')) {
        $.ajax({
            url: `/api/product-supplier/hard-delete/${productId}/${supplierId}`,
            method: 'DELETE'
        })
        .done(function(response) {
            if (response.status === 'success') {
                // Remove from table
                $(`#relationship-${productId}-${supplierId}`).fadeOut(500, function() {
                    $(this).remove();
                    
                    // If no more relationships, reload page to show empty state
                    if ($('tbody tr').length === 0) {
                        location.reload();
                    }
                });
                
                // Update badge count
                const currentCount = parseInt($('.badge.bg-warning').text().split(' ')[0]);
                $('.badge.bg-warning').text(`${currentCount - 1} Silinen İlişki`);
                
                showToast('success', response.message);
            } else {
                showToast('error', response.message);
            }
        })
        .fail(function() {
            showToast('error', 'İlişki silinirken hata oluştu!');
        });
    }
}

function bulkRestore() {
    if (confirm('Tüm silinen ilişkileri geri yüklemek istediğinizden emin misiniz?')) {
        const relationships = [];
        $('tbody tr').each(function() {
            const id = $(this).attr('id');
            const [, productId, supplierId] = id.split('-');
            relationships.push({productId, supplierId});
        });
        
        let completed = 0;
        const total = relationships.length;
        
        relationships.forEach(rel => {
            $.post(`/api/product-supplier/restore/${rel.productId}/${rel.supplierId}`)
                .always(() => {
                    completed++;
                    if (completed === total) {
                        location.reload();
                    }
                });
        });
    }
}

function bulkHardDelete() {
    if (confirm('Tüm silinen ilişkileri kalıcı olarak silmek istediğinizden emin misiniz? Bu işlem geri alınamaz!')) {
        const relationships = [];
        $('tbody tr').each(function() {
            const id = $(this).attr('id');
            const [, productId, supplierId] = id.split('-');
            relationships.push({productId, supplierId});
        });
        
        let completed = 0;
        const total = relationships.length;
        
        relationships.forEach(rel => {
            $.ajax({
                url: `/api/product-supplier/hard-delete/${rel.productId}/${rel.supplierId}`,
                method: 'DELETE'
            })
            .always(() => {
                completed++;
                if (completed === total) {
                    location.reload();
                }
            });
        });
    }
}

function showToast(type, message) {
    // Simple toast notification
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'check-circle' : 'exclamation-triangle';
    
    const toast = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            <i class="bi bi-${icon}"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);
    
    $('body').append(toast);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        toast.alert('close');
    }, 3000);
}
</script>
{% endblock %}