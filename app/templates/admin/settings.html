{% extends "base.html" %}

{% block title %}Genel Ayarlar - Admin - PazarYeri{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1>Gene<PERSON> Ayarlar</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">Ana Sayfa</a></li>
                            <li class="breadcrumb-item">Admin</li>
                            <li class="breadcrumb-item active">Genel Ayarlar</li>
                        </ol>
                    </nav>
                </div>
            </div>

            <!-- Settings Form -->
            <div class="row">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Sistem Ayarları</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="{{ url_for('settings.update') }}">
                                <!-- Default Profit Margin -->
                                <div class="mb-4">
                                    <label for="default_profit_margin" class="form-label">
                                        <i class="bi bi-percent"></i> Varsayılan Kar Marjı (%)
                                    </label>
                                    <input type="number" 
                                           class="form-control" 
                                           id="default_profit_margin" 
                                           name="default_profit_margin" 
                                           value="{{ settings.get('default_profit_margin', {}).get('value', '20') }}"
                                           min="0" 
                                           max="100" 
                                           step="0.1"
                                           required>
                                    <div class="form-text">
                                        Yeni ürünler eklenirken kullanılacak varsayılan kar marjı yüzdesi
                                    </div>
                                </div>

                                <!-- Minimum Profit Margin -->
                                <div class="mb-4">
                                    <label for="minimum_profit_margin" class="form-label">
                                        <i class="bi bi-graph-down"></i> Minimum Kar Marjı (%)
                                    </label>
                                    <input type="number" 
                                           class="form-control" 
                                           id="minimum_profit_margin" 
                                           name="minimum_profit_margin" 
                                           value="{{ settings.get('minimum_profit_margin', {}).get('value', '10') }}"
                                           min="0" 
                                           max="100" 
                                           step="0.1"
                                           required>
                                    <div class="form-text">
                                        Ürünler için izin verilen minimum kar marjı yüzdesi
                                    </div>
                                </div>

                                <!-- Product Name Prefix -->
                                <div class="mb-4">
                                    <label for="product_name_prefix" class="form-label">
                                        <i class="bi bi-tag"></i> Ürün Tanımı Ön Eki
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="product_name_prefix" 
                                           name="product_name_prefix" 
                                           value="{{ settings.get('product_name_prefix', {}).get('value', '') }}"
                                           maxlength="100"
                                           placeholder="Örn: [MAĞAZA ADI]">
                                    <div class="form-text">
                                        Ürün isimlerinin başına eklenecek ön ek (isteğe bağlı)
                                    </div>
                                </div>

                                <!-- Default Currency -->
                                <div class="mb-4">
                                    <label for="default_currency" class="form-label">
                                        <i class="bi bi-currency-exchange"></i> Varsayılan Para Birimi
                                    </label>
                                    <select class="form-select" id="default_currency" name="default_currency" required>
                                        {% set current_currency = settings.get('default_currency', {}).get('value', 'TRY') %}
                                        <option value="TRY" {% if current_currency == 'TRY' %}selected{% endif %}>₺ Türk Lirası (TRY)</option>
                                        <option value="USD" {% if current_currency == 'USD' %}selected{% endif %}>$ Amerikan Doları (USD)</option>
                                        <option value="EUR" {% if current_currency == 'EUR' %}selected{% endif %}>€ Euro (EUR)</option>
                                        <option value="RUB" {% if current_currency == 'RUB' %}selected{% endif %}>₽ Rus Rublesi (RUB)</option>
                                        <option value="GBP" {% if current_currency == 'GBP' %}selected{% endif %}>£ İngiliz Sterlini (GBP)</option>
                                    </select>
                                    <div class="form-text">
                                        Sistemde kullanılacak varsayılan para birimi
                                    </div>
                                </div>

                                <!-- Stock Management Section -->
                                <hr class="my-4">
                                <h6 class="mb-3"><i class="bi bi-box-seam"></i> Stok Yönetimi</h6>
                                
                                <!-- Unknown Stock Range -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <label for="unknown_stock_value_1" class="form-label">
                                                Stok Değeri 1 (Minimum)
                                            </label>
                                            <input type="number" 
                                                   class="form-control" 
                                                   id="unknown_stock_value_1" 
                                                   name="unknown_stock_value_1" 
                                                   value="{{ settings.get('unknown_stock_value_1', {}).get('value', '5') }}"
                                                   min="0" 
                                                   step="1"
                                                   required>
                                            <div class="form-text">
                                                Minimum stok değeri
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-4">
                                            <label for="unknown_stock_value_2" class="form-label">
                                                Stok Değeri 2 (Maksimum)
                                            </label>
                                            <input type="number" 
                                                   class="form-control" 
                                                   id="unknown_stock_value_2" 
                                                   name="unknown_stock_value_2" 
                                                   value="{{ settings.get('unknown_stock_value_2', {}).get('value', '10') }}"
                                                   min="0" 
                                                   step="1"
                                                   required>
                                            <div class="form-text">
                                                Maksimum stok değeri
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-text mb-4">
                                    <i class="bi bi-info-circle"></i> Stok değeri bilinmeyen ürünler için kullanılacak stok aralığı
                                </div>

                                <!-- Submit Button -->
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-lg"></i> Ayarları Kaydet
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Info Panel -->
                <div class="col-lg-4">
                    <div class="card bg-light">
                        <div class="card-header">
                            <h5 class="mb-0">Bilgi</h5>
                        </div>
                        <div class="card-body">
                            <h6>Ayarların Kullanımı</h6>
                            <ul class="small">
                                <li><strong>Varsayılan Kar Marjı:</strong> Yeni ürün eklerken otomatik olarak doldurulur</li>
                                <li><strong>Minimum Kar Marjı:</strong> Ürün fiyatlandırmalarında alt limit olarak kullanılır</li>
                                <li><strong>Ürün Tanımı Ön Eki:</strong> Marketplace'lere gönderilen ürün isimlerinin başına eklenir</li>
                                <li><strong>Varsayılan Para Birimi:</strong> Tüm finansal işlemlerde kullanılır</li>
                                <li><strong>Stok Aralığı:</strong> Marketplace'den stok bilgisi gelmeyen ürünler için rastgele stok ataması yapılır</li>
                            </ul>
                            
                            <div class="alert alert-info mt-3">
                                <i class="bi bi-info-circle"></i>
                                <small>Bu ayarlar tüm kullanıcılar için geçerlidir ve sistem genelinde uygulanır.</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Stock range validation
    const stockValue1 = document.getElementById('unknown_stock_value_1');
    const stockValue2 = document.getElementById('unknown_stock_value_2');
    
    function validateStockRange() {
        const val1 = parseInt(stockValue1.value) || 0;
        const val2 = parseInt(stockValue2.value) || 0;
        
        if (val1 > val2) {
            stockValue1.setCustomValidity('Stok Değeri 1, Stok Değeri 2\'den büyük olamaz!');
            stockValue2.setCustomValidity('Stok Değeri 2, Stok Değeri 1\'den küçük olamaz!');
        } else {
            stockValue1.setCustomValidity('');
            stockValue2.setCustomValidity('');
        }
    }
    
    stockValue1.addEventListener('input', validateStockRange);
    stockValue2.addEventListener('input', validateStockRange);
    
    // Initial validation
    validateStockRange();
});
</script>
{% endblock %}