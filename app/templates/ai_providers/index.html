{% extends "base.html" %}

{% block title %}AI APIs - PazarYeri{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-robot"></i> AI APIs</h1>
    <div>
        <span class="badge bg-info me-2">{{ providers|length }} Provider</span>
        <a href="{{ url_for('ai_providers.add') }}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> Yeni AI Provider Ekle
        </a>
    </div>
</div>

<div class="row">
    {% for provider in providers %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">{{ provider.name }}</h6>
                <div>
                    {% if provider.has_credentials %}
                        <span class="badge bg-success" title="API key mevcut">
                            <i class="bi bi-key-fill"></i>
                        </span>
                    {% else %}
                        <span class="badge bg-warning" title="API key eksik">
                            <i class="bi bi-key"></i>
                        </span>
                    {% endif %}
                    
                    {% if provider.is_free %}
                        <span class="badge bg-info">Ücretsiz</span>
                    {% else %}
                        <span class="badge bg-secondary">Ücretli</span>
                    {% endif %}
                    
                    {% if provider.is_active %}
                        <span class="badge bg-success">Aktif</span>
                    {% else %}
                        <span class="badge bg-secondary">Pasif</span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <p class="text-muted small mb-2">{{ provider.base_url }}</p>
                
                {% if provider.model %}
                <p class="mb-2">
                    <small><strong>Model:</strong> {{ provider.model }}</small>
                </p>
                {% endif %}
                
                <div class="mt-3">
                    <small class="text-muted">Rate Limit: {{ provider.rate_limit }}/{{ provider.rate_limit_window }}s</small>
                </div>
                
                <div class="mt-3 d-flex gap-2">
                    <a href="{{ url_for('ai_providers.edit', provider_id=provider.id) }}" 
                       class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-pencil"></i> Düzenle
                    </a>
                    
                    <button class="btn btn-sm btn-outline-{{ 'warning' if provider.is_active else 'success' }}"
                            onclick="toggleProvider({{ provider.id }})">
                        <i class="bi bi-{{ 'pause' if provider.is_active else 'play' }}"></i>
                        {{ 'Pasif Yap' if provider.is_active else 'Aktif Yap' }}
                    </button>
                    
                    <button class="btn btn-sm btn-outline-danger"
                            onclick="deleteProvider({{ provider.id }}, '{{ provider.name }}')">
                        <i class="bi bi-trash"></i> Sil
                    </button>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

{% if not providers %}
<div class="alert alert-info">
    <i class="bi bi-info-circle"></i> Henüz AI provider eklenmemiş. 
    <a href="{{ url_for('ai_providers.add') }}">İlk provider'ı ekleyin</a>.
</div>
{% endif %}

<!-- Predefined Providers Info -->
<div class="mt-5">
    <h3><i class="bi bi-lightbulb"></i> Önerilen Ücretsiz AI Sağlayıcıları</h3>
    <div class="row mt-3">
        {% for default in default_providers %}
        <div class="col-md-6 mb-3">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">{{ default.name }}</h6>
                    <p class="card-text small text-muted">{{ default.description }}</p>
                    <p class="small mb-0"><strong>API URL:</strong> {{ default.base_url }}</p>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<script>
function toggleProvider(providerId) {
    if (!confirm('Provider durumunu değiştirmek istediğinize emin misiniz?')) {
        return;
    }
    
    $.ajax({
        url: `/ai-providers/${providerId}/toggle`,
        method: 'POST',
        success: function(response) {
            if (response.status === 'success') {
                location.reload();
            } else {
                alert('Hata: ' + response.message);
            }
        },
        error: function(xhr) {
            alert('İşlem başarısız: ' + (xhr.responseJSON?.message || 'Bilinmeyen hata'));
        }
    });
}

function deleteProvider(providerId, providerName) {
    if (!confirm(`"${providerName}" provider'ını silmek istediğinize emin misiniz?`)) {
        return;
    }
    
    $.ajax({
        url: `/ai-providers/${providerId}/delete`,
        method: 'POST',
        success: function(response) {
            if (response.status === 'success') {
                location.reload();
            } else {
                alert('Hata: ' + response.message);
            }
        },
        error: function(xhr) {
            alert('İşlem başarısız: ' + (xhr.responseJSON?.message || 'Bilinmeyen hata'));
        }
    });
}
</script>
{% endblock %}