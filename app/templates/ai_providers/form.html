{% extends "base.html" %}

{% block title %}
    {% if action == 'add' %}
        Yeni AI Provider Ekle - PazarYeri
    {% else %}
        AI Provider Düzenle - PazarYeri
    {% endif %}
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    {% if action == 'add' %}
                        <i class="bi bi-plus-circle"></i> Yeni AI Provider Ekle
                    {% else %}
                        <i class="bi bi-pencil"></i> AI Provider Düzenle
                    {% endif %}
                </h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="provider_select" class="form-label">Provider Seçin</label>
                                <select class="form-select" id="provider_select" onchange="fillProviderDefaults()">
                                    <option value="">-- <PERSON>zel Provider --</option>
                                    {% for default in default_providers %}
                                    <option value="{{ loop.index0 }}" 
                                            data-name="{{ default.name }}"
                                            data-url="{{ default.base_url }}"
                                            data-models="{{ default.models|join(',') if default.models else '' }}">
                                        {{ default.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">Önerilen provider'lardan birini seçin veya özel ayarlar girin</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">Provider Adı *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ provider.name if provider else '' }}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="base_url" class="form-label">API Base URL *</label>
                        <input type="url" class="form-control" id="base_url" name="base_url" 
                               value="{{ provider.base_url if provider else '' }}" required>
                        <div class="form-text">Örnek: https://api.openai.com/v1</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="api_key" class="form-label">API Key</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="api_key" name="api_key" 
                                   value="{{ provider.api_key_masked if provider and provider.get('api_key_masked') else '' }}"
                                   placeholder="API anahtarınızı girin">
                            <button class="btn btn-outline-secondary" type="button" onclick="toggleApiKeyVisibility()">
                                <i class="bi bi-eye" id="api_key_toggle_icon"></i>
                            </button>
                        </div>
                        <div class="form-text">API anahtarınız güvenli bir şekilde saklanacaktır</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="model" class="form-label">Varsayılan Model</label>
                        <input type="text" class="form-control" id="model" name="model" 
                               value="{{ provider.model if provider else '' }}"
                               placeholder="Örn: gpt-3.5-turbo, mistral-tiny">
                        <div class="form-text">Bu provider için kullanılacak varsayılan model</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="rate_limit" class="form-label">Rate Limit</label>
                                <input type="number" class="form-control" id="rate_limit" name="rate_limit" 
                                       value="{{ provider.rate_limit if provider else 100 }}" min="1">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="rate_limit_window" class="form-label">Rate Limit Penceresi (saniye)</label>
                                <input type="number" class="form-control" id="rate_limit_window" name="rate_limit_window" 
                                       value="{{ provider.rate_limit_window if provider else 3600 }}" min="1">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_free" name="is_free" 
                                   {% if provider and provider.is_free %}checked{% endif %}>
                            <label class="form-check-label" for="is_free">
                                Ücretsiz Provider
                            </label>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-check-circle"></i> 
                            {% if action == 'add' %}Ekle{% else %}Güncelle{% endif %}
                        </button>
                        <a href="{{ url_for('ai_providers.index') }}" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Geri Dön
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
const defaultProviders = {{ default_providers|tojson|safe }};

function fillProviderDefaults() {
    const select = document.getElementById('provider_select');
    const selectedIndex = select.value;
    
    if (selectedIndex === '') {
        // Clear fields for custom provider
        document.getElementById('name').value = '';
        document.getElementById('base_url').value = '';
        document.getElementById('model').value = '';
        document.getElementById('is_free').checked = false;
        return;
    }
    
    const provider = defaultProviders[selectedIndex];
    if (provider) {
        document.getElementById('name').value = provider.name;
        document.getElementById('base_url').value = provider.base_url;
        document.getElementById('is_free').checked = provider.is_free;
        
        // Set first model as default if available
        if (provider.models && provider.models.length > 0) {
            document.getElementById('model').value = provider.models[0];
        }
    }
}

function toggleApiKeyVisibility() {
    const input = document.getElementById('api_key');
    const icon = document.getElementById('api_key_toggle_icon');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.classList.remove('bi-eye');
        icon.classList.add('bi-eye-slash');
    } else {
        input.type = 'password';
        icon.classList.remove('bi-eye-slash');
        icon.classList.add('bi-eye');
    }
}
</script>
{% endblock %}