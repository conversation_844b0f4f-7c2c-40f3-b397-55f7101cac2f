{% extends "base.html" %}

{% block title %}Tedarikçiler - PazarYeri{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-truck"></i> Tedarikçiler</h1>
    <span class="badge bg-info">{{ suppliers|length }} Tedarikçi</span>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>ID</th>
                        <th>Tedarikçi Adı</th>
                        <th>Website</th>
                        <th>Açıklama</th>
                        <th><PERSON>r<PERSON><PERSON></th>
                        <th>Eklenme Tarihi</th>
                        <th>İşlemler</th>
                    </tr>
                </thead>
                <tbody>
                    {% for supplier in suppliers %}
                    <tr>
                        <td>{{ supplier.id }}</td>
                        <td>
                            <strong>{{ supplier.name }}</strong>
                        </td>
                        <td>
                            {% if supplier.website %}
                                <a href="{{ supplier.website }}" target="_blank" class="text-decoration-none">
                                    {{ supplier.website }}
                                    <i class="bi bi-box-arrow-up-right"></i>
                                </a>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">{{ supplier.description or '-' }}</small>
                        </td>
                        <td>
                            <span class="badge bg-primary">{{ supplier.product_count }} ürün</span>
                        </td>
                        <td>
                            <small>{{ supplier.created_at or '-' }}</small>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" 
                                    onclick="viewSupplierProducts({{ supplier.id }}, '{{ supplier.name }}')">
                                <i class="bi bi-box"></i> Ürünleri Gör
                            </button>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="7" class="text-center text-muted py-4">
                            <i class="bi bi-inbox display-1"></i><br>
                            Henüz tedarikçi bulunmuyor.
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Supplier Products Modal -->
<div class="modal fade" id="supplierProductsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="supplierModalTitle">Tedarikçi Ürünleri</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="supplierProductsList">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Yükleniyor...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
function viewSupplierProducts(supplierId, supplierName) {
    $('#supplierModalTitle').text(supplierName + ' Ürünleri');
    $('#supplierProductsModal').modal('show');
    
    // Load products for this supplier
    $.get(`/api/suppliers/${supplierId}/products`, function(data) {
        let html = '';
        if (data.products && data.products.length > 0) {
            html = '<div class="list-group">';
            data.products.forEach(product => {
                html += `
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">${product.product_name}</h6>
                                <small class="text-muted">SKU: ${product.sku}</small>
                                ${product.supplier_price ? `<br><strong>₺${product.supplier_price}</strong>` : ''}
                            </div>
                            <div>
                                <a href="${product.supplier_product_url}" target="_blank" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-link-45deg"></i> Ürüne Git
                                </a>
                                <a href="{{ url_for('products.product_detail', id=0) }}".replace('0', product.product_id) 
                                   class="btn btn-sm btn-outline-secondary">
                                    <i class="bi bi-eye"></i> Detay
                                </a>
                            </div>
                        </div>
                    </div>
                `;
            });
            html += '</div>';
        } else {
            html = '<p class="text-center text-muted">Bu tedarikçiden ürün bulunmuyor.</p>';
        }
        $('#supplierProductsList').html(html);
    }).fail(function() {
        $('#supplierProductsList').html('<p class="text-center text-danger">Ürünler yüklenirken hata oluştu.</p>');
    });
}
</script>
{% endblock %}