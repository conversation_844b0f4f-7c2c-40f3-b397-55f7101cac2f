{% extends "base.html" %}

{% block title %}Marketplaces - PazarYeri{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-shop"></i> Marketplaces</h1>
    <span class="badge bg-info">{{ sites|length }} Marketplace</span>
</div>

<div class="row">
    {% for site in sites %}
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">{{ site.name }}</h6>
                <div>
                    {% if site.has_credentials %}
                        <span class="badge bg-success" title="API bilgileri mevcut">
                            <i class="bi bi-check-circle"></i>
                        </span>
                    {% else %}
                        <span class="badge bg-warning" title="API bilgileri eksik">
                            <i class="bi bi-exclamation-triangle"></i>
                        </span>
                    {% endif %}
                    
                    {% if site.is_active %}
                        <span class="badge bg-success">Aktif</span>
                    {% else %}
                        <span class="badge bg-secondary">Pasif</span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <p class="text-muted small mb-2">{{ site.base_url }}</p>
                
                <div class="row text-center">
                    <div class="col-6">
                        <span class="badge bg-primary">{{ site.marketplace_type }}</span>
                    </div>
                    <div class="col-6">
                        <span class="badge bg-info">{{ site.country_code }}</span>
                    </div>
                </div>
                
                <div class="mt-3">
                    <small class="text-muted">Rate Limit: {{ site.rate_limit }}/{{ site.rate_limit_window }}s</small>
                </div>
                
                <!-- Additional Costs -->
                {% if site.additional_shipping_cost > 0 or site.additional_commission_rate > 0 %}
                <div class="mt-2 border-top pt-2">
                    {% if site.additional_shipping_cost > 0 %}
                    <small class="d-block">
                        <i class="bi bi-truck"></i> Ek Kargo: <strong>{{ "%.2f"|format(site.additional_shipping_cost) }} {{ currency_symbol }}</strong>
                    </small>
                    {% endif %}
                    {% if site.additional_commission_rate > 0 %}
                    <small class="d-block">
                        <i class="bi bi-percent"></i> Ek Komisyon: <strong>{{ "%.2f"|format(site.additional_commission_rate) }}%</strong>
                    </small>
                    {% endif %}
                </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <div class="btn-group w-100" role="group">
                    <button type="button" 
                            class="btn btn-outline-primary btn-sm edit-api-btn" 
                            data-bs-toggle="modal" 
                            data-bs-target="#apiModal"
                            data-site-id="{{ site.id }}"
                            data-site-name="{{ site.name }}"
                            data-client-id="{{ site.client_id or '' }}"
                            data-api-key="{{ site.api_key or '' }}"
                            data-api-secret="{{ site.api_secret or '' }}"
                            data-shipping-cost="{{ site.additional_shipping_cost or '0' }}"
                            data-commission-rate="{{ site.additional_commission_rate or '0' }}">
                        <i class="bi bi-gear"></i>
                    </button>
                    <button type="button" 
                            class="btn btn-outline-{% if site.is_active %}warning{% else %}success{% endif %} btn-sm toggle-status-btn"
                            data-site-id="{{ site.id }}">
                        <i class="bi bi-{% if site.is_active %}pause{% else %}play{% endif %}"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- API Düzenleme Modal -->
<div class="modal fade" id="apiModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">API Bilgilerini Düzenle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="apiForm">
                <div class="modal-body">
                    <input type="hidden" id="siteId">
                    
                    <div class="mb-3">
                        <label for="clientId" class="form-label">Client ID</label>
                        <input type="text" class="form-control" id="clientId" name="client_id">
                    </div>
                    
                    <div class="mb-3">
                        <label for="apiKey" class="form-label">API Key</label>
                        <input type="text" class="form-control" id="apiKey" name="api_key">
                    </div>
                    
                    <div class="mb-3">
                        <label for="apiSecret" class="form-label">API Secret</label>
                        <input type="password" class="form-control" id="apiSecret" name="api_secret">
                    </div>
                    
                    <hr>
                    <h6>Ek Maliyetler</h6>
                    
                    <div class="mb-3">
                        <label for="additionalShippingCost" class="form-label">
                            <i class="bi bi-truck"></i> Ek Kargo Maliyeti ({{ currency_symbol }})
                        </label>
                        <input type="number" class="form-control" id="additionalShippingCost" 
                               name="additional_shipping_cost" step="0.01" min="0">
                        <div class="form-text">Bu marketplace için sabit ek kargo ücreti</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="additionalCommissionRate" class="form-label">
                            <i class="bi bi-percent"></i> Ek Komisyon Oranı (%)
                        </label>
                        <input type="number" class="form-control" id="additionalCommissionRate" 
                               name="additional_commission_rate" step="0.01" min="0" max="100">
                        <div class="form-text">Normal komisyona ek olarak uygulanacak komisyon oranı</div>
                    </div>
                    
                    <div class="alert alert-info">
                        <small>
                            <i class="bi bi-info-circle"></i>
                            Bu bilgiler güvenli bir şekilde şifrelenerek saklanacaktır.
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-primary">Kaydet</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // API düzenleme modal
    $('.edit-api-btn').click(function() {
        const siteId = $(this).data('site-id');
        const siteName = $(this).data('site-name');
        const clientId = $(this).data('client-id');
        const apiKey = $(this).data('api-key');
        const apiSecret = $(this).data('api-secret');
        const shippingCost = $(this).data('shipping-cost');
        const commissionRate = $(this).data('commission-rate');
        
        $('#siteId').val(siteId);
        $('#clientId').val(clientId);
        $('#apiKey').val(apiKey);
        $('#apiSecret').val(apiSecret);
        $('#additionalShippingCost').val(shippingCost);
        $('#additionalCommissionRate').val(commissionRate);
        $('.modal-title').text(`${siteName} - API Bilgileri`);
    });
    
    // API form gönderimi
    $('#apiForm').submit(function(e) {
        e.preventDefault();
        
        const siteId = $('#siteId').val();
        const formData = {
            'client_id': $('#clientId').val(),
            'api_key': $('#apiKey').val(),
            'api_secret': $('#apiSecret').val()
        };
        
        console.log('Form gönderiliyor:', siteId);
        console.log('Form verileri:', formData);
        
        $.ajax({
            url: `/marketplaces/${siteId}/update`,
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                console.log('API Başarılı:', response);
                if (response.status === 'success') {
                    // Now update costs
                    const costData = {
                        'additional_shipping_cost': $('#additionalShippingCost').val(),
                        'additional_commission_rate': $('#additionalCommissionRate').val()
                    };
                    
                    $.ajax({
                        url: `/marketplaces/${siteId}/update-costs`,
                        type: 'POST',
                        data: costData,
                        dataType: 'json',
                        success: function(costResponse) {
                            console.log('Maliyet Başarılı:', costResponse);
                            $('#apiModal').modal('hide');
                            alert('Marketplace bilgileri başarıyla güncellendi!');
                            setTimeout(() => location.reload(), 500);
                        },
                        error: function(xhr, status, error) {
                            console.error('Maliyet hatası:', error);
                            alert('Maliyetler güncellenirken hata oluştu: ' + error);
                        }
                    });
                } else {
                    alert('API güncelleme başarısız: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Hata:', xhr.responseText);
                try {
                    const response = JSON.parse(xhr.responseText);
                    alert('Hata oluştu: ' + (response.message || error));
                } catch(e) {
                    alert('Hata oluştu: ' + error);
                }
            }
        });
    });
    
    // Durum değiştirme
    $('.toggle-status-btn').click(function() {
        const siteId = $(this).data('site-id');
        const button = $(this);
        
        $.post(`/marketplaces/${siteId}/toggle`)
            .done(function(response) {
                location.reload();
            })
            .fail(function() {
                alert('Hata oluştu!');
            });
    });
});
</script>
{% endblock %}