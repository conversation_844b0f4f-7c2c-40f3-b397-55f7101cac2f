{% extends "base.html" %}

{% block title %}<PERSON><PERSON><PERSON><PERSON>eleri <PERSON> PazarYeri{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-arrow-left-right"></i> <PERSON><PERSON><PERSON><PERSON></h1>
    <span class="badge bg-info fs-6">{{ mappings | length }} eşleştirme</span>
</div>

<!-- <PERSON>r<PERSON><PERSON> -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-10">
                <label for="product_id" class="form-label"><PERSON>rel <PERSON><PERSON></label>
                <select class="form-select" id="product_id" name="product_id">
                    <option value="">Tüm Eşleştirmeler</option>
                    {% for product in local_products %}
                    <option value="{{ product.id }}" 
                            data-sku="{{ product.sku }}"
                            data-brand="{{ product.brand or '' }}"
                            data-search="{{ product.product_name }} {{ product.sku }} {{ product.brand or '' }}"
                            {% if selected_product_id and selected_product_id|int == product.id %}selected{% endif %}>
                        {{ product.product_name }} ({{ product.sku }}){% if product.brand %} - {{ product.brand }}{% endif %}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="button" class="btn btn-success w-100" data-bs-toggle="modal" data-bs-target="#createMappingModal">
                    <i class="bi bi-plus-circle"></i> Yeni Eşleştirme
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Mevcut Eşleştirmeler -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-link-45deg"></i> Mevcut Eşleştirmeler</h5>
    </div>
    <div class="card-body">
        {% if mappings %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Yerel Ürün</th>
                            <th>Marketplace Ürün</th>
                            <th>Marketplace</th>
                            <th>Güven Skoru</th>
                            <th>Tip</th>
                            <th>Oluşturulma</th>
                            <th>Notlar</th>
                            <th>İşlemler</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for mapping in mappings %}
                        <tr>
                            <td>
                                <strong>{{ mapping.local_product_name }}</strong><br>
                                <small class="text-muted">SKU: {{ mapping.local_product_sku }}</small>
                            </td>
                            <td>
                                <strong>{{ mapping.marketplace_product_title[:40] }}...</strong><br>
                                <small class="text-muted">ID: {{ mapping.external_product_id }}</small>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ mapping.marketplace_name }}</span>
                            </td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar" 
                                         style="width: {{ (mapping.mapping_confidence * 100) | round(0) }}%">
                                        {{ (mapping.mapping_confidence * 100) | round(0) }}%
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if mapping.mapping_type == 'manual' %}
                                    <span class="badge bg-warning">Manuel</span>
                                {% elif mapping.mapping_type == 'auto' %}
                                    <span class="badge bg-success">Otomatik</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ mapping.mapping_type }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <small>{{ mapping.created_at.split(' ')[0] if mapping.created_at else '-' }}</small>
                            </td>
                            <td>
                                {% if mapping.notes %}
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-info"
                                            data-bs-toggle="tooltip"
                                            title="{{ mapping.notes }}">
                                        <i class="bi bi-sticky"></i>
                                    </button>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button type="button" class="btn btn-outline-primary" title="Düzenle">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" title="Sil">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center text-muted py-4">
                <i class="bi bi-link-45deg display-1"></i><br>
                <h5>Eşleştirme bulunamadı</h5>
                <p>{% if selected_product_id %}Bu ürün için henüz eşleştirme yapılmamış.{% else %}Henüz hiç eşleştirme yapılmamış.{% endif %}</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Önerilen Eşleştirmeler -->
{% if suggestions and selected_product_id %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-lightbulb"></i> Önerilen Eşleştirmeler</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Marketplace Ürün</th>
                        <th>Marketplace</th>
                        <th>Fiyat</th>
                        <th>Benzerlik</th>
                        <th>İşlem</th>
                    </tr>
                </thead>
                <tbody>
                    {% for suggestion in suggestions %}
                    <tr>
                        <td>
                            <strong>{{ suggestion.title[:50] }}...</strong><br>
                            <small class="text-muted">{{ suggestion.brand }} - {{ suggestion.category }}</small>
                        </td>
                        <td>
                            <span class="badge bg-primary">{{ suggestion.marketplace_name }}</span>
                        </td>
                        <td class="text-end">
                            <strong>{{ suggestion.price }} {{ suggestion.currency }}</strong>
                        </td>
                        <td>
                            <div class="progress" style="height: 15px;">
                                <div class="progress-bar bg-success" 
                                     style="width: {{ (suggestion.similarity_score * 100) | round(0) }}%">
                                    {{ (suggestion.similarity_score * 100) | round(0) }}%
                                </div>
                            </div>
                        </td>
                        <td>
                            <button type="button" 
                                    class="btn btn-sm btn-success create-mapping-btn"
                                    data-local-id="{{ selected_product_id }}"
                                    data-marketplace-id="{{ suggestion.id }}"
                                    data-title="{{ suggestion.title }}">
                                <i class="bi bi-plus-circle"></i> Eşleştir
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}

<!-- Yeni Eşleştirme Modal -->
<div class="modal fade" id="createMappingModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Yeni Eşleştirme Oluştur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createMappingForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modalLocalProduct" class="form-label">Yerel Ürün</label>
                                <select class="form-select" id="modalLocalProduct" name="local_product_id" required 
                                        data-placeholder="Ürün adı, SKU veya barkod ile arayın...">
                                    <option value=""></option>
                                    {% for product in local_products %}
                                    <option value="{{ product.id }}"
                                            data-sku="{{ product.sku }}"
                                            data-brand="{{ product.brand or '' }}"
                                            data-barcode="{{ product.barcode or '' }}"
                                            data-search="{{ product.product_name }} {{ product.sku }} {{ product.brand or '' }} {{ product.barcode or '' }}"
                                            {% if selected_product_id and selected_product_id|int == product.id %}selected{% endif %}>
                                        {{ product.product_name }} ({{ product.sku }}){% if product.brand %} - {{ product.brand }}{% endif %}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">Ürün adı, SKU veya marka ile arama yapabilirsiniz</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="modalMarketplaceProduct" class="form-label">Marketplace Ürün</label>
                                <select class="form-select" id="modalMarketplaceProduct" name="marketplace_product_id" required
                                        data-placeholder="Marketplace ürün seçin...">
                                    <option value=""></option>
                                    {% for product in marketplace_products %}
                                    <option value="{{ product.id }}"
                                            data-title="{{ product.title }}"
                                            data-brand="{{ product.brand or '' }}"
                                            data-category="{{ product.category or '' }}"
                                            data-marketplace="{{ product.marketplace_name }}"
                                            data-price="{{ product.price }}"
                                            data-currency="{{ product.currency }}"
                                            data-search="{{ product.title }} {{ product.brand or '' }} {{ product.category or '' }} {{ product.marketplace_name }}">
                                        [{{ product.marketplace_name }}] {{ product.title[:50] }}{% if product.title|length > 50 %}...{% endif %}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">Ürün adı, marka veya marketplace ile arama yapabilirsiniz</div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="mappingNotes" class="form-label">Notlar</label>
                        <textarea class="form-control" id="mappingNotes" name="notes" rows="3"
                                  placeholder="Bu eşleştirme hakkında notlar..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-success">Eşleştirme Oluştur</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Tooltip'leri etkinleştir
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize Select2 for main product filter
    $('#product_id').select2({
        theme: 'bootstrap-5',
        width: '100%',
        allowClear: true,
        placeholder: 'Tüm Eşleştirmeler',
        language: {
            searching: function() { return null; }, // Disable "searching" message
            noResults: function() { return "Sonuç bulunamadı"; }
        },
        matcher: function(params, data) {
            // If there are no search terms, return all data
            if ($.trim(params.term) === '') {
                return data;
            }

            // Get the search data
            var searchData = $(data.element).data('search');
            if (!searchData) {
                searchData = data.text;
            }

            // Perform case-insensitive search
            if (searchData.toLowerCase().indexOf(params.term.toLowerCase()) > -1) {
                return data;
            }

            return null;
        }
    }).on('change', function() {
        // Submit form when selection changes
        $(this).closest('form').submit();
    });
    
    // Initialize Select2 for product selection in modal
    $('#modalLocalProduct').select2({
        theme: 'bootstrap-5',
        dropdownParent: $('#createMappingModal'),
        width: '100%',
        allowClear: true,
        placeholder: 'Ürün adı, SKU veya barkod ile arayın...',
        language: {
            searching: function() { return null; }, // Disable "searching" message
            noResults: function() { return "Sonuç bulunamadı"; }
        },
        minimumInputLength: 0,
        matcher: function(params, data) {
            // If there are no search terms, return all data
            if ($.trim(params.term) === '') {
                return data;
            }

            // Get the search data
            var searchData = $(data.element).data('search');
            if (!searchData) {
                searchData = data.text;
            }

            // Perform case-insensitive search
            if (searchData.toLowerCase().indexOf(params.term.toLowerCase()) > -1) {
                return data;
            }

            return null;
        },
        templateResult: function(data) {
            if (!data.id) {
                return data.text;
            }
            
            var $result = $('<span>');
            var $line1 = $('<div class="fw-bold">' + data.text + '</div>');
            
            var details = [];
            if ($(data.element).data('brand')) {
                details.push('Marka: ' + $(data.element).data('brand'));
            }
            if ($(data.element).data('barcode')) {
                details.push('Barkod: ' + $(data.element).data('barcode'));
            }
            
            if (details.length > 0) {
                var $line2 = $('<div class="text-muted small">' + details.join(' | ') + '</div>');
                $result.append($line1).append($line2);
            } else {
                $result.append($line1);
            }
            
            return $result;
        },
        escapeMarkup: function(markup) {
            return markup;
        }
    });
    
    // Initialize Select2 for marketplace product selection in modal
    $('#modalMarketplaceProduct').select2({
        theme: 'bootstrap-5',
        dropdownParent: $('#createMappingModal'),
        width: '100%',
        allowClear: true,
        placeholder: 'Marketplace ürün seçin...',
        language: {
            searching: function() { return null; }, // Disable "searching" message
            noResults: function() { return "Sonuç bulunamadı"; }
        },
        minimumInputLength: 0,
        matcher: function(params, data) {
            // If there are no search terms, return all data
            if ($.trim(params.term) === '') {
                return data;
            }

            // Get the search data
            var searchData = $(data.element).data('search');
            if (!searchData) {
                searchData = data.text;
            }

            // Perform case-insensitive search
            if (searchData.toLowerCase().indexOf(params.term.toLowerCase()) > -1) {
                return data;
            }

            return null;
        },
        templateResult: function(data) {
            if (!data.id) {
                return data.text;
            }
            
            var $result = $('<span>');
            var marketplace = $(data.element).data('marketplace');
            var title = $(data.element).data('title');
            var brand = $(data.element).data('brand');
            var price = $(data.element).data('price');
            var currency = $(data.element).data('currency');
            
            var $line1 = $('<div class="fw-bold">[' + marketplace + '] ' + title.substring(0, 50) + (title.length > 50 ? '...' : '') + '</div>');
            
            var details = [];
            if (brand) {
                details.push('Marka: ' + brand);
            }
            if (price && currency) {
                details.push('Fiyat: ' + price + ' ' + currency);
            }
            
            if (details.length > 0) {
                var $line2 = $('<div class="text-muted small">' + details.join(' | ') + '</div>');
                $result.append($line1).append($line2);
            } else {
                $result.append($line1);
            }
            
            return $result;
        },
        escapeMarkup: function(markup) {
            return markup;
        }
    });
    
    // Reset Select2 when modal is closed
    $('#createMappingModal').on('hidden.bs.modal', function () {
        $('#modalLocalProduct').val(null).trigger('change');
        $('#modalMarketplaceProduct').val(null).trigger('change');
    });
    
    // Manuel eşleştirme formu
    $('#createMappingForm').submit(function(e) {
        e.preventDefault();
        
        const button = $(this).find('button[type="submit"]');
        const originalText = button.html();
        
        button.prop('disabled', true);
        button.html('<i class="bi bi-hourglass-split"></i> Oluşturuluyor...');
        
        $.ajax({
            url: '/create-mapping',
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                if (response.status === 'success') {
                    $('#createMappingModal').modal('hide');
                    alert('Eşleştirme başarıyla oluşturuldu!');
                    location.reload();
                } else {
                    alert('Hata: ' + response.message);
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                alert('Hata: ' + (response ? response.message : 'Bilinmeyen hata'));
            },
            complete: function() {
                button.prop('disabled', false);
                button.html(originalText);
            }
        });
    });
    
    // Hızlı eşleştirme butonları
    $('.create-mapping-btn').click(function() {
        const localId = $(this).data('local-id');
        const marketplaceId = $(this).data('marketplace-id');
        const title = $(this).data('title');
        
        if (confirm(`"${title}" ürününü eşleştirmek istediğinizden emin misiniz?`)) {
            const button = $(this);
            const originalText = button.html();
            
            button.prop('disabled', true);
            button.html('<i class="bi bi-hourglass-split"></i>');
            
            $.ajax({
                url: '/create-mapping',
                type: 'POST',
                data: {
                    local_product_id: localId,
                    marketplace_product_id: marketplaceId,
                    notes: 'Otomatik öneri ile oluşturuldu'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        alert('Eşleştirme başarıyla oluşturuldu!');
                        location.reload();
                    } else {
                        alert('Hata: ' + response.message);
                        button.prop('disabled', false);
                        button.html(originalText);
                    }
                },
                error: function(xhr) {
                    const response = xhr.responseJSON;
                    alert('Hata: ' + (response ? response.message : 'Bilinmeyen hata'));
                    button.prop('disabled', false);
                    button.html(originalText);
                }
            });
        }
    });
});
</script>
{% endblock %}