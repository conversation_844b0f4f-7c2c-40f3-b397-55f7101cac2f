{% extends "base.html" %}

{% block title %}Sync Durumu - PazarYeri{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-arrow-repeat"></i> Sync Durumu</h1>
    <button type="button" class="btn btn-primary" onclick="location.reload()">
        <i class="bi bi-arrow-clockwise"></i> Ye<PERSON><PERSON>
    </button>
</div>

<!-- Marketplace İstatistikleri -->
<div class="row mb-4">
    {% for count in product_counts %}
    <div class="col-md-3 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <h5 class="card-title">{{ count.marketplace_name }}</h5>
                <h2 class="text-primary">{{ count.product_count }}</h2>
                <p class="card-text">
                    <small class="text-muted">
                        {{ count.recent_updates }} güncelleme (24 saat)
                    </small>
                </p>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Sync Logları -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-list-ul"></i> Son Sync İşlemleri</h5>
    </div>
    <div class="card-body">
        {% if status_logs %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Marketplace</th>
                            <th>Tip</th>
                            <th>Durum</th>
                            <th>İşlenen</th>
                            <th>Başarılı</th>
                            <th>Başarısız</th>
                            <th>Başlangıç</th>
                            <th>Bitiş</th>
                            <th>Süre</th>
                            <th>Hata</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in status_logs %}
                        <tr>
                            <td>
                                <span class="badge bg-primary">{{ log.marketplace_name }}</span>
                            </td>
                            <td>
                                <small>{{ log.sync_type }}</small>
                            </td>
                            <td>
                                {% if log.status == 'completed' %}
                                    <span class="badge bg-success">
                                        <i class="bi bi-check-circle"></i> Tamamlandı
                                    </span>
                                {% elif log.status == 'running' %}
                                    <span class="badge bg-warning">
                                        <i class="bi bi-hourglass-split"></i> Çalışıyor
                                    </span>
                                {% elif log.status == 'failed' %}
                                    <span class="badge bg-danger">
                                        <i class="bi bi-x-circle"></i> Başarısız
                                    </span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ log.status }}</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <span class="badge bg-info">{{ log.records_processed or 0 }}</span>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-success">{{ log.records_success or 0 }}</span>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-danger">{{ log.records_failed or 0 }}</span>
                            </td>
                            <td>
                                <small>{{ log.started_at.split(' ')[1][:5] if log.started_at else '-' }}</small><br>
                                <small class="text-muted">{{ log.started_at.split(' ')[0] if log.started_at else '-' }}</small>
                            </td>
                            <td>
                                <small>{{ log.completed_at.split(' ')[1][:5] if log.completed_at else '-' }}</small><br>
                                <small class="text-muted">{{ log.completed_at.split(' ')[0] if log.completed_at else '-' }}</small>
                            </td>
                            <td>
                                {% if log.started_at and log.completed_at %}
                                    {% set start_time = log.started_at | datetime %}
                                    {% set end_time = log.completed_at | datetime %}
                                    <small>
                                        {{ ((end_time - start_time).total_seconds()) | round(1) }}s
                                    </small>
                                {% else %}
                                    <small>-</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if log.error_message %}
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-danger"
                                            data-bs-toggle="tooltip"
                                            title="{{ log.error_message }}">
                                        <i class="bi bi-exclamation-triangle"></i>
                                    </button>
                                {% else %}
                                    <small class="text-muted">-</small>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center text-muted py-4">
                <i class="bi bi-inbox display-1"></i><br>
                <h5>Henüz sync işlemi yapılmadı</h5>
                <p>İlk sync işlemini başlatmak için Marketplace Ürünler sayfasından sync butonunu kullanın.</p>
                <a href="{{ url_for('marketplace.marketplace_products') }}" class="btn btn-primary">
                    <i class="bi bi-arrow-repeat"></i> Sync Başlat
                </a>
            </div>
        {% endif %}
    </div>
</div>

{% if status_logs %}
<!-- Özet İstatistikler -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <h6 class="card-title">Toplam İşlem</h6>
                <h4 class="text-info">{{ status_logs | length }}</h4>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <h6 class="card-title">Başarılı</h6>
                <h4 class="text-success">
                    {{ status_logs | selectattr('status', 'equalto', 'completed') | list | length }}
                </h4>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <h6 class="card-title">Başarısız</h6>
                <h4 class="text-danger">
                    {{ status_logs | selectattr('status', 'equalto', 'failed') | list | length }}
                </h4>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="card-body text-center">
                <h6 class="card-title">Çalışan</h6>
                <h4 class="text-warning">
                    {{ status_logs | selectattr('status', 'equalto', 'running') | list | length }}
                </h4>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Tooltip'leri etkinleştir
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Auto refresh her 30 saniyede bir
    setTimeout(() => {
        location.reload();
    }, 30000);
});
</script>
{% endblock %}