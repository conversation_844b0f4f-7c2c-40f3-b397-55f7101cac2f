{% extends "base.html" %}

{% block title %}Sipariş Analitikleri{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1>Sipariş Analitikleri</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('orders.orders') }}">Si<PERSON>iş<PERSON></a></li>
                            <li class="breadcrumb-item active">Analitik</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ url_for('orders.orders') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Siparişlere Dön
                    </a>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Toplam Sipariş</h6>
                                    <h3>{{ stats.total_orders }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-bag-check display-4"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Toplam Gelir</h6>
                                    <h3>${{ "%.2f"|format(stats.total_revenue) }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-currency-dollar display-4"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Son 7 Gün</h6>
                                    <h3>{{ stats.recent_orders }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-calendar-week display-4"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">Ortalama Değer</h6>
                                    <h3>${{ "%.2f"|format(stats.total_revenue / stats.total_orders if stats.total_orders > 0 else 0) }}</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-graph-up display-4"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Order Status Chart -->
                <div class="col-lg-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Sipariş Durumu Dağılımı</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="statusChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Marketplace Distribution -->
                <div class="col-lg-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Marketplace Dağılımı</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="marketplaceChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Recent Orders Trend -->
                <div class="col-12">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Son 30 Gün Sipariş Trendi</h5>
                        </div>
                        <div class="card-body">
                            <canvas id="trendChart" width="400" height="100"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Status Details -->
                <div class="col-lg-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Durum Detayları</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Durum</th>
                                            <th>Adet</th>
                                            <th>Yüzde</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% set status_names = {
                                            'pending': 'Beklemede',
                                            'confirmed': 'Onaylandı',
                                            'processing': 'İşlemde',
                                            'shipped': 'Kargoda',
                                            'delivered': 'Teslim Edildi',
                                            'cancelled': 'İptal'
                                        } %}
                                        {% for status, count in stats.by_status.items() %}
                                        <tr>
                                            <td>{{ status_names.get(status, status) }}</td>
                                            <td><strong>{{ count }}</strong></td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    {{ "%.1f"|format((count / stats.total_orders * 100) if stats.total_orders > 0 else 0) }}%
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Marketplace Details -->
                <div class="col-lg-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Marketplace Detayları</h5>
                        </div>
                        <div class="card-body">
                            {% if stats.by_marketplace %}
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Marketplace</th>
                                            <th>Sipariş</th>
                                            <th>Yüzde</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for marketplace, count in stats.by_marketplace.items() %}
                                        <tr>
                                            <td>{{ marketplace }}</td>
                                            <td><strong>{{ count }}</strong></td>
                                            <td>
                                                <span class="badge bg-info">
                                                    {{ "%.1f"|format((count / stats.total_orders * 100) if stats.total_orders > 0 else 0) }}%
                                                </span>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <p class="text-muted mb-0">Henüz marketplace verisi yok.</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Status Chart
const statusCtx = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: [
            {% for status in stats.by_status.keys() %}
            '{{ status }}',
            {% endfor %}
        ],
        datasets: [{
            data: [
                {% for count in stats.by_status.values() %}
                {{ count }},
                {% endfor %}
            ],
            backgroundColor: [
                '#ffc107', // pending - warning
                '#17a2b8', // confirmed - info  
                '#007bff', // processing - primary
                '#fd7e14', // shipped - warning
                '#28a745', // delivered - success
                '#dc3545'  // cancelled - danger
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Marketplace Chart
const marketplaceCtx = document.getElementById('marketplaceChart').getContext('2d');
const marketplaceChart = new Chart(marketplaceCtx, {
    type: 'pie',
    data: {
        labels: [
            {% for marketplace in stats.by_marketplace.keys() %}
            '{{ marketplace }}',
            {% endfor %}
        ],
        datasets: [{
            data: [
                {% for count in stats.by_marketplace.values() %}
                {{ count }},
                {% endfor %}
            ],
            backgroundColor: [
                '#007bff',
                '#28a745', 
                '#ffc107',
                '#dc3545',
                '#17a2b8',
                '#6f42c1'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Trend Chart
const trendCtx = document.getElementById('trendChart').getContext('2d');
const trendChart = new Chart(trendCtx, {
    type: 'line',
    data: {
        labels: [
            {% for order in recent_orders %}
            '{{ order.date }}',
            {% endfor %}
        ],
        datasets: [{
            label: 'Sipariş Sayısı',
            data: [
                {% for order in recent_orders %}
                {{ order.count }},
                {% endfor %}
            ],
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.4,
            fill: true
        }, {
            label: 'Gelir ($)',
            data: [
                {% for order in recent_orders %}
                {{ order.revenue or 0 }},
                {% endfor %}
            ],
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            fill: false,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        scales: {
            x: {
                display: true,
                title: {
                    display: true,
                    text: 'Tarih'
                }
            },
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'Sipariş Sayısı'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'Gelir ($)'
                },
                grid: {
                    drawOnChartArea: false,
                },
            }
        },
        plugins: {
            legend: {
                position: 'top'
            }
        }
    }
});
</script>
{% endblock %}