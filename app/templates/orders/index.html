{% extends "base.html" %}

{% block title %}<PERSON><PERSON><PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><PERSON><PERSON><PERSON><PERSON><PERSON></h1>
                <div>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#syncModal">
                        <i class="bi bi-arrow-clockwise"></i> Senkronize Et
                    </button>
                    <a href="{{ url_for('orders.analytics') }}" class="btn btn-info">
                        <i class="bi bi-graph-up"></i> Analitik
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <h6 class="card-title">Toplam Sipariş</h6>
                            <h3>{{ stats.total_orders }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <h6 class="card-title">Teslim Edildi</h6>
                            <h3>{{ stats.by_status.get('delivered', 0) }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <h6 class="card-title">İşlemde</h6>
                            <h3>{{ stats.by_status.get('processing', 0) + stats.by_status.get('confirmed', 0) }}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <h6 class="card-title">Son 7 Gün</h6>
                            <h3>{{ stats.recent_orders }}</h3>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-2">
                            <label for="marketplace_id" class="form-label">Marketplace</label>
                            <select name="marketplace_id" id="marketplace_id" class="form-select">
                                <option value="">Tümü</option>
                                {% for marketplace in marketplaces %}
                                <option value="{{ marketplace.id }}" 
                                    {% if filters.marketplace_id == marketplace.id %}selected{% endif %}>
                                    {{ marketplace.name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Durum</label>
                            <select name="status" id="status" class="form-select">
                                <option value="">Tümü</option>
                                <option value="pending" {% if filters.status == 'pending' %}selected{% endif %}>Beklemede</option>
                                <option value="confirmed" {% if filters.status == 'confirmed' %}selected{% endif %}>Onaylandı</option>
                                <option value="processing" {% if filters.status == 'processing' %}selected{% endif %}>İşlemde</option>
                                <option value="shipped" {% if filters.status == 'shipped' %}selected{% endif %}>Kargoda</option>
                                <option value="delivered" {% if filters.status == 'delivered' %}selected{% endif %}>Teslim Edildi</option>
                                <option value="cancelled" {% if filters.status == 'cancelled' %}selected{% endif %}>İptal</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="customer_search" class="form-label">Müşteri Ara</label>
                            <input type="text" name="customer_search" id="customer_search" 
                                   class="form-control" value="{{ filters.customer_search }}" 
                                   placeholder="İsim veya e-posta">
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">Başlangıç</label>
                            <input type="date" name="date_from" id="date_from" 
                                   class="form-control" value="{{ filters.date_from }}">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">Bitiş</label>
                            <input type="date" name="date_to" id="date_to" 
                                   class="form-control" value="{{ filters.date_to }}">
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="bi bi-search"></i> Filtrele
                            </button>
                            <a href="{{ url_for('orders.orders') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i>
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Orders Table -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Sipariş Listesi ({{ pagination.total_count }} toplam)</h5>
                    <div>
                        <button class="btn btn-sm btn-outline-primary" onclick="selectAllOrders()">
                            Tümünü Seç
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="bulkUpdateStatus('confirmed')">
                            Toplu Onayla
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% if orders %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="selectAll"></th>
                                    <th>Sipariş No</th>
                                    <th>Marketplace</th>
                                    <th>Müşteri</th>
                                    <th>Tutar</th>
                                    <th>Durum</th>
                                    <th>Sipariş Tarihi</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="order-checkbox" value="{{ order.id }}">
                                    </td>
                                    <td>
                                        <a href="{{ url_for('orders.order_detail', order_id=order.id) }}" 
                                           class="text-decoration-none">
                                            {{ order.marketplace_order_id }}
                                        </a>
                                        {% if order.item_count > 0 %}
                                        <span class="badge bg-secondary">{{ order.item_count }} ürün</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ order.marketplace_name }}</span>
                                    </td>
                                    <td>
                                        <div>{{ order.customer_name or 'Bilinmiyor' }}</div>
                                        {% if order.customer_email %}
                                        <small class="text-muted">{{ order.customer_email }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if order.total_amount %}
                                        <strong>{{ "%.2f"|format(order.total_amount) }} {{ order.currency or 'USD' }}</strong>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% set status_class = {
                                            'pending': 'warning',
                                            'confirmed': 'info',
                                            'processing': 'primary',
                                            'shipped': 'warning',
                                            'delivered': 'success',
                                            'cancelled': 'danger'
                                        } %}
                                        {% set status_text = {
                                            'pending': 'Beklemede',
                                            'confirmed': 'Onaylandı',
                                            'processing': 'İşlemde',
                                            'shipped': 'Kargoda',
                                            'delivered': 'Teslim Edildi',
                                            'cancelled': 'İptal'
                                        } %}
                                        <span class="badge bg-{{ status_class.get(order.order_status, 'secondary') }}">
                                            {{ status_text.get(order.order_status, order.order_status) }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if order.order_date %}
                                        {{ order.order_date[:10] }}
                                        {% else %}
                                        {{ order.created_at[:10] }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('orders.order_detail', order_id=order.id) }}" 
                                               class="btn btn-outline-primary" title="Detay">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <button class="btn btn-outline-secondary dropdown-toggle" 
                                                    data-bs-toggle="dropdown">
                                                <i class="bi bi-gear"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><button class="dropdown-item" onclick="updateOrderStatus({{ order.id }}, 'confirmed')">Onayla</button></li>
                                                <li><button class="dropdown-item" onclick="updateOrderStatus({{ order.id }}, 'processing')">İşleme Al</button></li>
                                                <li><button class="dropdown-item" onclick="updateOrderStatus({{ order.id }}, 'shipped')">Kargoya Ver</button></li>
                                                <li><button class="dropdown-item" onclick="updateOrderStatus({{ order.id }}, 'delivered')">Teslim Et</button></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><button class="dropdown-item text-danger" onclick="updateOrderStatus({{ order.id }}, 'cancelled')">İptal Et</button></li>
                                            </ul>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if pagination.total_pages > 1 %}
                    <nav>
                        <ul class="pagination justify-content-center">
                            {% if pagination.page > 1 %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ pagination.page - 1 }}&{{ request.query_string.decode() | replace('page=' + pagination.page|string, '') }}">Önceki</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in range(1, pagination.total_pages + 1) %}
                                {% if page_num <= 3 or page_num > pagination.total_pages - 3 or (page_num >= pagination.page - 1 and page_num <= pagination.page + 1) %}
                                <li class="page-item {% if page_num == pagination.page %}active{% endif %}">
                                    <a class="page-link" href="?page={{ page_num }}&{{ request.query_string.decode() | replace('page=' + pagination.page|string, '') }}">{{ page_num }}</a>
                                </li>
                                {% elif page_num == 4 and pagination.page > 5 %}
                                <li class="page-item disabled"><span class="page-link">...</span></li>
                                {% elif page_num == pagination.total_pages - 3 and pagination.page < pagination.total_pages - 4 %}
                                <li class="page-item disabled"><span class="page-link">...</span></li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.page < pagination.total_pages %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ pagination.page + 1 }}&{{ request.query_string.decode() | replace('page=' + pagination.page|string, '') }}">Sonraki</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-inbox display-1 text-muted"></i>
                        <h4 class="text-muted">Sipariş bulunamadı</h4>
                        <p class="text-muted">Henüz hiç sipariş yok veya filtrelere uygun sipariş bulunamadı.</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#syncModal">
                            Marketplace'lerden Senkronize Et
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sync Modal -->
<div class="modal fade" id="syncModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Siparişleri Senkronize Et</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('orders.sync_orders') }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="marketplace" class="form-label">Marketplace</label>
                        <select name="marketplace" id="marketplace" class="form-select">
                            <option value="all">Tüm Marketplace'ler</option>
                            {% for marketplace in marketplaces %}
                            <option value="{{ marketplace.name }}">{{ marketplace.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="days_back" class="form-label">Kaç Gün Geriye Git</label>
                        <select name="days_back" id="days_back" class="form-select">
                            <option value="1">Son 1 Gün</option>
                            <option value="3">Son 3 Gün</option>
                            <option value="7" selected>Son 7 Gün</option>
                            <option value="14">Son 14 Gün</option>
                            <option value="30">Son 30 Gün</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                    <button type="submit" class="btn btn-primary">Senkronize Et</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Select all orders functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.order-checkbox');
    checkboxes.forEach(cb => cb.checked = this.checked);
});

function selectAllOrders() {
    const checkboxes = document.querySelectorAll('.order-checkbox');
    const allChecked = Array.from(checkboxes).every(cb => cb.checked);
    checkboxes.forEach(cb => cb.checked = !allChecked);
    document.getElementById('selectAll').checked = !allChecked;
}

// Update single order status
function updateOrderStatus(orderId, newStatus) {
    if (!confirm('Sipariş durumunu değiştirmek istediğinizden emin misiniz?')) {
        return;
    }
    
    fetch('/orders/api/update-status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            order_id: orderId,
            status: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Hata: ' + data.error);
        }
    })
    .catch(error => {
        alert('Bir hata oluştu: ' + error.message);
    });
}

// Bulk update orders
function bulkUpdateStatus(newStatus) {
    const selectedOrders = Array.from(document.querySelectorAll('.order-checkbox:checked'))
                                .map(cb => parseInt(cb.value));
    
    if (selectedOrders.length === 0) {
        alert('Lütfen en az bir sipariş seçin.');
        return;
    }
    
    if (!confirm(`${selectedOrders.length} siparişin durumunu değiştirmek istediğinizden emin misiniz?`)) {
        return;
    }
    
    fetch('/orders/api/bulk-update', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            order_ids: selectedOrders,
            status: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Hata: ' + data.error);
        }
    })
    .catch(error => {
        alert('Bir hata oluştu: ' + error.message);
    });
}
</script>
{% endblock %}