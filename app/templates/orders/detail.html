{% extends "base.html" %}

{% block title %}Sipariş Detayı - {{ order.marketplace_order_id }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1>Sipariş Detayı</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="{{ url_for('orders.orders') }}"><PERSON><PERSON><PERSON><PERSON><PERSON></a></li>
                            <li class="breadcrumb-item active">{{ order.marketplace_order_id }}</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="{{ url_for('orders.orders') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> <PERSON><PERSON>
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Order Info -->
                <div class="col-lg-8">
                    <!-- Order Summary -->
                    <div class="card mb-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Sipariş Bilgileri</h5>
                            <div class="dropdown">
                                <button class="btn btn-outline-primary dropdown-toggle" type="button" 
                                        data-bs-toggle="dropdown">
                                    Durum Değiştir
                                </button>
                                <ul class="dropdown-menu">
                                    <li><button class="dropdown-item" onclick="updateStatus('confirmed')">Onayla</button></li>
                                    <li><button class="dropdown-item" onclick="updateStatus('processing')">İşleme Al</button></li>
                                    <li><button class="dropdown-item" onclick="updateStatus('shipped')">Kargoya Ver</button></li>
                                    <li><button class="dropdown-item" onclick="updateStatus('delivered')">Teslim Et</button></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><button class="dropdown-item text-danger" onclick="updateStatus('cancelled')">İptal Et</button></li>
                                </ul>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Sipariş No:</strong></td>
                                            <td>{{ order.marketplace_order_id }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Marketplace:</strong></td>
                                            <td><span class="badge bg-info">{{ order.marketplace_name }}</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Durum:</strong></td>
                                            <td>
                                                {% set status_class = {
                                                    'pending': 'warning',
                                                    'confirmed': 'info',
                                                    'processing': 'primary',
                                                    'shipped': 'warning',
                                                    'delivered': 'success',
                                                    'cancelled': 'danger'
                                                } %}
                                                {% set status_text = {
                                                    'pending': 'Beklemede',
                                                    'confirmed': 'Onaylandı',
                                                    'processing': 'İşlemde',
                                                    'shipped': 'Kargoda',
                                                    'delivered': 'Teslim Edildi',
                                                    'cancelled': 'İptal'
                                                } %}
                                                <span class="badge bg-{{ status_class.get(order.order_status, 'secondary') }}">
                                                    {{ status_text.get(order.order_status, order.order_status) }}
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Ödeme Durumu:</strong></td>
                                            <td>
                                                {% if order.payment_status == 'paid' %}
                                                <span class="badge bg-success">Ödendi</span>
                                                {% elif order.payment_status == 'pending' %}
                                                <span class="badge bg-warning">Beklemede</span>
                                                {% else %}
                                                <span class="badge bg-danger">{{ order.payment_status }}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Sipariş Tarihi:</strong></td>
                                            <td>{{ order.order_date[:19] if order.order_date else order.created_at[:19] }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Toplam Tutar:</strong></td>
                                            <td>
                                                {% if order.total_amount %}
                                                <strong>{{ "%.2f"|format(order.total_amount) }} {{ order.currency or 'USD' }}</strong>
                                                {% else %}
                                                <span class="text-muted">Belirtilmemiş</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% if order.tracking_number %}
                                        <tr>
                                            <td><strong>Takip No:</strong></td>
                                            <td><code>{{ order.tracking_number }}</code></td>
                                        </tr>
                                        {% endif %}
                                        {% if order.shipping_method %}
                                        <tr>
                                            <td><strong>Kargo Yöntemi:</strong></td>
                                            <td>{{ order.shipping_method }}</td>
                                        </tr>
                                        {% endif %}
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Info -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Müşteri Bilgileri</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>İletişim</h6>
                                    <p class="mb-1"><strong>Ad:</strong> {{ order.customer_name or 'Belirtilmemiş' }}</p>
                                    {% if order.customer_email %}
                                    <p class="mb-1"><strong>E-posta:</strong> 
                                        <a href="mailto:{{ order.customer_email }}">{{ order.customer_email }}</a>
                                    </p>
                                    {% endif %}
                                    {% if order.customer_phone %}
                                    <p class="mb-1"><strong>Telefon:</strong> 
                                        <a href="tel:{{ order.customer_phone }}">{{ order.customer_phone }}</a>
                                    </p>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    {% if order.shipping_address %}
                                    <h6>Teslimat Adresi</h6>
                                    {% set address = order.shipping_address | safe %}
                                    {% if address.startswith('{') %}
                                        {% set addr_data = address | fromjson %}
                                        <address class="mb-0">
                                            {% if addr_data.address_tail %}{{ addr_data.address_tail }}<br>{% endif %}
                                            {% if addr_data.city %}{{ addr_data.city }}{% endif %}
                                            {% if addr_data.district %}, {{ addr_data.district }}{% endif %}<br>
                                            {% if addr_data.country %}{{ addr_data.country }}{% endif %}
                                            {% if addr_data.zip_code %} {{ addr_data.zip_code }}{% endif %}
                                        </address>
                                    {% else %}
                                        <address class="mb-0">{{ address }}</address>
                                    {% endif %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Order Items with Financial Data -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Sipariş Kalemleri ({{ items|length }})</h5>
                        </div>
                        <div class="card-body">
                            {% if items %}
                            {% if order.marketplace_data %}
                                {% set marketplace_data = order.marketplace_data | fromjson %}
                                {% set financial_data = marketplace_data.get('financial_data', {}) %}
                                {% set financial_products = financial_data.get('products', []) %}
                            {% else %}
                                {% set financial_products = [] %}
                            {% endif %}
                            
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Ürün</th>
                                            <th>SKU</th>
                                            <th>Adet</th>
                                            <th>Liste Fiyatı</th>
                                            <th>Satış Fiyatı</th>
                                            <th>İndirim</th>
                                            <th>Komisyon</th>
                                            <th>Net Ödeme</th>
                                            <th>Durum</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in items %}
                                        {% set fin_product = None %}
                                        {% for fp in financial_products %}
                                            {% if fp.sku|string == item.sku|string or fp.product_id|string == item.sku|string %}
                                                {% set fin_product = fp %}
                                            {% endif %}
                                        {% endfor %}
                                        <tr>
                                            <td>
                                                <div>
                                                    <strong>{{ item.product_name }}</strong>
                                                    {% if item.local_product_name and item.local_product_name != item.product_name %}
                                                    <br><small class="text-muted">Yerel: {{ item.local_product_name }}</small>
                                                    {% endif %}
                                                </div>
                                            </td>
                                            <td>
                                                {% if item.sku %}
                                                <code>{{ item.sku }}</code>
                                                {% else %}
                                                <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ item.quantity }}</td>
                                            <td>
                                                {% if fin_product and fin_product.old_price %}
                                                    {{ "%.2f"|format(fin_product.old_price) }}
                                                {% else %}
                                                    {{ "%.2f"|format(item.unit_price) if item.unit_price else '-' }}
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if fin_product %}
                                                    <strong>{{ "%.2f"|format(fin_product.price) }}</strong>
                                                {% else %}
                                                    <strong>{{ "%.2f"|format(item.unit_price) if item.unit_price else '-' }}</strong>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if fin_product and fin_product.total_discount_value > 0 %}
                                                    <span class="text-danger">
                                                        -{{ "%.2f"|format(fin_product.total_discount_value) }}
                                                        <small>({{ "%.1f"|format(fin_product.total_discount_percent) }}%)</small>
                                                    </span>
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if fin_product %}
                                                    {% if fin_product.commission_amount > 0 %}
                                                        <span class="text-warning">
                                                            {{ "%.2f"|format(fin_product.commission_amount) }}
                                                            <small>({{ "%.1f"|format(fin_product.commission_percent) }}%)</small>
                                                        </span>
                                                    {% else %}
                                                        <span class="text-muted">-</span>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if fin_product %}
                                                    <strong class="text-success">{{ "%.2f"|format(fin_product.payout) }}</strong>
                                                {% else %}
                                                    <strong>{{ "%.2f"|format(item.total_price) if item.total_price else '-' }}</strong>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if item.product_id %}
                                                <span class="badge bg-success">Eşleştirildi</span>
                                                {% else %}
                                                <span class="badge bg-warning">Eşleştirme Gerekli</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <p class="text-muted mb-0">Bu siparişe ait ürün bulunmuyor.</p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Financial Summary -->
                    {% if order.marketplace_data %}
                        {% set marketplace_data = order.marketplace_data | fromjson %}
                        {% set financial_data = marketplace_data.get('financial_data', {}) %}
                        {% if financial_data and financial_data.get('products') %}
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="mb-0">Finansal Özet</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        {% set total_old_price = 0 %}
                                        {% set total_discount = 0 %}
                                        {% set total_commission = 0 %}
                                        {% set total_payout = 0 %}
                                        {% set all_actions = [] %}
                                        
                                        {% for fp in financial_data.products %}
                                            {% set total_old_price = total_old_price + (fp.old_price * fp.quantity) %}
                                            {% set total_discount = total_discount + fp.total_discount_value %}
                                            {% set total_commission = total_commission + fp.commission_amount %}
                                            {% set total_payout = total_payout + fp.payout %}
                                            {% if fp.actions %}
                                                {% for action in fp.actions %}
                                                    {% if action not in all_actions %}
                                                        {% set _ = all_actions.append(action) %}
                                                    {% endif %}
                                                {% endfor %}
                                            {% endif %}
                                        {% endfor %}
                                        
                                        <table class="table table-sm">
                                            <tr>
                                                <td>Liste Fiyatı Toplamı:</td>
                                                <td class="text-end">{{ "%.2f"|format(total_old_price) }} {{ order.currency }}</td>
                                            </tr>
                                            <tr>
                                                <td>İndirimler:</td>
                                                <td class="text-end text-danger">-{{ "%.2f"|format(total_discount) }} {{ order.currency }}</td>
                                            </tr>
                                            <tr>
                                                <td>Satış Fiyatı:</td>
                                                <td class="text-end"><strong>{{ "%.2f"|format(total_old_price - total_discount) }} {{ order.currency }}</strong></td>
                                            </tr>
                                            <tr>
                                                <td>Komisyonlar:</td>
                                                <td class="text-end text-warning">-{{ "%.2f"|format(total_commission) }} {{ order.currency }}</td>
                                            </tr>
                                            <tr class="table-active">
                                                <td><strong>Net Ödeme:</strong></td>
                                                <td class="text-end"><strong class="text-success">{{ "%.2f"|format(total_payout) }} {{ order.currency }}</strong></td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        {% if all_actions %}
                                        <h6>Uygulanan İndirimler ve Promosyonlar</h6>
                                        <ul class="list-unstyled">
                                            {% for action in all_actions %}
                                            <li><i class="bi bi-tag-fill text-primary"></i> {{ action }}</li>
                                            {% endfor %}
                                        </ul>
                                        {% endif %}
                                        
                                        {% if financial_data.posting_services %}
                                        <h6 class="mt-3">Ek Hizmetler</h6>
                                        <p class="text-muted">{{ financial_data.posting_services }}</p>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    {% endif %}

                    <!-- Notes -->
                    {% if order.notes %}
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Notlar</h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-0">{{ order.notes }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- Sidebar -->
                <div class="col-lg-4">
                    <!-- Status History -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">Durum Geçmişi</h5>
                        </div>
                        <div class="card-body">
                            {% if history %}
                            <div class="timeline">
                                {% for entry in history %}
                                <div class="timeline-item">
                                    <div class="timeline-marker"></div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">
                                            {% if entry.old_status %}
                                            {{ entry.old_status }} → {{ entry.new_status }}
                                            {% else %}
                                            {{ entry.new_status }}
                                            {% endif %}
                                        </h6>
                                        <p class="text-muted mb-1">
                                            {{ entry.changed_by }} tarafından
                                            {% if entry.change_reason %}
                                            - {{ entry.change_reason }}
                                            {% endif %}
                                        </p>
                                        <small class="text-muted">{{ entry.created_at[:19] }}</small>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <p class="text-muted mb-0">Henüz durum değişikliği yok.</p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Hızlı İşlemler</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                {% if order.order_status == 'pending' %}
                                <button class="btn btn-success" onclick="updateStatus('confirmed')">
                                    <i class="bi bi-check-circle"></i> Siparişi Onayla
                                </button>
                                {% elif order.order_status == 'confirmed' %}
                                <button class="btn btn-primary" onclick="updateStatus('processing')">
                                    <i class="bi bi-gear"></i> İşleme Al
                                </button>
                                {% elif order.order_status == 'processing' %}
                                <button class="btn btn-warning" onclick="updateStatus('shipped')">
                                    <i class="bi bi-truck"></i> Kargoya Ver
                                </button>
                                {% elif order.order_status == 'shipped' %}
                                <button class="btn btn-success" onclick="updateStatus('delivered')">
                                    <i class="bi bi-check2-all"></i> Teslim Et
                                </button>
                                {% endif %}
                                
                                {% if order.order_status not in ['delivered', 'cancelled'] %}
                                <button class="btn btn-outline-danger" onclick="updateStatus('cancelled')">
                                    <i class="bi bi-x-circle"></i> İptal Et
                                </button>
                                {% endif %}
                                
                                <hr>
                                <button class="btn btn-outline-info" onclick="printOrder()">
                                    <i class="bi bi-printer"></i> Yazdır
                                </button>
                                <button class="btn btn-outline-secondary" onclick="exportOrder()">
                                    <i class="bi bi-download"></i> Export
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Sipariş Durumunu Güncelle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="statusForm">
                    <input type="hidden" id="newStatus" name="status">
                    <div class="mb-3">
                        <label for="notes" class="form-label">Not (Opsiyonel)</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="Durum değişikliği hakkında not ekleyin..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                <button type="button" class="btn btn-primary" onclick="confirmStatusUpdate()">Güncelle</button>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 10px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -25px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #007bff;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 5px;
}
</style>

<script>
function updateStatus(status) {
    document.getElementById('newStatus').value = status;
    new bootstrap.Modal(document.getElementById('statusModal')).show();
}

function confirmStatusUpdate() {
    const formData = new FormData(document.getElementById('statusForm'));
    const data = {
        order_id: {{ order.id }},
        status: formData.get('status'),
        notes: formData.get('notes')
    };
    
    fetch('/orders/api/update-status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Hata: ' + data.error);
        }
    })
    .catch(error => {
        alert('Bir hata oluştu: ' + error.message);
    });
}

function printOrder() {
    window.print();
}

function exportOrder() {
    // TODO: Implement order export functionality
    alert('Export özelliği yakında eklenecek.');
}
</script>
{% endblock %}