{% extends "base.html" %}

{% block title %}{{ product.product_name }} - PazarYeri{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-5">
        <!-- Product Images -->
        <div class="card">
            <div class="card-body">
                {% if images %}
                    <!-- Main Image Display -->
                    <div id="productCarousel" class="carousel slide mb-3" data-bs-ride="carousel">
                        <div class="carousel-inner">
                            {% for image in images %}
                            <div class="carousel-item {% if loop.first %}active{% endif %}">
                                <img src="{{ url_for('static', filename=image.image_path) }}" 
                                     class="d-block w-100" alt="{{ product.product_name }}">
                            </div>
                            {% endfor %}
                        </div>
                        {% if images|length > 1 %}
                        <button class="carousel-control-prev" type="button" data-bs-target="#productCarousel" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon"></span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#productCarousel" data-bs-slide="next">
                            <span class="carousel-control-next-icon"></span>
                        </button>
                        {% endif %}
                    </div>
                    
                    <!-- Thumbnails -->
                    {% if images|length > 1 %}
                    <div class="row g-2">
                        {% for image in images %}
                        <div class="col-3">
                            <img src="{{ url_for('static', filename=image.image_path) }}" 
                                 class="img-fluid rounded thumbnail-img" 
                                 onclick="changeCarouselSlide({{ loop.index0 }})"
                                 style="cursor: pointer; opacity: 0.7;"
                                 onmouseover="this.style.opacity='1'" 
                                 onmouseout="this.style.opacity='0.7'">
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-image display-1 text-muted"></i>
                        <p class="text-muted">Ürün resmi bulunmuyor</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-7">
        <!-- Product Details -->
        <div class="card mb-3">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">{{ product.product_name }}</h4>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>SKU:</strong></div>
                    <div class="col-sm-9"><code>{{ product.sku }}</code></div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Model:</strong></div>
                    <div class="col-sm-9">{{ product.model or '-' }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Marka:</strong></div>
                    <div class="col-sm-9">{{ product.brand or '-' }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Barkod:</strong></div>
                    <div class="col-sm-9">{{ product.barcode or '-' }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Desi:</strong></div>
                    <div class="col-sm-9">{{ product.desi or 0 }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Kar Marjı:</strong></div>
                    <div class="col-sm-9">{{ product.profit_margin or 0 }}%</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Komisyon Oranı:</strong></div>
                    <div class="col-sm-9">{{ product.commission_rate or 0 }}%</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>GTIP:</strong></div>
                    <div class="col-sm-9">{{ product.gtip or '-' }}</div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>Stok Çarpanı:</strong></div>
                    <div class="col-sm-9">{{ product.stock_multiplier or 1 }}</div>
                </div>
                
                <hr>
                
                <div class="d-flex gap-2">
                    <a href="{{ url_for('products.products') }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Geri Dön
                    </a>
                    <a href="/product/{{ product.id }}/edit" class="btn btn-warning">
                        <i class="bi bi-pencil"></i> Düzenle
                    </a>
                    <button type="button" class="btn btn-success" onclick="findSuppliers({{ product.id }})">
                        <i class="bi bi-search"></i> Tedarikçi Bul
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Suppliers -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-truck"></i> Tedarikçiler</h5>
                <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addSupplierModal">
                    <i class="bi bi-plus"></i> Tedarikçi Ekle
                </button>
            </div>
            <div class="card-body">
                {% if suppliers %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Tedarikçi</th>
                                    <th>Ürün Linki</th>
                                    <th>Fiyat</th>
                                    <th>Son Kontrol</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for supplier in suppliers %}
                                <tr>
                                    <td>
                                        <strong>{{ supplier.name }}</strong>
                                        {% if supplier.website %}
                                            <br><small class="text-muted">
                                                <a href="{{ supplier.website }}" target="_blank" class="text-decoration-none">
                                                    {{ supplier.website }}
                                                </a>
                                            </small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if supplier.supplier_product_url %}
                                            <a href="{{ supplier.supplier_product_url }}" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-link-45deg"></i> Ürüne Git
                                            </a>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if supplier.supplier_price %}
                                            <strong>₺{{ "%.2f"|format(supplier.supplier_price) }}</strong>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ supplier.last_checked or '-' }}</small>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteProductSupplier({{ product.id }}, {{ supplier.id }})">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4 text-muted">
                        <i class="bi bi-truck display-1"></i><br>
                        Henüz tedarikçi eklenmemiş.<br>
                        <button type="button" class="btn btn-sm btn-success mt-2" onclick="findSuppliers({{ product.id }})">
                            <i class="bi bi-search"></i> Tedarikçi Ara
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Add Supplier Modal -->
<div class="modal fade" id="addSupplierModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bu Ürün İçin Tedarikçi Ekle</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addSupplierForm">
                    <div class="mb-3">
                        <label class="form-label">Tedarikçi Seç</label>
                        <select class="form-select" id="modal_supplier_id" required>
                            <option value="">Tedarikçi seçin...</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Ürün Linki</label>
                        <input type="url" class="form-control" id="modal_supplier_url" 
                               placeholder="https://..." required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Fiyat (₺)</label>
                        <input type="number" step="0.01" class="form-control" id="modal_supplier_price" 
                               placeholder="0.00">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">İptal</button>
                <button type="button" class="btn btn-primary" onclick="addSupplierToProduct()">Ekle</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function changeCarouselSlide(index) {
    const carousel = new bootstrap.Carousel(document.getElementById('productCarousel'));
    carousel.to(index);
}

function findSuppliers(productId) {
    $.post(`/product/${productId}/find-suppliers`)
        .done(function(response) {
            alert(response.message);
            location.reload();
        })
        .fail(function() {
            alert('Tedarikçi arama sırasında hata oluştu!');
        });
}

function deleteProductSupplier(productId, supplierId) {
    if (confirm('Bu ürün-tedarikçi ilişkisini silmek istediğinizden emin misiniz?')) {
        $.ajax({
            url: `/api/product-supplier/delete/${productId}/${supplierId}`,
            type: 'DELETE',
            success: function(response) {
                if (response.status === 'success') {
                    location.reload();
                } else {
                    alert('Hata: ' + response.message);
                }
            },
            error: function() {
                alert('Tedarikçi silinirken hata oluştu!');
            }
        });
    }
}

function loadSuppliers() {
    $.get('/api/suppliers', function(data) {
        const select = $('#modal_supplier_id');
        select.empty().append('<option value="">Tedarikçi seçin...</option>');
        
        if (data.suppliers) {
            data.suppliers.forEach(supplier => {
                select.append(`<option value="${supplier.id}">${supplier.name}</option>`);
            });
        }
    });
}

function addSupplierToProduct() {
    const productId = {{ product.id }};
    const supplierId = $('#modal_supplier_id').val();
    const supplierUrl = $('#modal_supplier_url').val();
    const supplierPrice = $('#modal_supplier_price').val();
    
    if (!supplierId || !supplierUrl) {
        alert('Lütfen tedarikçi ve ürün linki girin!');
        return;
    }
    
    $.post('/api/product-supplier/add', {
        product_id: productId,
        supplier_id: supplierId,
        supplier_product_url: supplierUrl,
        supplier_price: supplierPrice || null
    })
    .done(function(response) {
        if (response.status === 'success') {
            $('#addSupplierModal').modal('hide');
            location.reload();
        } else {
            alert('Hata: ' + response.message);
        }
    })
    .fail(function() {
        alert('Tedarikçi eklenirken hata oluştu!');
    });
}

// Load suppliers when modal is shown
$('#addSupplierModal').on('show.bs.modal', function() {
    loadSuppliers();
});
</script>
{% endblock %}