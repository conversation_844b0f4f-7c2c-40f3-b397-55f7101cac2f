from flask import Blueprint, render_template
from app.database import get_db_connection
from app.services.product_sync_service import ProductSyncService

bp = Blueprint('main', __name__)

@bp.route('/')
def index():
    conn = get_db_connection()
    sync_service = ProductSyncService()
    
    # İstatistikler
    products_count = conn.execute('SELECT COUNT(*) FROM products').fetchone()[0]
    suppliers_count = conn.execute('SELECT COUNT(*) FROM suppliers').fetchone()[0]
    marketplaces_count = conn.execute('SELECT COUNT(*) FROM marketplaces WHERE is_active = 1 AND is_deleted = 0').fetchone()[0]
    matches_count = conn.execute('SELECT COUNT(*) FROM product_suppliers WHERE is_deleted = 0').fetchone()[0]
    
    # Yeni marketplace istatistikleri
    marketplace_products_count = conn.execute('SELECT COUNT(*) FROM marketplace_products').fetchone()[0]
    product_mappings_count = conn.execute('SELECT COUNT(*) FROM product_mappings WHERE mapping_status = "active"').fetchone()[0]
    
    # Son sync durumu
    recent_syncs = sync_service.get_sync_status()[:3]  # Son 3 sync
    
    # Marketplace ürün sayıları
    marketplace_counts = sync_service.get_marketplace_product_counts()
    
    conn.close()
    
    return render_template('index.html', 
                         products_count=products_count,
                         suppliers_count=suppliers_count,
                         marketplaces_count=marketplaces_count,
                         matches_count=matches_count,
                         marketplace_products_count=marketplace_products_count,
                         product_mappings_count=product_mappings_count,
                         marketplace_counts=marketplace_counts,
                         recent_syncs=recent_syncs)

@bp.route('/suppliers')
def suppliers():
    conn = get_db_connection()
    # Get all suppliers with their product counts (excluding soft deleted)
    suppliers_data = conn.execute('''
        SELECT s.*, COUNT(DISTINCT CASE WHEN ps.is_deleted = 0 THEN ps.product_id END) as product_count
        FROM suppliers s
        LEFT JOIN product_suppliers ps ON s.id = ps.supplier_id
        GROUP BY s.id
        ORDER BY s.name
    ''').fetchall()
    conn.close()
    
    return render_template('suppliers.html', suppliers=suppliers_data)

@bp.route('/admin/deleted-relationships')
def deleted_relationships():
    """Admin page for managing soft-deleted product-supplier relationships"""
    from app.utils.soft_delete import get_deleted_product_suppliers
    
    deleted_relationships = get_deleted_product_suppliers()
    
    return render_template('admin/deleted_relationships.html', 
                         deleted_relationships=deleted_relationships)