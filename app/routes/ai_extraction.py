from flask import Blueprint, request, jsonify
from app.services.ai_product_extractor import AIProductExtractor
import logging

# Configure logging
logger = logging.getLogger(__name__)

bp = Blueprint('ai_extraction', __name__, url_prefix='/api/ai')

@bp.route('/extract-product-data', methods=['POST'])
def extract_product_data():
    """
    Extract product data from URL using AI
    """
    try:
        # Get request data
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'JSON veri gerekli'
            }), 400
        
        url = data.get('url', '').strip()
        ai_provider_id = data.get('ai_provider_id')
        
        # Validate input
        if not url:
            return jsonify({
                'success': False,
                'error': 'URL gerekli'
            }), 400
        
        if not ai_provider_id:
            return jsonify({
                'success': False,
                'error': 'AI provider seçimi gerekli'
            }), 400
        
        try:
            ai_provider_id = int(ai_provider_id)
        except (ValueError, TypeError):
            return jsonify({
                'success': False,
                'error': 'Geçersiz AI provider ID'
            }), 400
        
        # Validate URL format
        if not url.startswith(('http://', 'https://')):
            if not url.startswith('www.'):
                url = 'https://' + url
            else:
                url = 'https://' + url
        
        logger.info(f"Starting AI extraction for URL: {url} with provider: {ai_provider_id}")
        
        # Extract data using AI
        extractor = AIProductExtractor()
        result = extractor.extract_product_data(url, ai_provider_id)
        
        if result['success']:
            logger.info("AI extraction successful")
            return jsonify(result), 200
        else:
            logger.warning(f"AI extraction failed: {result.get('error', 'Unknown error')}")
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"Error in extract_product_data endpoint: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Sunucu hatası: {str(e)}'
        }), 500

@bp.route('/providers/active', methods=['GET'])
def get_active_providers():
    """
    Get list of active AI providers
    """
    try:
        extractor = AIProductExtractor()
        providers = extractor.get_active_ai_providers()
        
        return jsonify({
            'success': True,
            'providers': providers
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting active providers: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Provider listesi alınamadı: {str(e)}'
        }), 500

@bp.route('/test', methods=['POST'])
def test_ai_connection():
    """
    Test AI provider connection
    """
    try:
        data = request.get_json()
        ai_provider_id = data.get('ai_provider_id')
        
        if not ai_provider_id:
            return jsonify({
                'success': False,
                'error': 'AI provider ID gerekli'
            }), 400
        
        try:
            ai_provider_id = int(ai_provider_id)
        except (ValueError, TypeError):
            return jsonify({
                'success': False,
                'error': 'Geçersiz AI provider ID'
            }), 400
        
        # Test with a simple extraction
        extractor = AIProductExtractor()
        test_content = "Test Product\nPrice: $29.99\nBrand: TestBrand"
        
        result = extractor._send_to_ai(test_content, ai_provider_id)
        
        if result:
            return jsonify({
                'success': True,
                'message': 'AI provider bağlantısı başarılı',
                'test_response': result[:100] + "..." if len(result) > 100 else result
            }), 200
        else:
            return jsonify({
                'success': False,
                'error': 'AI provider bağlantısı başarısız'
            }), 400
            
    except Exception as e:
        logger.error(f"Error testing AI connection: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Test hatası: {str(e)}'
        }), 500