from flask import Blueprint, render_template, request, redirect, url_for, flash
from app.database import get_db_connection

bp = Blueprint('settings', __name__, url_prefix='/admin')

def get_settings():
    """Get all settings from database"""
    conn = get_db_connection()
    settings = conn.execute('SELECT key, value, description FROM settings').fetchall()
    conn.close()
    
    # Convert to dictionary for easier access in template
    settings_dict = {}
    for setting in settings:
        settings_dict[setting['key']] = {
            'value': setting['value'],
            'description': setting['description']
        }
    
    return settings_dict

def update_setting(key, value):
    """Update a specific setting"""
    conn = get_db_connection()
    conn.execute('''
        UPDATE settings 
        SET value = ?, updated_at = CURRENT_TIMESTAMP 
        WHERE key = ?
    ''', (value, key))
    conn.commit()
    conn.close()

@bp.route('/settings')
def index():
    """Display settings page"""
    settings = get_settings()
    return render_template('admin/settings.html', settings=settings)

@bp.route('/settings/update', methods=['POST'])
def update():
    """Update settings"""
    try:
        # Get form data
        default_profit_margin = request.form.get('default_profit_margin', '20')
        minimum_profit_margin = request.form.get('minimum_profit_margin', '10')
        product_name_prefix = request.form.get('product_name_prefix', '')
        default_currency = request.form.get('default_currency', 'TRY')
        unknown_stock_value_1 = request.form.get('unknown_stock_value_1', '5')
        unknown_stock_value_2 = request.form.get('unknown_stock_value_2', '10')
        
        # Validate numeric values
        try:
            float(default_profit_margin)
            float(minimum_profit_margin)
            stock_val_1 = int(unknown_stock_value_1)
            stock_val_2 = int(unknown_stock_value_2)
            
            # Validate stock range
            if stock_val_1 < 0 or stock_val_2 < 0:
                flash('Stok değerleri negatif olamaz!', 'error')
                return redirect(url_for('settings.index'))
            
            if stock_val_1 > stock_val_2:
                flash('Stok Değeri 1, Stok Değeri 2\'den büyük olamaz!', 'error')
                return redirect(url_for('settings.index'))
                
        except ValueError:
            flash('Sayısal değerler geçersiz!', 'error')
            return redirect(url_for('settings.index'))
        
        # Update settings
        update_setting('default_profit_margin', default_profit_margin)
        update_setting('minimum_profit_margin', minimum_profit_margin)
        update_setting('product_name_prefix', product_name_prefix)
        update_setting('default_currency', default_currency)
        update_setting('unknown_stock_value_1', unknown_stock_value_1)
        update_setting('unknown_stock_value_2', unknown_stock_value_2)
        
        flash('Ayarlar başarıyla güncellendi!', 'success')
        
    except Exception as e:
        flash(f'Ayarlar güncellenirken hata oluştu: {str(e)}', 'error')
    
    return redirect(url_for('settings.index'))

def get_setting_value(key, default=None):
    """Get a specific setting value (helper function for other modules)"""
    conn = get_db_connection()
    result = conn.execute('SELECT value FROM settings WHERE key = ?', (key,)).fetchone()
    conn.close()
    
    if result:
        return result['value']
    return default

def get_default_currency():
    """Get the default currency setting"""
    return get_setting_value('default_currency', 'TRY')

def get_currency_symbol(currency=None):
    """Get currency symbol for display"""
    if currency is None:
        currency = get_default_currency()
    
    symbols = {
        'TRY': '₺',
        'USD': '$',
        'EUR': '€',
        'RUB': '₽',
        'GBP': '£'
    }
    return symbols.get(currency, currency)

def format_currency(amount, currency=None):
    """Format amount with currency symbol"""
    if currency is None:
        currency = get_default_currency()
    
    symbol = get_currency_symbol(currency)
    
    # Format with thousand separators and 2 decimal places
    formatted = "{:,.2f}".format(float(amount))
    
    # Replace comma with dot for thousand separator (Turkish style)
    formatted = formatted.replace(',', 'X').replace('.', ',').replace('X', '.')
    
    return f"{formatted} {symbol}"