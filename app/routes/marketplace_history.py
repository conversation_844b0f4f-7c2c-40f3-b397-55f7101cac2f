from flask import Blueprint, render_template
from app.database import get_db_connection

bp = Blueprint('marketplace_history', __name__)

@bp.route('/marketplace-uploads')
def marketplace_uploads():
    """Marketplace upload geçmişi sayfası"""
    conn = get_db_connection()
    
    uploads = conn.execute('''
        SELECT ul.*, p.product_name, p.sku, m.name as marketplace_name
        FROM marketplace_upload_logs ul
        JOIN products p ON ul.product_id = p.id
        JOIN api_sites m ON ul.marketplace_id = m.id
        ORDER BY ul.created_at DESC
        LIMIT 100
    ''').fetchall()
    
    conn.close()
    
    return render_template('marketplace_uploads.html', uploads=uploads)