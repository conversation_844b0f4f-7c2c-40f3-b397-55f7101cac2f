from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from app.database import get_db_connection
import json
from datetime import datetime

bp = Blueprint('categories', __name__, url_prefix='/categories')

@bp.route('/')
def categories():
    """Kategorileri listele"""
    conn = get_db_connection()
    
    # Tüm kategorileri parent-child ilişkisiyle getir
    categories = conn.execute('''
        SELECT id, name, parent_id, created_at, updated_at
        FROM categories 
        WHERE is_deleted = 0 
        ORDER BY parent_id ASC, name ASC
    ''').fetchall()
    
    # Marketplace kategorilerinin sayısını getir
    marketplace_categories_count = conn.execute('''
        SELECT COUNT(*) FROM marketplace_categories WHERE is_deleted = 0
    ''').fetchone()[0]
    
    # <PERSON><PERSON><PERSON>ştirmelerinin sayısını getir
    mappings_count = conn.execute('''
        SELECT COUNT(*) FROM category_mappings WHERE is_deleted = 0
    ''').fetchone()[0]
    
    conn.close()
    
    return render_template('categories/index.html', 
                         categories=categories,
                         marketplace_categories_count=marketplace_categories_count,
                         mappings_count=mappings_count)

@bp.route('/add', methods=['GET', 'POST'])
def add_category():
    """Kategori ekle"""
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        parent_id = request.form.get('parent_id')
        
        if not name:
            flash('Kategori adı gerekli!', 'error')
            return redirect(url_for('categories.add_category'))
        
        # Parent ID'yi kontrol et
        if parent_id == '':
            parent_id = None
        else:
            parent_id = int(parent_id)
        
        conn = get_db_connection()
        
        # Aynı isimde kategori var mı kontrol et
        existing = conn.execute('''
            SELECT id FROM categories 
            WHERE name = ? AND parent_id IS ? AND is_deleted = 0
        ''', (name, parent_id)).fetchone()
        
        if existing:
            flash('Bu isimde bir kategori zaten mevcut!', 'error')
            conn.close()
            return redirect(url_for('categories.add_category'))
        
        # Kategoriyi ekle
        conn.execute('''
            INSERT INTO categories (name, parent_id, created_at, updated_at)
            VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ''', (name, parent_id))
        
        conn.commit()
        conn.close()
        
        flash('Kategori başarıyla eklendi!', 'success')
        return redirect(url_for('categories.categories'))
    
    # GET request - form göster
    conn = get_db_connection()
    parent_categories = conn.execute('''
        SELECT id, name FROM categories 
        WHERE is_deleted = 0 AND parent_id IS NULL
        ORDER BY name
    ''').fetchall()
    conn.close()
    
    return render_template('categories/form.html', 
                         title='Kategori Ekle',
                         parent_categories=parent_categories)

@bp.route('/edit/<int:id>', methods=['GET', 'POST'])
def edit_category(id):
    """Kategori düzenle"""
    conn = get_db_connection()
    
    # Kategoriyi getir
    category = conn.execute('''
        SELECT id, name, parent_id 
        FROM categories 
        WHERE id = ? AND is_deleted = 0
    ''', (id,)).fetchone()
    
    if not category:
        flash('Kategori bulunamadı!', 'error')
        conn.close()
        return redirect(url_for('categories.categories'))
    
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        parent_id = request.form.get('parent_id')
        
        if not name:
            flash('Kategori adı gerekli!', 'error')
            return redirect(url_for('categories.edit_category', id=id))
        
        # Parent ID'yi kontrol et
        if parent_id == '':
            parent_id = None
        else:
            parent_id = int(parent_id)
            # Kendisini parent olarak seçemez
            if parent_id == id:
                flash('Kategori kendisinin alt kategorisi olamaz!', 'error')
                return redirect(url_for('categories.edit_category', id=id))
        
        # Aynı isimde başka kategori var mı kontrol et
        existing = conn.execute('''
            SELECT id FROM categories 
            WHERE name = ? AND parent_id IS ? AND id != ? AND is_deleted = 0
        ''', (name, parent_id, id)).fetchone()
        
        if existing:
            flash('Bu isimde bir kategori zaten mevcut!', 'error')
            return redirect(url_for('categories.edit_category', id=id))
        
        # Kategoriyi güncelle
        conn.execute('''
            UPDATE categories 
            SET name = ?, parent_id = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (name, parent_id, id))
        
        conn.commit()
        conn.close()
        
        flash('Kategori başarıyla güncellendi!', 'success')
        return redirect(url_for('categories.categories'))
    
    # GET request - form göster
    parent_categories = conn.execute('''
        SELECT id, name FROM categories 
        WHERE is_deleted = 0 AND parent_id IS NULL AND id != ?
        ORDER BY name
    ''', (id,)).fetchall()
    
    conn.close()
    
    return render_template('categories/form.html',
                         title='Kategori Düzenle',
                         category=category,
                         parent_categories=parent_categories)

@bp.route('/delete/<int:id>', methods=['POST'])
def delete_category(id):
    """Kategoriyi soft delete yap"""
    conn = get_db_connection()
    
    # Kategoriyi kontrol et
    category = conn.execute('''
        SELECT id, name FROM categories 
        WHERE id = ? AND is_deleted = 0
    ''', (id,)).fetchone()
    
    if not category:
        return jsonify({'status': 'error', 'message': 'Kategori bulunamadı!'}), 404
    
    # Alt kategorileri kontrol et
    subcategories = conn.execute('''
        SELECT COUNT(*) FROM categories 
        WHERE parent_id = ? AND is_deleted = 0
    ''', (id,)).fetchone()[0]
    
    if subcategories > 0:
        conn.close()
        return jsonify({'status': 'error', 'message': 'Alt kategorileri olan kategori silinemez!'}), 400
    
    # Ürünlerde kullanılıyor mu kontrol et (eğer ürünlerde kategori alanı varsa)
    # Bu kontrol ürün yapısına göre güncellenebilir
    
    # Soft delete yap
    conn.execute('''
        UPDATE categories 
        SET is_deleted = 1, deleted_at = CURRENT_TIMESTAMP
        WHERE id = ?
    ''', (id,))
    
    conn.commit()
    conn.close()
    
    return jsonify({'status': 'success', 'message': 'Kategori başarıyla silindi!'})

@bp.route('/marketplace')
def marketplace_categories():
    """Marketplace kategorilerini listele"""
    conn = get_db_connection()
    
    # Marketplace'leri getir
    marketplaces = conn.execute('''
        SELECT id, name FROM marketplaces 
        WHERE is_active = 1 AND is_deleted = 0
        ORDER BY name
    ''').fetchall()
    
    # Seçilen marketplace'e göre kategorileri getir
    marketplace_id = request.args.get('marketplace_id', type=int)
    marketplace_categories = []
    
    if marketplace_id:
        marketplace_categories = conn.execute('''
            SELECT id, marketplace_category_id, name, parent_marketplace_category_id, 
                   level, is_leaf, created_at
            FROM marketplace_categories
            WHERE marketplace_id = ? AND is_deleted = 0
            ORDER BY level ASC, name ASC
        ''', (marketplace_id,)).fetchall()
    
    conn.close()
    
    return render_template('categories/marketplace.html',
                         marketplaces=marketplaces,
                         marketplace_categories=marketplace_categories,
                         selected_marketplace_id=marketplace_id)

@bp.route('/sync/<int:marketplace_id>', methods=['POST'])
def sync_marketplace_categories(marketplace_id):
    """Marketplace kategorilerini senkronize et"""
    try:
        conn = get_db_connection()
        
        # Marketplace adını al
        marketplace = conn.execute('''
            SELECT name FROM marketplaces 
            WHERE id = ? AND is_deleted = 0
        ''', (marketplace_id,)).fetchone()
        
        if not marketplace:
            flash('Marketplace bulunamadı!', 'error')
            return redirect(url_for('categories.marketplace_categories'))
        
        marketplace_name = marketplace['name']
        conn.close()
        
        # Marketplace'e göre API client oluştur
        if marketplace_name == 'Ozon':
            from app.services.marketplace.ozon_api_client import OzonAPIClient
            from app.services.category_service import MarketplaceCategoryService
            
            client = OzonAPIClient()
            category_service = MarketplaceCategoryService()
            
            # Kategorileri çek ve kaydet
            success = client.sync_categories_to_db()
            
            if success:
                flash('Ozon kategorileri başarıyla senkronize edildi!', 'success')
            else:
                flash('Kategori senkronizasyonu başarısız oldu!', 'error')
        else:
            flash(f'{marketplace_name} için kategori senkronizasyonu henüz desteklenmiyor.', 'warning')
        
        return redirect(url_for('categories.marketplace_categories', marketplace_id=marketplace_id))
    
    except Exception as e:
        flash(f'Senkronizasyon hatası: {str(e)}', 'error')
        return redirect(url_for('categories.marketplace_categories'))

@bp.route('/mappings')
def category_mappings():
    """Kategori eşleştirmelerini listele"""
    conn = get_db_connection()
    
    # Mevcut eşleştirmeleri getir
    mappings = conn.execute('''
        SELECT cm.id, c.name as category_name, m.name as marketplace_name,
               mc.name as marketplace_category_name, cm.created_at
        FROM category_mappings cm
        JOIN categories c ON cm.category_id = c.id
        JOIN marketplaces m ON cm.marketplace_id = m.id
        JOIN marketplace_categories mc ON cm.marketplace_category_id = mc.id
        WHERE cm.is_deleted = 0
        ORDER BY c.name, m.name
    ''').fetchall()
    
    conn.close()
    
    return render_template('categories/mappings.html', mappings=mappings)

@bp.route('/mappings/add', methods=['GET', 'POST'])
def add_mapping():
    """Kategori eşleştirmesi ekle"""
    if request.method == 'POST':
        category_id = request.form.get('category_id', type=int)
        marketplace_id = request.form.get('marketplace_id', type=int)
        marketplace_category_id = request.form.get('marketplace_category_id', type=int)
        
        if not all([category_id, marketplace_id, marketplace_category_id]):
            flash('Tüm alanlar gerekli!', 'error')
            return redirect(url_for('categories.add_mapping'))
        
        conn = get_db_connection()
        
        # Aynı eşleştirme var mı kontrol et
        existing = conn.execute('''
            SELECT id FROM category_mappings
            WHERE category_id = ? AND marketplace_id = ? AND is_deleted = 0
        ''', (category_id, marketplace_id)).fetchone()
        
        if existing:
            flash('Bu kategori için bu marketplace\'te zaten eşleştirme mevcut!', 'error')
            conn.close()
            return redirect(url_for('categories.add_mapping'))
        
        # Eşleştirmeyi ekle
        conn.execute('''
            INSERT INTO category_mappings (category_id, marketplace_id, marketplace_category_id, created_at)
            VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        ''', (category_id, marketplace_id, marketplace_category_id))
        
        conn.commit()
        conn.close()
        
        flash('Kategori eşleştirmesi başarıyla eklendi!', 'success')
        return redirect(url_for('categories.category_mappings'))
    
    # GET request - form göster
    conn = get_db_connection()
    
    categories = conn.execute('''
        SELECT id, name FROM categories 
        WHERE is_deleted = 0 
        ORDER BY name
    ''').fetchall()
    
    marketplaces = conn.execute('''
        SELECT id, name FROM marketplaces 
        WHERE is_active = 1 AND is_deleted = 0
        ORDER BY name
    ''').fetchall()
    
    conn.close()
    
    return render_template('categories/mapping_form.html',
                         categories=categories,
                         marketplaces=marketplaces)

@bp.route('/mappings/delete/<int:mapping_id>', methods=['POST'])
def delete_mapping(mapping_id):
    """Kategori eşleştirmesini sil"""
    conn = get_db_connection()
    
    # Eşleştirme var mı kontrol et
    mapping = conn.execute('''
        SELECT id FROM category_mappings
        WHERE id = ? AND is_deleted = 0
    ''', (mapping_id,)).fetchone()
    
    if not mapping:
        conn.close()
        return jsonify({'status': 'error', 'message': 'Eşleştirme bulunamadı!'}), 404
    
    # Soft delete yap
    conn.execute('''
        UPDATE category_mappings
        SET is_deleted = 1, deleted_at = CURRENT_TIMESTAMP
        WHERE id = ?
    ''', (mapping_id,))
    
    conn.commit()
    conn.close()
    
    return jsonify({'status': 'success', 'message': 'Eşleştirme başarıyla silindi!'})

@bp.route('/api/marketplace-categories/<int:marketplace_id>')
def api_marketplace_categories(marketplace_id):
    """AJAX için marketplace kategorilerini getir"""
    conn = get_db_connection()
    
    categories = conn.execute('''
        SELECT id, name, marketplace_category_id
        FROM marketplace_categories
        WHERE marketplace_id = ? AND is_deleted = 0
        ORDER BY name
    ''', (marketplace_id,)).fetchall()
    
    conn.close()
    
    categories_list = [dict(category) for category in categories]
    return jsonify({'status': 'success', 'categories': categories_list})