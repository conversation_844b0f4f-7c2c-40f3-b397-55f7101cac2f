from flask import Blueprint, render_template, request, jsonify
from app.database import get_db_connection
from app.services.product_sync_service import ProductSyncService

bp = Blueprint('marketplace', __name__)

@bp.route('/marketplace-products')
def marketplace_products():
    conn = get_db_connection()
    
    # Filtreler
    search_term = request.args.get('search', '')
    marketplace_filter = request.args.get('marketplace', '')
    page = int(request.args.get('page', 1))
    per_page = 50
    offset = (page - 1) * per_page
    
    # Marketplaces
    marketplaces = conn.execute('SELECT id, name FROM marketplaces WHERE is_active = 1 AND is_deleted = 0').fetchall()
    
    sync_service = ProductSyncService()
    products, total = sync_service.search_marketplace_products(
        search_term, marketplace_filter, per_page, offset
    )
    
    # Sayfalama
    total_pages = (total + per_page - 1) // per_page
    
    conn.close()
    
    return render_template('marketplace_products.html', 
                         products=products,
                         marketplaces=marketplaces,
                         search_term=search_term,
                         marketplace_filter=marketplace_filter,
                         page=page,
                         total_pages=total_pages,
                         total=total,
                         total_count=total,
                         per_page=per_page)

@bp.route('/marketplace-sync', methods=['POST'])
def marketplace_sync():
    marketplace_value = request.form.get('marketplace')  # Fixed: was 'marketplace_name'
    limit = int(request.form.get('limit', 100))
    
    sync_service = ProductSyncService()
    
    if marketplace_value == 'all':
        # Tüm marketplaceleri senkronize et
        result = sync_service.sync_all_marketplaces(limit_per_marketplace=limit)
    else:
        # marketplace_value is an ID, convert to name
        conn = get_db_connection()
        cursor = conn.execute('SELECT name FROM marketplaces WHERE id = ? AND is_deleted = 0', (marketplace_value,))
        marketplace_row = cursor.fetchone()
        conn.close()
        
        if not marketplace_row:
            return jsonify({'status': 'error', 'message': 'Marketplace bulunamadı'}), 400
        
        marketplace_name = marketplace_row[0]
        
        # Belirli bir marketplace'i senkronize et
        result = sync_service.sync_marketplace_by_name(marketplace_name, limit=limit)
    
    if result:
        return jsonify({'status': 'success', 'message': f'Senkronizasyon başlatıldı'})
    else:
        return jsonify({'status': 'error', 'message': 'Senkronizasyon başlatılamadı'}), 500

@bp.route('/sync-status')
def sync_status():
    sync_service = ProductSyncService()
    sync_logs = sync_service.get_sync_status()
    
    return render_template('sync_status.html', sync_logs=sync_logs)

@bp.route('/product-mappings')
def product_mappings():
    conn = get_db_connection()
    
    # Get selected product ID from query params
    selected_product_id = request.args.get('product_id', type=int)
    
    # Yerel ürünler
    local_products = conn.execute('SELECT * FROM products ORDER BY product_name').fetchall()
    
    # Marketplace ürünleri
    marketplace_products = conn.execute('''
        SELECT mp.*, m.name as marketplace_name 
        FROM marketplace_products mp
        JOIN marketplaces m ON mp.marketplace_id = m.id
        WHERE m.is_deleted = 0
        ORDER BY m.name, mp.title
    ''').fetchall()
    
    sync_service = ProductSyncService()
    
    # Get mappings and suggestions based on selection
    mappings = []
    suggestions = []
    
    if selected_product_id:
        # Get mappings for specific product
        mappings = sync_service.get_product_mappings(selected_product_id)
        suggestions = sync_service.get_product_mapping_suggestions(selected_product_id)
    else:
        # Get all mappings
        conn = get_db_connection()
        mappings = conn.execute('''
            SELECT 
                pm.*,
                p.product_name as local_product_name,
                p.sku as local_product_sku,
                mp.title as marketplace_product_title,
                mp.external_product_id,
                m.name as marketplace_name
            FROM product_mappings pm
            LEFT JOIN products p ON pm.local_product_id = p.id
            LEFT JOIN marketplace_products mp ON pm.marketplace_product_id = mp.id
            LEFT JOIN marketplaces m ON mp.marketplace_id = m.id
            WHERE pm.mapping_status = 'active'
            ORDER BY pm.created_at DESC
        ''').fetchall()
    
    conn.close()
    
    return render_template('product_mappings.html', 
                         local_products=local_products,
                         marketplace_products=marketplace_products,
                         mappings=mappings,
                         suggestions=suggestions,
                         selected_product_id=selected_product_id)

@bp.route('/create-mapping', methods=['POST'])
def create_mapping():
    try:
        local_product_id = int(request.form.get('local_product_id'))
        marketplace_product_id = int(request.form.get('marketplace_product_id'))
        mapping_type = request.form.get('mapping_type', 'manual')
        confidence = float(request.form.get('confidence', 0.0))
        notes = request.form.get('notes', '')
        
        # Validate that both products exist
        conn = get_db_connection()
        
        # Check local product
        local_product = conn.execute('SELECT id FROM products WHERE id = ?', (local_product_id,)).fetchone()
        if not local_product:
            conn.close()
            return jsonify({'status': 'error', 'message': 'Yerel ürün bulunamadı'}), 400
        
        # Check marketplace product
        marketplace_product = conn.execute('SELECT id FROM marketplace_products WHERE id = ?', (marketplace_product_id,)).fetchone()
        if not marketplace_product:
            conn.close()
            return jsonify({'status': 'error', 'message': 'Marketplace ürünü bulunamadı'}), 400
        
        conn.close()
        
        sync_service = ProductSyncService()
        mapping_id = sync_service.create_product_mapping(
            local_product_id,
            marketplace_product_id,
            mapping_type,
            confidence,
            notes
        )
        
        if mapping_id:
            return jsonify({'status': 'success', 'message': 'Eşleştirme oluşturuldu'})
        else:
            return jsonify({'status': 'error', 'message': 'Eşleştirme zaten mevcut'})
            
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500