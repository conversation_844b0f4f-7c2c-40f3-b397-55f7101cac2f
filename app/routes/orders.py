from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from app.services.order_management_service import OrderManagementService
from app.database import get_db_connection
from datetime import datetime, timedelta

bp = Blueprint('orders', __name__, url_prefix='/orders')

@bp.route('/')
def orders():
    """Main orders page with filtering"""
    # Get filter parameters
    marketplace_id = request.args.get('marketplace_id', type=int)
    status = request.args.get('status', '').strip()
    customer_search = request.args.get('customer_search', '').strip()
    date_from = request.args.get('date_from', '').strip()
    date_to = request.args.get('date_to', '').strip()
    page = int(request.args.get('page', 1))
    per_page = 25
    offset = (page - 1) * per_page
    
    # Convert date strings to proper format
    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d').isoformat()
        except ValueError:
            date_from = None
    
    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d').isoformat()
        except ValueError:
            date_to = None
    
    # Get orders
    order_service = OrderManagementService()
    result = order_service.get_orders_with_filters(
        marketplace_id=marketplace_id,
        status=status,
        customer_search=customer_search,
        date_from=date_from,
        date_to=date_to,
        limit=per_page,
        offset=offset
    )
    
    orders = result.get('orders', [])
    total_count = result.get('total_count', 0)
    total_pages = (total_count + per_page - 1) // per_page if total_count > 0 else 1
    
    # Get marketplaces for filter
    conn = get_db_connection()
    marketplaces = conn.execute('''
        SELECT id, name FROM api_sites 
        WHERE is_active = 1
        ORDER BY name
    ''').fetchall()
    conn.close()
    
    # Get order statistics
    stats = order_service.get_order_statistics()
    
    return render_template('orders/index.html',
                         orders=orders,
                         marketplaces=marketplaces,
                         stats=stats,
                         filters={
                             'marketplace_id': marketplace_id,
                             'status': status,
                             'customer_search': customer_search,
                             'date_from': request.args.get('date_from', ''),
                             'date_to': request.args.get('date_to', '')
                         },
                         pagination={
                             'page': page,
                             'per_page': per_page,
                             'total_pages': total_pages,
                             'total_count': total_count
                         })

@bp.route('/<int:order_id>')
def order_detail(order_id):
    """Order detail page"""
    order_service = OrderManagementService()
    order_data = order_service.get_order_by_id(order_id)
    
    if not order_data:
        flash('Sipariş bulunamadı', 'error')
        return redirect(url_for('orders.orders'))
    
    return render_template('orders/detail.html', 
                         order=order_data['order'],
                         items=order_data['items'],
                         history=order_data['history'])

@bp.route('/sync', methods=['POST'])
def sync_orders():
    """Sync orders from marketplaces"""
    try:
        marketplace = request.form.get('marketplace', 'all')
        days_back = int(request.form.get('days_back', 7))
        
        order_service = OrderManagementService()
        
        if marketplace == 'all':
            result = order_service.sync_marketplace_orders(days_back=days_back)
        else:
            result = order_service.sync_marketplace_orders(marketplace_name=marketplace, days_back=days_back)
        
        if isinstance(result, dict) and 'success' in result:
            if result['success']:
                flash(f'Siparişler başarıyla senkronize edildi', 'success')
            else:
                flash(f'Senkronizasyon hatası: {result.get("error", "Bilinmeyen hata")}', 'error')
        else:
            # Multiple marketplace results
            success_count = sum(1 for r in result.values() if isinstance(r, dict) and r.get('success'))
            total_count = len(result)
            flash(f'{success_count}/{total_count} marketplace başarıyla senkronize edildi', 'info')
        
        return redirect(url_for('orders.orders'))
        
    except Exception as e:
        flash(f'Senkronizasyon hatası: {str(e)}', 'error')
        return redirect(url_for('orders.orders'))

@bp.route('/api/update-status', methods=['POST'])
def update_order_status():
    """Update order status via API"""
    try:
        data = request.get_json()
        order_id = data.get('order_id')
        new_status = data.get('status')
        notes = data.get('notes', '')
        
        if not order_id or not new_status:
            return jsonify({'success': False, 'error': 'Order ID ve status gerekli'}), 400
        
        order_service = OrderManagementService()
        result = order_service.update_order_status_manually(
            order_id, new_status, notes, changed_by='user'
        )
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/bulk-update', methods=['POST'])
def bulk_update_orders():
    """Bulk update order statuses"""
    try:
        data = request.get_json()
        order_ids = data.get('order_ids', [])
        new_status = data.get('status')
        notes = data.get('notes', '')
        
        if not order_ids or not new_status:
            return jsonify({'success': False, 'error': 'Order ID\'leri ve status gerekli'}), 400
        
        order_service = OrderManagementService()
        success_count = 0
        error_count = 0
        
        for order_id in order_ids:
            try:
                result = order_service.update_order_status_manually(
                    order_id, new_status, notes, changed_by='user'
                )
                if result.get('success'):
                    success_count += 1
                else:
                    error_count += 1
            except:
                error_count += 1
        
        return jsonify({
            'success': True,
            'message': f'{success_count} sipariş güncellendi, {error_count} hata',
            'success_count': success_count,
            'error_count': error_count
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/sync-status')
def get_sync_status():
    """Get order sync status and statistics"""
    try:
        order_service = OrderManagementService()
        stats = order_service.get_order_statistics()
        
        return jsonify({
            'success': True,
            'stats': stats
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/analytics')
def analytics():
    """Order analytics page"""
    order_service = OrderManagementService()
    stats = order_service.get_order_statistics()
    
    # Get recent orders for chart
    conn = get_db_connection()
    recent_orders = conn.execute('''
        SELECT DATE(created_at) as date, COUNT(*) as count, SUM(total_amount) as revenue
        FROM orders
        WHERE created_at >= datetime('now', '-30 days')
        GROUP BY DATE(created_at)
        ORDER BY date DESC
    ''').fetchall()
    conn.close()
    
    return render_template('orders/analytics.html',
                         stats=stats,
                         recent_orders=[dict(row) for row in recent_orders])