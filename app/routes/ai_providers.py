from flask import Blueprint, render_template, request, jsonify, redirect, url_for, flash
from app.services.ai_provider_service import AIProviderService

bp = Blueprint('ai_providers', __name__, url_prefix='/ai-providers')

@bp.route('/')
def index():
    """List all AI providers"""
    service = AIProviderService()
    providers = service.get_all_providers()
    
    # Get predefined providers for reference
    default_providers = service.get_default_providers()
    
    return render_template('ai_providers/index.html', 
                         providers=providers,
                         default_providers=default_providers)

@bp.route('/add', methods=['GET', 'POST'])
def add():
    """Add a new AI provider"""
    service = AIProviderService()
    
    if request.method == 'POST':
        try:
            name = request.form['name']
            base_url = request.form['base_url']
            api_key = request.form.get('api_key', '').strip()
            model = request.form.get('model', '').strip()
            is_free = 'is_free' in request.form
            rate_limit = int(request.form.get('rate_limit', 100))
            rate_limit_window = int(request.form.get('rate_limit_window', 3600))
            
            # Don't store empty strings as api_key
            if not api_key:
                api_key = None
            
            provider_id = service.create_provider(
                name=name,
                base_url=base_url,
                api_key=api_key,
                model=model,
                is_free=is_free,
                rate_limit=rate_limit,
                rate_limit_window=rate_limit_window
            )
            
            flash('AI provider başarıyla eklendi!', 'success')
            return redirect(url_for('ai_providers.index'))
            
        except Exception as e:
            flash(f'Hata oluştu: {str(e)}', 'error')
    
    # Get default providers for dropdown
    default_providers = service.get_default_providers()
    
    return render_template('ai_providers/form.html', 
                         action='add',
                         default_providers=default_providers)

@bp.route('/<int:provider_id>/edit', methods=['GET', 'POST'])
def edit(provider_id):
    """Edit an existing AI provider"""
    service = AIProviderService()
    provider = service.get_provider_by_id(provider_id)
    
    if not provider:
        flash('AI provider bulunamadı!', 'error')
        return redirect(url_for('ai_providers.index'))
    
    if request.method == 'POST':
        try:
            name = request.form['name']
            base_url = request.form['base_url']
            api_key = request.form.get('api_key', '').strip()
            model = request.form.get('model', '').strip()
            is_free = 'is_free' in request.form
            rate_limit = int(request.form.get('rate_limit', 100))
            rate_limit_window = int(request.form.get('rate_limit_window', 3600))
            
            # If api_key field is empty or contains placeholder, don't update it
            if not api_key or api_key.startswith('•'):
                api_key = None
            
            service.update_provider(
                provider_id=provider_id,
                name=name,
                base_url=base_url,
                api_key=api_key,
                model=model,
                is_free=is_free,
                rate_limit=rate_limit,
                rate_limit_window=rate_limit_window
            )
            
            flash('AI provider başarıyla güncellendi!', 'success')
            return redirect(url_for('ai_providers.index'))
            
        except Exception as e:
            flash(f'Hata oluştu: {str(e)}', 'error')
    
    # Mask API key for display
    if provider['api_key']:
        provider['api_key_masked'] = '•' * 20
    
    # Get default providers for reference
    default_providers = service.get_default_providers()
    
    return render_template('ai_providers/form.html', 
                         action='edit',
                         provider=provider,
                         default_providers=default_providers)

@bp.route('/<int:provider_id>/toggle', methods=['POST'])
def toggle(provider_id):
    """Toggle AI provider active status"""
    try:
        service = AIProviderService()
        success = service.toggle_provider_status(provider_id)
        
        if success:
            return jsonify({'status': 'success', 'message': 'Durum güncellendi'})
        else:
            return jsonify({'status': 'error', 'message': 'Provider bulunamadı'}), 404
            
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@bp.route('/<int:provider_id>/delete', methods=['POST'])
def delete(provider_id):
    """Delete an AI provider"""
    try:
        service = AIProviderService()
        success = service.delete_provider(provider_id)
        
        if success:
            return jsonify({'status': 'success', 'message': 'AI provider silindi'})
        else:
            return jsonify({'status': 'error', 'message': 'Provider bulunamadı'}), 404
            
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500