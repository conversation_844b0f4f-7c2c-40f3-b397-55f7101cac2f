from flask import Blueprint, request, jsonify
from app.database import get_db_connection
from app.services.marketplace.ozon_api_client import OzonAPIClient
import json
from datetime import datetime

bp = Blueprint('marketplace_upload', __name__, url_prefix='/api/marketplace')

@bp.route('/upload', methods=['POST'])
def upload_product():
    """Ürünü marketplace'e yükle"""
    try:
        product_id = int(request.form.get('product_id'))
        marketplace_id = int(request.form.get('marketplace_id'))
        price = float(request.form.get('price'))
        vat = float(request.form.get('vat'))
        offer_id = request.form.get('offer_id')
        category_id = int(request.form.get('category_id'))
        description = request.form.get('description')
        weight = int(request.form.get('weight', 0))
        
        # Veritabanından ürün bilgilerini al
        conn = get_db_connection()
        
        product = conn.execute('''
            SELECT * FROM products WHERE id = ?
        ''', (product_id,)).fetchone()
        
        if not product:
            conn.close()
            return jsonify({'status': 'error', 'message': 'Ürün bulunamadı'}), 404
        
        marketplace = conn.execute('''
            SELECT * FROM api_sites WHERE id = ?
        ''', (marketplace_id,)).fetchone()
        
        if not marketplace:
            conn.close()
            return jsonify({'status': 'error', 'message': 'Marketplace bulunamadı'}), 404
        
        # Upload log oluştur
        cursor = conn.cursor()
        cursor.execute('''
            INSERT INTO marketplace_upload_logs 
            (product_id, marketplace_id, status, request_data)
            VALUES (?, ?, ?, ?)
        ''', (product_id, marketplace_id, 'pending', json.dumps({
            'price': price,
            'vat': vat,
            'offer_id': offer_id,
            'category_id': category_id,
            'description': description,
            'weight': weight
        })))
        
        log_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        # Marketplace'e göre uygun client'ı seç
        if marketplace['name'] == 'Ozon':
            result = upload_to_ozon(product, {
                'price': price,
                'vat': vat,
                'offer_id': offer_id,
                'category_id': category_id,
                'description': description,
                'weight': weight
            }, log_id)
        else:
            update_upload_log(log_id, 'failed', error_message=f'{marketplace["name"]} henüz desteklenmiyor')
            return jsonify({'status': 'error', 'message': f'{marketplace["name"]} henüz desteklenmiyor'}), 400
        
        if result:
            return jsonify({'status': 'success', 'message': 'Ürün yükleme başlatıldı', 'log_id': log_id})
        else:
            return jsonify({'status': 'error', 'message': 'Ürün yükleme başarısız'}), 500
            
    except ValueError as e:
        return jsonify({'status': 'error', 'message': f'Geçersiz veri: {str(e)}'}), 400
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

def upload_to_ozon(product, upload_data, log_id):
    """Ürünü Ozon'a yükle"""
    try:
        # Upload log durumunu güncelle
        update_upload_log(log_id, 'uploading')
        
        ozon_client = OzonAPIClient()
        
        # Ozon formatında ürün verisi hazırla
        product_data = {
            'name': product['product_name'],
            'category_id': upload_data['category_id'],
            'offer_id': upload_data['offer_id'],
            'price': upload_data['price'],
            'vat': upload_data['vat'],
            'description': upload_data['description'],
            'barcode': product['barcode'] or '',
            'weight': upload_data['weight'],
            'currency_code': 'USD',
            'weight_unit': 'g',
            'dimension_unit': 'cm'
        }
        
        # Ürünü Ozon'a gönder
        task_id = ozon_client.create_product(product_data)
        
        if task_id:
            # Başarılı - task_id'yi kaydet
            update_upload_log(log_id, 'success', 
                            marketplace_product_id=task_id,
                            response_data=json.dumps({'task_id': task_id}))
            return True
        else:
            # Başarısız
            update_upload_log(log_id, 'failed', error_message='Ozon API yükleme başarısız')
            return False
            
    except Exception as e:
        update_upload_log(log_id, 'failed', error_message=str(e))
        return False

def update_upload_log(log_id, status, marketplace_product_id=None, response_data=None, error_message=None):
    """Upload log'u güncelle"""
    conn = get_db_connection()
    cursor = conn.cursor()
    
    cursor.execute('''
        UPDATE marketplace_upload_logs 
        SET status = ?, marketplace_product_id = ?, response_data = ?, error_message = ?, updated_at = ?
        WHERE id = ?
    ''', (status, marketplace_product_id, response_data, error_message, datetime.now(), log_id))
    
    conn.commit()
    conn.close()

@bp.route('/upload-status/<int:log_id>')
def get_upload_status(log_id):
    """Upload durumunu kontrol et"""
    try:
        conn = get_db_connection()
        
        log = conn.execute('''
            SELECT ul.*, p.product_name, m.name as marketplace_name
            FROM marketplace_upload_logs ul
            JOIN products p ON ul.product_id = p.id
            JOIN api_sites m ON ul.marketplace_id = m.id
            WHERE ul.id = ?
        ''', (log_id,)).fetchone()
        
        conn.close()
        
        if not log:
            return jsonify({'status': 'error', 'message': 'Log bulunamadı'}), 404
        
        return jsonify({
            'status': 'success',
            'data': {
                'id': log['id'],
                'product_name': log['product_name'],
                'marketplace_name': log['marketplace_name'],
                'status': log['status'],
                'marketplace_product_id': log['marketplace_product_id'],
                'error_message': log['error_message'],
                'created_at': log['created_at'],
                'updated_at': log['updated_at']
            }
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@bp.route('/uploads')
def list_uploads():
    """Tüm upload loglarını listele"""
    try:
        conn = get_db_connection()
        
        uploads = conn.execute('''
            SELECT ul.*, p.product_name, p.sku, m.name as marketplace_name
            FROM marketplace_upload_logs ul
            JOIN products p ON ul.product_id = p.id
            JOIN api_sites m ON ul.marketplace_id = m.id
            ORDER BY ul.created_at DESC
            LIMIT 100
        ''').fetchall()
        
        conn.close()
        
        upload_list = []
        for upload in uploads:
            upload_list.append({
                'id': upload['id'],
                'product_name': upload['product_name'],
                'sku': upload['sku'],
                'marketplace_name': upload['marketplace_name'],
                'status': upload['status'],
                'marketplace_product_id': upload['marketplace_product_id'],
                'error_message': upload['error_message'],
                'created_at': upload['created_at'],
                'updated_at': upload['updated_at']
            })
        
        return jsonify({
            'status': 'success',
            'data': upload_list
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500