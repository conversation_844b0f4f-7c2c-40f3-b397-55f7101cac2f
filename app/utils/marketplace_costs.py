"""
Marketplace cost calculation utilities
"""

from app.database import get_db_connection
from app.routes.settings import get_setting_value, format_currency

def get_marketplace_costs(marketplace_id):
    """Get additional costs for a specific marketplace"""
    conn = get_db_connection()
    result = conn.execute('''
        SELECT additional_shipping_cost, additional_commission_rate
        FROM api_sites
        WHERE id = ?
    ''', (marketplace_id,)).fetchone()
    conn.close()
    
    if result:
        return {
            'shipping_cost': float(result['additional_shipping_cost'] or 0),
            'commission_rate': float(result['additional_commission_rate'] or 0)
        }
    return {
        'shipping_cost': 0,
        'commission_rate': 0
    }

def calculate_total_commission(base_price, commission_rate, marketplace_id):
    """Calculate total commission including marketplace additional commission"""
    marketplace_costs = get_marketplace_costs(marketplace_id)
    total_commission_rate = commission_rate + marketplace_costs['commission_rate']
    return base_price * (total_commission_rate / 100)

def calculate_total_cost(base_price, marketplace_id):
    """Calculate total cost including marketplace shipping"""
    marketplace_costs = get_marketplace_costs(marketplace_id)
    return base_price + marketplace_costs['shipping_cost']

def get_marketplace_summary(marketplace_id):
    """Get formatted marketplace cost summary"""
    costs = get_marketplace_costs(marketplace_id)
    
    summary = []
    if costs['shipping_cost'] > 0:
        summary.append(f"Ek Kargo: {format_currency(costs['shipping_cost'])}")
    if costs['commission_rate'] > 0:
        summary.append(f"Ek Komisyon: %{costs['commission_rate']}")
    
    return " | ".join(summary) if summary else "Ek maliyet yok"