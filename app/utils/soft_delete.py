"""
Soft Delete Utility Functions
Provides reusable functions for soft delete operations
"""

from datetime import datetime
from flask import current_app
from app.database import get_db_connection

class SoftDeleteMixin:
    """Mixin class for soft delete functionality"""
    
    @staticmethod
    def add_soft_delete_filter(query, table_alias="ps"):
        """Add soft delete filter to existing query"""
        if "WHERE" in query.upper():
            return query + f" AND {table_alias}.is_deleted = 0"
        else:
            return query + f" WHERE {table_alias}.is_deleted = 0"
    
    @staticmethod
    def soft_delete_product_supplier(product_id, supplier_id, deleted_by=None):
        """Soft delete a product-supplier relationship"""
        conn = get_db_connection()
        try:
            cursor = conn.execute('''
                UPDATE product_suppliers 
                SET is_deleted = 1, 
                    deleted_at = CURRENT_TIMESTAMP,
                    deleted_by = ?
                WHERE product_id = ? AND supplier_id = ? AND is_deleted = 0
            ''', (deleted_by, product_id, supplier_id))
            
            if cursor.rowcount == 0:
                return False, "<PERSON><PERSON>şki bulunamadı veya zaten silinmiş"
            
            conn.commit()
            return True, "İlişki başarıyla silindi"
        except Exception as e:
            conn.rollback()
            return False, f"Silme işlemi başarısız: {str(e)}"
        finally:
            conn.close()
    
    @staticmethod
    def restore_product_supplier(product_id, supplier_id):
        """Restore a soft-deleted product-supplier relationship"""
        conn = get_db_connection()
        try:
            cursor = conn.execute('''
                UPDATE product_suppliers 
                SET is_deleted = 0, 
                    deleted_at = NULL,
                    deleted_by = NULL
                WHERE product_id = ? AND supplier_id = ? AND is_deleted = 1
            ''', (product_id, supplier_id))
            
            if cursor.rowcount == 0:
                return False, "Silinen ilişki bulunamadı"
            
            conn.commit()
            return True, "İlişki başarıyla geri yüklendi"
        except Exception as e:
            conn.rollback()
            return False, f"Geri yükleme işlemi başarısız: {str(e)}"
        finally:
            conn.close()
    
    @staticmethod
    def get_deleted_product_suppliers():
        """Get all soft-deleted product-supplier relationships"""
        conn = get_db_connection()
        try:
            deleted_relationships = conn.execute('''
                SELECT ps.id, ps.product_id, ps.supplier_id, ps.supplier_product_url, 
                       ps.supplier_price, ps.deleted_at, ps.deleted_by,
                       p.product_name, p.sku,
                       s.name as supplier_name, s.website as supplier_website
                FROM product_suppliers ps
                JOIN products p ON ps.product_id = p.id
                JOIN suppliers s ON ps.supplier_id = s.id
                WHERE ps.is_deleted = 1
                ORDER BY ps.deleted_at DESC
            ''').fetchall()
            
            return [dict(row) for row in deleted_relationships]
        finally:
            conn.close()
    
    @staticmethod
    def hard_delete_product_supplier(product_id, supplier_id):
        """Permanently delete a product-supplier relationship (use with caution)"""
        conn = get_db_connection()
        try:
            cursor = conn.execute('''
                DELETE FROM product_suppliers 
                WHERE product_id = ? AND supplier_id = ?
            ''', (product_id, supplier_id))
            
            if cursor.rowcount == 0:
                return False, "İlişki bulunamadı"
            
            conn.commit()
            return True, "İlişki kalıcı olarak silindi"
        except Exception as e:
            conn.rollback()
            return False, f"Kalıcı silme işlemi başarısız: {str(e)}"
        finally:
            conn.close()

# Convenience functions
def soft_delete_product_supplier(product_id, supplier_id, deleted_by=None):
    return SoftDeleteMixin.soft_delete_product_supplier(product_id, supplier_id, deleted_by)

def restore_product_supplier(product_id, supplier_id):
    return SoftDeleteMixin.restore_product_supplier(product_id, supplier_id)

def get_deleted_product_suppliers():
    return SoftDeleteMixin.get_deleted_product_suppliers()

def add_soft_delete_filter(query, table_alias="ps"):
    return SoftDeleteMixin.add_soft_delete_filter(query, table_alias)