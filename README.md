# PazarYeri - Marketplace Integration Platform

Profesyonel yapıda çoklu marketplace entegrasyonu ile ürün ve stok yönetim sistemi.

## 🚀 Özellikler

- 📦 **Ürün Yönetimi**: SKU, barkod, marka, model bazlı ürün takibi
- 🏢 **Çoklu Depo**: Farklı lokasyonlarda stok yönetimi
- 🌐 **Marketplace Entegrasyonu**: Amazon, Ozon ve diğer platformlarla senkronizasyon
- 💰 **Fiyat Takibi**: Tedarikçi fiyatları ve marketplace fiyat geçmişi
- 📊 **Kar Marjı Hesaplama**: Komisyon ve kar oranları yönetimi
- 🔄 **Otomatik Senkronizasyon**: Marketplace ürünlerini otomatik çekme
- 🔗 **Ürün Eşleştirme**: Yerel ürünler ile marketplace ürünlerini eşleştirme
- 🏗️ **Modern Mimari**: Flask Blueprint yapısı ile modüler tasarım

## 📁 Proje <PERSON>

```
PazarYeri/
├── app/                     # Ana uygulama paketi
│   ├── __init__.py         # Flask app factory
│   ├── config.py           # Konfigürasyon ayarları
│   ├── database.py         # Veritabanı yardımcıları
│   ├── models/             # Veritabanı modelleri
│   ├── routes/             # Route blueprint'leri
│   │   ├── main.py         # Ana sayfalar
│   │   ├── products.py     # Ürün işlemleri
│   │   ├── api.py          # API endpoint'leri
│   │   └── marketplace.py  # Marketplace işlemleri
│   ├── services/           # İş mantığı servisleri
│   │   ├── api_manager.py
│   │   ├── supplier_finder.py
│   │   ├── product_sync_service.py
│   │   └── marketplace/    # Marketplace istemcileri
│   ├── static/             # Statik dosyalar
│   └── templates/          # HTML şablonları
├── tests/                  # Test dosyaları
├── migrations/             # Veritabanı migration'ları
├── logs/                   # Uygulama logları
├── docs/                   # Dokümantasyon
└── run.py                 # Uygulama başlatıcı
```

## 🛠️ Kurulum

```bash
# Repository'yi klonla
git clone https://github.com/zekigalip/pazaryeri.git
cd pazaryeri

# Virtual environment oluştur
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Gerekli paketleri yükle
pip install -r requirements.txt

# Environment değişkenlerini ayarla
cp .env.example .env
# .env dosyasını düzenle

# Uygulamayı başlat
python run.py
```

## Kullanım

1. Uygulama başladıktan sonra http://localhost:5002 adresine gidin
2. İlk olarak API Yönetimi sayfasından marketplace API bilgilerinizi girin
3. Ürünler sayfasından yeni ürün ekleyin
4. Marketplace Sync ile ürünleri senkronize edin
5. Ürün Eşleştirmeleri ile yerel ürünlerinizi marketplace ürünleriyle eşleştirin

## Teknolojiler

- **Backend**: Python, Flask
- **Veritabanı**: SQLite
- **Frontend**: Bootstrap 5
- **API Entegrasyonları**: Amazon SP-API, Ozon Seller API

## Veritabanı Yapısı

- `users` - Kullanıcı yönetimi
- `warehouses` - Depo tanımları
- `products` - Ana ürün bilgileri
- `suppliers` - Tedarikçi bilgileri
- `inventory` - Stok durumu
- `marketplace_products` - Marketplace ürünleri
- `product_mappings` - Ürün eşleştirmeleri
- `api_sites` - API yapılandırmaları

## API Entegrasyonu

### Ozon
- Client ID ve API Key ile kimlik doğrulama
- Ürün listesi çekme
- Ürün detayları sorgulama

### Amazon
- SP-API entegrasyonu (geliştirme aşamasında)
- OAuth 2.0 kimlik doğrulama

## Güvenlik

- API anahtarları şifrelenerek saklanır
- Hassas bilgiler .gitignore ile korunur

## Lisans

MIT

## İletişim

Sorularınız için issue açabilirsiniz.