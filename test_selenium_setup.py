#!/usr/bin/env python3
"""
Test script to verify Selenium setup
"""

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_selenium():
    print("Testing Selenium setup...")
    print("-" * 50)
    
    # Check imports
    try:
        import selenium
        print(f"✓ Selenium version: {selenium.__version__}")
    except ImportError as e:
        print(f"✗ Selenium import failed: {e}")
        return False
    
    try:
        import webdriver_manager
        print(f"✓ Webdriver-manager installed")
    except ImportError as e:
        print(f"✗ Webdriver-manager import failed: {e}")
        return False
    
    # Test Chrome driver
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        
        print("\nInitializing Chrome driver...")
        
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        
        # Use system ChromeDriver
        import platform
        driver_path = None
        
        if platform.system() == 'Darwin':  # macOS
            chromedriver_paths = [
                '/opt/homebrew/bin/chromedriver',
                '/usr/local/bin/chromedriver',
                '/usr/bin/chromedriver'
            ]
            for path in chromedriver_paths:
                if os.path.exists(path):
                    driver_path = path
                    print(f"Using system ChromeDriver: {path}")
                    break
        
        if not driver_path:
            print("Using WebDriverManager...")
            driver_path = ChromeDriverManager().install()
            
        service = Service(driver_path)
        driver = webdriver.Chrome(service=service, options=options)
        
        print("✓ Chrome driver initialized successfully")
        
        # Test navigation
        print("\nTesting page navigation...")
        driver.get("https://www.google.com")
        print(f"✓ Successfully loaded: {driver.title}")
        
        driver.quit()
        print("✓ Driver closed successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Chrome driver test failed: {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trendyol_extraction():
    print("\n" + "-" * 50)
    print("Testing Trendyol extraction with Selenium...")
    
    try:
        from app.services.selenium_helper import SeleniumHelper
        
        helper = SeleniumHelper()
        url = "https://www.trendyol.com/tp-link/archer-c80-4-port-gigabit-ac1900-kablosuz-mu-mimo-wi-fi-router-p-45585462"
        
        print(f"Testing URL: {url}")
        print("Extracting description (this may take 10-15 seconds)...")
        
        description = helper.extract_trendyol_description(url)
        
        if description:
            print(f"✓ Description found! Length: {len(description)} characters")
            print(f"First 200 chars: {description[:200]}...")
        else:
            print("✗ No description found")
            
        helper.close()
        return description is not None
        
    except Exception as e:
        print(f"✗ Trendyol extraction test failed: {type(e).__name__}: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("SELENIUM SETUP TEST")
    print("=" * 50)
    
    # Basic Selenium test
    selenium_ok = test_selenium()
    
    if selenium_ok:
        # Test Trendyol extraction
        trendyol_ok = test_trendyol_extraction()
        
        print("\n" + "=" * 50)
        if trendyol_ok:
            print("✓ ALL TESTS PASSED! Selenium is working correctly.")
        else:
            print("✗ Trendyol extraction failed. Check the error messages above.")
    else:
        print("\n" + "=" * 50)
        print("✗ Basic Selenium test failed. Please check:")
        print("1. Chrome/Chromium is installed")
        print("2. You have proper permissions")
        print("3. No firewall/proxy blocking Chrome")